/* eslint-disable react-hooks/exhaustive-deps */
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { BlockStack, Box } from '@shopify/polaris';
import { SaveBar } from '@shopify/app-bridge-react';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { FormField, useFormField } from './hooks/useFormField';
import { PlanStatus } from '../types';
import { AccountStatusCard } from './components/AccountStatus';
import { EmailSettingsCard } from './components/EmailSettingsCard';
import { StorefrontPasswordCard } from './components/StorefrontPasswordCard';
import { LanguageCard } from './components/LanguageCard';
import { BillTable } from './components/BillTable';
import { SkeletonAccountSetting } from '../skeleton/SkeletonAccountSetting';

interface FormFields {
    email: FormField<string>;
    emailNotifications: FormField<string[]>;
    language: FormField<string>;
    storefrontPassword: FormField<string>;
}

export const AccountSettings: FC = () => {
    const { appSettings, isAuthenticated, apiStatus, getAppSettings, updateAppSettings } = useAppStore();
    const [shopifyDomain, setShopifyDomain] = useState('');
    const [currentPlanStatus, setCurrentPlanStatus] = useState('free');
    const [currentPlan, setCurrentPlan] = useState<string>('');

    const formFields: FormFields = {
        email: useFormField<string>(appSettings?.accountEmail?.emailSubscribed || ''),
        emailNotifications: useFormField<string[]>(
            appSettings?.accountEmail?.isSubscribed ? ['subscribe'] : ['unsubscribe'],
        ),
        language: useFormField<string>(appSettings?.language || ''),
        storefrontPassword: useFormField<string>(appSettings?.storefrontPassword || ''),
    };

    useEffect(() => {
        if (isAuthenticated) {
            getAppSettings();
        }
    }, [isAuthenticated, getAppSettings]);

    useEffect(() => {
        if (appSettings) {
            const { accountEmail, language, storefrontPassword, accountStatus } = appSettings;
            formFields.email.setInitialValue(accountEmail?.emailSubscribed);
            formFields.emailNotifications.setInitialValue(accountEmail?.isSubscribed ? ['subscribe'] : ['unsubscribe']);
            formFields.language.setInitialValue(language);
            formFields.storefrontPassword.setInitialValue(storefrontPassword);
            setShopifyDomain(accountStatus?.shopifyDomain);
            setCurrentPlanStatus(accountStatus?.plan?.current);

            const planItem = accountStatus?.plan?.list?.find((item) => item.key === accountStatus?.plan?.current);
            setCurrentPlan(planItem?.value || 'Autoketing Builder Free');
        }
    }, [appSettings]);

    const hasChanges = useMemo(
        () => Object.values(formFields).some((field) => field.hasChanges),
        [
            formFields.email.hasChanges,
            formFields.emailNotifications.hasChanges,
            formFields.language.hasChanges,
            formFields.storefrontPassword.hasChanges,
        ],
    );

    useEffect(() => {
        if (hasChanges) {
            shopify.saveBar.show('account-settings-save-bar');
        } else {
            shopify.saveBar.hide('account-settings-save-bar');
        }
    }, [hasChanges]);

    const validateEmail = (value: string) => {
        if (!value) {
            return 'Email is required';
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            return 'Invalid email address';
        }
        return false;
    };

    const handleSave = useCallback(async () => {
        if (validateEmail(formFields.email.value)) {
            return;
        }
        const request = {
            shopifyDomain: shopifyDomain ?? '',
            emailSubscribed: formFields.email.value,
            isSubscribe: formFields.emailNotifications.value.includes('subscribe') ? 1 : 0,
            storefrontPassword: formFields.storefrontPassword.value,
            language: formFields.language.value,
            plan: currentPlanStatus,
        };
        try {
            await updateAppSettings(request);
            shopify.toast.show('App settings updated successfully', {
                duration: 1500,
                isError: false,
            });
        } catch (error) {
            shopify.toast.show('Failed to update app settings', {
                duration: 1500,
                isError: true,
            });
            throw error;
        }
    }, [formFields, shopifyDomain, currentPlanStatus]);

    const handleDiscard = useCallback(() => {
        Object.values(formFields).forEach((field) => field.reset());
    }, [formFields]);

    return (
        <Box paddingBlockEnd="500">
            {apiStatus === 'loading' && <SkeletonAccountSetting />}
            <div className={`account-settings ${apiStatus === 'success' ? 'account-settings--success' : ''}`}>
                <SaveBar id="account-settings-save-bar">
                    <button loading={apiStatus === 'loading' ? '' : undefined} variant="primary" onClick={handleSave}>
                        Save
                    </button>
                    <button onClick={handleDiscard}>Discard</button>
                </SaveBar>

                <BlockStack gap="500">
                    <AccountStatusCard
                        shopifyDomain={shopifyDomain}
                        accountStatus={currentPlanStatus as PlanStatus}
                        accountPlanName={currentPlan}
                    />
                    <EmailSettingsCard
                        emailField={formFields.email as FormField<string>}
                        emailNotificationsField={formFields.emailNotifications as FormField<string[]>}
                        validateEmail={validateEmail}
                    />
                    <BillTable />
                    <StorefrontPasswordCard
                        storefrontPasswordField={formFields.storefrontPassword as FormField<string>}
                    />
                    <LanguageCard languageField={formFields.language as FormField<string>} />
                </BlockStack>
            </div>
        </Box>
    );
};
