.radius-setting {
    &__diagram {
        width: 7.125rem;
        height: 5rem;
        margin-inline: auto;

        &-row {
            display: flex;
            justify-content: space-between;

            &--center {
                justify-content: center;
            }

            &--top-left,
            &--top-right,
            &--bottom-left,
            &--bottom-right {
                width: 43px;
                height: 25px;
                border-style: solid;
                border-color: #8a8a8a;
                border-width: 0;
                border-radius: 0;
            }

            &--top-left {
                border-top-width: 1.5px;
                border-left-width: 1.5px;
            }

            &--top-right {
                border-top-width: 1.5px;
                border-right-width: 1.5px;
            }

            &--bottom-left {
                border-bottom-width: 1.5px;
                border-left-width: 1.5px;
            }

            &--bottom-right {
                border-bottom-width: 1.5px;
                border-right-width: 1.5px;
            }
        }
    }

    &__button {
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        cursor: pointer;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        &--lock {
            width: 1.5rem;
            height: 1.5rem;
            background-color: #8a8a8a;
            border-radius: 5px;
        }
    }
}
