import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BlockStack } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import OverviewStates from './components/OverviewStates';
interface StylesProps {
    id: string;
}

export const States: FC<StylesProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="States"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <OverviewStates id={id} />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
