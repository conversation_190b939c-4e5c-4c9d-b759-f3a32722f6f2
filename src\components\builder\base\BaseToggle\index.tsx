import { FC, useMemo } from 'react';
import clsx from 'clsx';
import { useUncontrolled } from '@/hooks';
import './style.scss';

export interface BaseToggleProps {
    valueData?: boolean;
    onChange?: (value: boolean) => void;
    defaultValue?: boolean;
    containerClassName?: string;
    label?: string;
    labelHidden?: boolean;
    id?: string;
}

export const BaseToggle: FC<BaseToggleProps> = ({
    valueData,
    onChange,
    defaultValue = false,
    containerClassName,
    label,
    labelHidden,
    id,
}) => {
    const switchId = useMemo(() => id || `switch-${Math.random().toString(36).substring(2, 15)}`, [id]);

    const [active, setActive] = useUncontrolled({
        value: valueData,
        onChange,
        defaultValue,
    });

    return (
        <div className={clsx('switch', containerClassName)} style={{ width: labelHidden ? 'auto' : '100%' }}>
            {!labelHidden && <div className="switch__label">{label}</div>}
            <div className="switch__control">
                <input checked={active} type="checkbox" className="switch__checkbox--input" id={switchId} readOnly />
                <label className="switch__label_checkbox" onClick={() => setActive(!active)}></label>
            </div>
        </div>
    );
};
