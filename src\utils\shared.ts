/* eslint-disable @typescript-eslint/no-explicit-any */
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import deepmerge from 'deepmerge';
import * as objectPath from 'object-path-immutable';

export const getBlockProperty = (path: string, blockId?: string) => {
    const blocks = useBlockStore.getState().blocks;
    const selectedBlockId = useBuilderStore.getState().selectedBlockId;
    const block = blocks[blockId ?? (selectedBlockId as string)];
    return objectPath.get(block, path);
};

export const getBlockBPProperty = (path: string, blockId?: string) => {
    const blocks = useBlockStore.getState().blocks;
    const selectedBlockId = useBuilderStore.getState().selectedBlockId;
    const currentDevice = useBuilderStore.getState().currentDevice;
    const block = blocks[blockId ?? (selectedBlockId as string)];
    const desktopValue = objectPath.get(block?.bpConfigs?.desktop, path);
    const mobileValue = objectPath.get(block?.bpConfigs?.mobile, path);
    if (currentDevice === 'mobile') {
        if (mobileValue === undefined) {
            return desktopValue;
        }
        if (typeof desktopValue === 'object' && desktopValue !== null) {
            return deepmerge(desktopValue, mobileValue, {
                arrayMerge: (_, sourceArray) => {
                    return sourceArray;
                },
            });
        }
        return mobileValue;
    }
    return desktopValue;
};

export const getNestedProperty = (obj: any, key: string): any => {
    const keyArray: string[] = key.split('.');

    return keyArray.reduce((acc, keyItem) => {
        return acc && acc[keyItem];
    }, obj);
};

export const setNestedProperty = (obj: any, key: string, value: any): void => {
    const keyArray: string[] = key.split('.');
    const lastKey: string = keyArray.pop() as string;

    const nestedObj = keyArray.reduce((acc, keyItem, index) => {
        if (!acc[keyItem]) {
            if (index < keyArray.length - 1) {
                acc[keyItem] = isNaN(parseInt(keyArray[index + 1], 10)) ? {} : [];
            } else {
                if (index === keyArray.length - 1) {
                    if (isNaN(parseInt(lastKey, 10))) {
                        acc[keyItem] = {};
                    } else {
                        acc[keyItem] = [];
                    }
                }
            }
        }
        return acc[keyItem];
    }, obj);

    if (!isNaN(parseInt(lastKey, 10)) && Array.isArray(nestedObj)) {
        nestedObj[parseInt(lastKey, 10)] = value;
    } else {
        nestedObj[lastKey] = value;
    }
};
