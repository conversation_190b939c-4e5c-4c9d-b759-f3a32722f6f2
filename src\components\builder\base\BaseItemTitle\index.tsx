import { FC } from 'react';
import { InlineStack, Text, TextProps, Tooltip } from '@shopify/polaris';
import clsx from 'clsx';

export interface BaseItemTitleProps extends TextProps {
    containerClassName?: string;
    tooltipContent?: string;
    hasTooltip?: boolean;
    tooltipChildren?: React.ReactNode;
}

export const BaseItemTitle: FC<BaseItemTitleProps> = ({
    containerClassName,
    tooltipContent,
    hasTooltip = false,
    tooltipChildren,
    ...textProps
}) => {
    return (
        <div className={clsx('base-item-title', containerClassName)}>
            <InlineStack gap="100" align="center" blockAlign="center">
                <Text {...textProps} />
                {hasTooltip && (
                    <Tooltip content={tooltipContent} dismissOnMouseOut>
                        {tooltipChildren}
                    </Tooltip>
                )}
            </InlineStack>
        </div>
    );
};
