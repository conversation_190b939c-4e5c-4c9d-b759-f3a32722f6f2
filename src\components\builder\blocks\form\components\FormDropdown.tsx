/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import {
    DATA_SET_FORM_DROPDOWN_ELEMENT,
    DATA_SET_FORM_INPUT,
    DATA_SET_FORM_FIELD_ID,
} from '@/components/builder/blocks/form/constants';
interface FormDropdownProps {
    field: any;
    index: number;
    autoId: string;
}

export const FormDropdown: FC<FormDropdownProps> = ({ field, index, autoId }) => {
    return (
        <select
            {...{ [`${DATA_SET_FORM_DROPDOWN_ELEMENT}-${index}`]: autoId }}
            {...{ [`${DATA_SET_FORM_INPUT}`]: autoId }}
            {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
            onChange={() => {}}
            css={{
                appearance: 'none !important' as any,
                backgroundImage: 'url(https://cdn-master.shopone.io/master-icons/svgFree/chevron-up-1.svg) !important',
                backgroundSize: '16px !important',
                backgroundRepeat: 'no-repeat !important',
                backgroundPosition: 'calc(100% - 16px) center !important',
                height: 'var(--form-field-' + field.key + '-height)',
            }}
            value={field.showInitialText === 'default' ? field.initialText : undefined}
        >
            <option selected={field.showInitialText === 'placeholder'} disabled value="">
                {field.placeholder}
            </option>
            {field.options?.map((option: any, optionIndex: number) => (
                <option key={`${field.key}-option-${optionIndex}`} value={option.value}>
                    {option.label}
                </option>
            ))}
        </select>
    );
};
