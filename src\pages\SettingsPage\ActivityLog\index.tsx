import { FC, useEffect, useState } from 'react';
import { BlockStack, Card, DataTable, InlineStack, Pagination, Text } from '@shopify/polaris';
import { SkeletonActivityLog } from '../skeleton/SkeletonActivityLog';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { ActivityLogParams } from '../types';
import { ActivityLogs } from '@/stores/appStore/appSettings/types';
import './style.scss';

export const ActivityLog: FC = () => {
    const apiStatus = useAppStore((state) => state.apiStatus);
    const isAuthenticated = useAppStore((state) => state.isAuthenticated);
    const activityLogs: ActivityLogs[] = useAppStore((state) => state.activityLogs);
    const setActivityLogs = useAppStore((state) => state.setActivityLogs);
    const totalLogs = useAppStore((state) => state.totalLogs);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [activityLogsList, setActivityLogsList] = useState<ActivityLogs[]>([]);
    const pageSize = 5;

    useEffect(() => {
        if (!isAuthenticated) return;
        const params: ActivityLogParams = {
            currentPage: currentPage,
            perPage: pageSize,
        };
        setActivityLogs(params);
    }, [setActivityLogs, currentPage, pageSize, isAuthenticated]);
    useEffect(() => {
        if (activityLogs) {
            setActivityLogsList(activityLogs);
        }
    }, [activityLogs]);

    const rows = activityLogsList.map((log: ActivityLogs) => [
        <InlineStack gap="100" align="start">
            <Text as="p" truncate>
                {log.action.key}:{' '}
            </Text>
            <Text as="span" fontWeight="semibold" truncate>
                {Array.isArray(log.action.value) ? log.action.value.join(', ') : log.action.value}
            </Text>
        </InlineStack>,
        <BlockStack gap="200" align="start">
            <InlineStack gap="100" align="start" wrap={false}>
                <Text as="p">{log.ip.key}: </Text>
                <Text as="span" fontWeight="semibold">
                    {log.ip.value}
                </Text>
            </InlineStack>
            <InlineStack gap="100" align="start" wrap={false}>
                <Text as="p">{log.description.key}: </Text>
                <Text as="span" fontWeight="semibold" truncate>
                    {Array.isArray(log.description.value) ? log.description.value.join(', ') : log.description.value}
                </Text>
            </InlineStack>
        </BlockStack>,
        <Text as="span" fontWeight="semibold">
            {log.created_at}
        </Text>,
    ]);

    return apiStatus === 'loading' ? (
        <SkeletonActivityLog />
    ) : (
        <Card padding="400">
            <DataTable
                columnContentTypes={['text', 'text', 'text']}
                headings={['Action', 'User', 'Time']}
                rows={rows}
            />
            <div className="activity-log__pagination">
                <Pagination
                    hasNext={currentPage * pageSize < totalLogs}
                    hasPrevious={currentPage > 1}
                    onNext={() => setCurrentPage(currentPage + 1)}
                    onPrevious={() => setCurrentPage(currentPage - 1)}
                />
            </div>
        </Card>
    );
};
