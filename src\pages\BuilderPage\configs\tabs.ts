import { TabProps } from '../types/tabs';
import {
    TextTitleIcon,
    ButtonIcon,
    ImageIcon,
    SlideshowIcon,
    CodeIcon,
    FormsIcon,
    LayoutColumn1Icon,
    PlayIcon,
    CategoriesIcon,
    SearchIcon,
    DataTableIcon,
    MenuIcon,
    ProductListIcon,
    FilterIcon,
    PaginationEndIcon,
    CartIcon,
    ProductIcon,
    BlogIcon,
    TextWithImageIcon,
    TextQuoteIcon,
} from '@shopify/polaris-icons';

import CountdownIcon from '@/assets/svgs/count-down.svg?url';
import BreadcrumbsIcon from '@/assets/svgs/breadcrumb.svg?url';
import CounterIcon from '@/assets/svgs/counter.svg?url';
import TabsIcon from '@/assets/svgs/tabs.svg?url';

export const ConfigTabs: TabProps[] = [
    {
        name: 'Autoketing',
        id: 'autoketing',
        categories: [
            {
                name: 'Basic',
                id: 'basic',
                items: [
                    { icon: TextTitleIcon, title: 'Text', id: 'text', type: 'text' },
                    { icon: ButtonIcon, title: 'Button', id: 'button', type: 'button' },
                    { icon: TabsIcon, title: 'Tabs', id: 'tabs', type: 'tabs' },
                    { icon: FormsIcon, title: 'Form', id: 'form', type: 'form' },
                    { icon: LayoutColumn1Icon, title: 'Shape', id: 'shape', type: 'shape' },
                    { icon: CodeIcon, title: 'HTML code', id: 'html-code', type: 'html-code' },
                ],
            },
            {
                name: 'Media',
                id: 'media',
                items: [
                    { icon: PlayIcon, title: 'Video', id: 'video', type: 'video' },
                    { icon: ImageIcon, title: 'Image', id: 'image', type: 'image' },
                    { icon: CategoriesIcon, title: 'Gallery', id: 'gallery', type: 'gallery' },
                    { icon: SlideshowIcon, title: 'Slideshow', id: 'slideshow', type: 'slideshow' },
                ],
            },
            {
                name: 'Advanced',
                id: 'advanced',
                items: [
                    { icon: CountdownIcon, title: 'Countdown', id: 'countdown', type: 'countdown' },
                    { icon: SearchIcon, title: 'Search', id: 'search', type: 'search' },
                    { icon: DataTableIcon, title: 'Accordion', id: 'accordion', type: 'accordion' },
                    { icon: BreadcrumbsIcon, title: 'Breadcrumbs', id: 'breadcrumbs', type: 'breadcrumbs' },
                    { icon: CounterIcon, title: 'Counter', id: 'counter', type: 'counter' },
                    { icon: MenuIcon, title: 'Menu', id: 'menu', type: 'menu' },
                ],
            },
        ],
        searchPlaceholder: 'Search Autoketing...',
    },
    {
        name: 'Shopify',
        id: 'shopify',
        categories: [
            {
                name: 'Products',
                id: 'products',
                items: [
                    {
                        icon: ProductListIcon,
                        title: 'Product collections',
                        id: 'product-collections',
                        type: 'product-collections',
                    },
                    { icon: FilterIcon, title: 'Product filter', id: 'product-filter', type: 'product-filter' },
                    {
                        icon: PaginationEndIcon,
                        title: 'Product pagination',
                        id: 'product-pagination',
                        type: 'product-pagination',
                    },
                    { icon: CartIcon, title: 'Add to cart', id: 'add-to-cart', type: 'add-to-cart' },
                    { icon: ProductIcon, title: 'Product detail', id: 'product-detail', type: 'product-detail' },
                ],
            },
            {
                name: 'Blog',
                id: 'blog',
                items: [
                    { icon: BlogIcon, title: 'Blog posts list', id: 'blog-posts-list', type: 'blog-posts-list' },
                    {
                        icon: TextWithImageIcon,
                        title: 'Blog posts detail',
                        id: 'blog-posts-detail',
                        type: 'blog-posts-detail',
                    },
                    {
                        icon: TextQuoteIcon,
                        title: 'Blog posts related',
                        id: 'blog-posts-related',
                        type: 'blog-posts-related',
                    },
                ],
            },
            {
                name: 'Collections',
                id: 'collections',
                items: [{ icon: CategoriesIcon, title: 'Category list', id: 'category-list', type: 'category-list' }],
            },
        ],
        searchPlaceholder: 'Search Shopify...',
    },
];
