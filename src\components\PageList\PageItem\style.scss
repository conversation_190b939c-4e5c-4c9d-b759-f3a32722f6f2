.page-item{
  position: relative;

  &__image{
    width: 90px;
    height: 70px;
    object-fit: contain;
    object-position: center;
    margin-right: 4px;
    border-radius: 4px;
    border: 1px solid rgb(225, 226, 227);
  }

  &__content{
    max-width: 80%;
  }

  & + div {
      border-top: var(--pc-resource-list-separator-border);
  }

  &__more-action {
    display: none;
    position: absolute;
    top: 50%;
    right: 0;
    z-index: 50;
    transform: translate(-16px, -50%);
    z-index: 1000;
    @media screen and (max-width: 767px) {
      position: unset;
      margin-top: 6px;
    }

    &--active {
      display: flex;
    }

    &__panel{
      width: 210px;
      padding: 6px;
      .Polaris-ActionList__Item.Polaris-ActionList--default {
          padding: 6px 8px;
      }
    }
  }
  
  &:hover .page-item__more-action {
    display: flex;
    justify-content: flex-end;
  }
  .Polaris-ResourceItem:hover{
    background-color: transparent;
  }

  &__text-field-wrapper{
    .Polaris-TextField__Input {
        font-size: 13px;
        font-weight: 450;
        line-height: 20px;
    }
  }
}