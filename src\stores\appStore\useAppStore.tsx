import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { shared } from 'use-broadcast-ts';
import { devtools } from 'zustand/middleware';
import { AppActions, AppState } from '@/stores/appStore/types';
import { createAppSettingsSlice } from '@/stores/appStore/appSettings/appSettingsSlice';
import { createPageSlice } from '@/stores/appStore/pages/pageSlice';
import { createEditorSlice } from '@/stores/appStore/editor/editorSlice';
import { createCssSlice } from '@/stores/appStore/css/cssSlice';
export const useAppStore = create<AppState & AppActions>()(
    devtools(
        shared(
            immer((set, get, store) => ({
                token: '',
                setToken: (token) =>
                    set((state) => {
                        state.token = token;
                    }),
                isAuthenticated: false,
                setIsAuthenticated: (isAuthenticated) =>
                    set((state) => {
                        state.isAuthenticated = isAuthenticated;
                    }),
                accordion: {},
                setAccordion: (blockId, activeId) =>
                    set((state) => {
                        state.accordion[blockId] = { activeId };
                    }),
                ...createPageSlice(set, get, store),
                ...createAppSettingsSlice(set, get, store),
                ...createEditorSlice(set, get, store),
                ...createCssSlice(set, get, store),
            })),
            {
                name: 'autoketing-builder',
                partialize: (state) => ({
                    token: state.token,
                    isAuthenticated: state.isAuthenticated,
                    updatePageJson: state.updatePageJson,
                    zoomLevel: state.zoomLevel,
                    screenWidth: state.screenWidth,
                    setScreenWidth: state.setScreenWidth,
                    setDeviceType: state.setDeviceType,
                    templateType: state.templateType,
                    setTemplateType: state.setTemplateType,
                    pageList: state.pageList,
                    setPageList: state.setPageList,
                    currentParams: state.currentParams,
                    setCurrentParams: state.setCurrentParams,
                    updatePage: state.updatePage,
                    deletePage: state.deletePage,
                    duplicatePage: state.duplicatePage,
                    apiStatus: state.apiStatus,
                    cssDesktop: state.cssDesktop,
                    cssMobile: state.cssMobile,
                    getCombinedCss: state.getCombinedCss,
                    pageCreationStatus: state.pageCreationStatus,
                    setPageCreationStatus: state.setPageCreationStatus,
                    templateTypeId: state.templateTypeId,
                    setTemplateTypeId: state.setTemplateTypeId,
                    requestsStatus: state.requestsStatus,
                    updateRequestStatus: state.updateRequestStatus,
                    hasEdits: state.hasEdits,
                    setHasEdits: state.setHasEdits,
                }),
            },
        ),
    ),
);
