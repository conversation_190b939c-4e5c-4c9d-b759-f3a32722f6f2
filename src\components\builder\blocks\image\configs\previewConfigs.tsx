import { ReactNode } from 'react';
import { Auto_BlockToolbar } from '@giaminhautoketing/auto-builder';
import { ReactComponent as Image1Icon } from '@/assets/svgs/w-image-1.svg';
import { ReactComponent as Image2Icon } from '@/assets/svgs/w-image-2.svg';
import * as settings from './data';

export const previewConfigs: {
    label: string;
    icon: ReactNode;
    settings: Auto_BlockToolbar;
}[] = [
    {
        label: 'Image',
        icon: <Image1Icon />,
        settings: settings.image1,
    },
    {
        label: 'Image',
        icon: <Image2Icon />,
        settings: settings.image2,
    },
];
