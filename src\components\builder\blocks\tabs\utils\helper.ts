/* eslint-disable @typescript-eslint/no-explicit-any */
import { generateUnitValue } from '@/components/builder/data/utils/CssGenerator/helper';
import { generateBackground, generateBorder } from '@/components/builder/data/utils/CssGenerator/utils';
import { generateBoxShadow, generateTextShadow } from '@/utils';

const getCssString = (styles: Record<string, any>, keyIgnore: string[]) => {
    const result = Object.entries(styles)
        .map(([key, value]) => (keyIgnore.includes(key) ? value : `${key}: ${value};`))
        .join(';');

    return result;
};

export const getStylesTabContainer = (data: any) => {
    const { gap, align, background, boxShadow, border } = data;
    const styles = {
        display: 'flex',
        'flex-direction': 'var(--atk-tabs-direction)',
        'justify-content': align,
        'flex-wrap': 'wrap',
        gap: generateUnitValue(gap),
        background: generateBackground(background),
        border: generateBorder(border),
        ...(boxShadow && { 'box-shadow': generateBoxShadow(boxShadow) }),
    };

    const keyIgnore = ['background', 'border'];

    return getCssString(styles, keyIgnore);
};

export const getStyleTabContent = (data: any) => {
    const { background, boxShadow, border } = data;
    const styles = {
        background: generateBackground(background),
        border: generateBorder(border),
        'flex-grow': 1,
        ...(boxShadow && { 'box-shadow': generateBoxShadow(boxShadow) }),
    };

    const keyIgnore = ['background', 'border'];

    return getCssString(styles, keyIgnore);
};

export const getStyleTabNormal = (data: any) => {
    const { icon, background, border, direction, fitted, spacing, text } = data;
    const styles = {
        '--atk-tabs-normal-icon-size': generateUnitValue(icon.size),
        '--atk-tabs-normal-icon-color': icon.color,
        display: 'flex',
        'align-items': 'center',
        'flex-direction': direction === 'row' ? 'row' : 'column',
        'flex-grow': fitted ? '1' : '0',
        padding: '8px 16px',
        gap: generateUnitValue(spacing),
        background: generateBackground(background),
        border: generateBorder(border),
        'font-family': text.fontFamily,
        'font-weight': text.fontWeight,
        'font-size': generateUnitValue(text.fontSize),
        'font-style': text.fontStyle,
        'text-align': text.textAlign,
        'text-decoration': text.textDecoration,
        'text-transform': text.textTransform,
        color: text.color,
        'line-height': generateUnitValue(text.lineHeight),
        'letter-spacing': generateUnitValue(text.letterSpacing),
        ...(text.textShadow && { 'text-shadow': generateTextShadow(text.textShadow) }),
    };
    const keyIgnore = ['background', 'border'];

    return getCssString(styles, keyIgnore);
};

export const getStyleTabHover = (data: any) => {
    const { icon, background, border, text } = data;

    const styles = {
        '--atk-tabs-hover-icon-color': icon.color,
        background: generateBackground(background),
        border: generateBorder(border),
        color: text.color,
        'text-decoration': text.textDecoration,

        ...(text.textShadow && { 'text-shadow': generateTextShadow(text.textShadow) + '!important' }),
    };

    const keyIgnore = ['background', 'border'];

    return getCssString(styles, keyIgnore);
};

export const getStyleTabSelected = (data: any) => {
    const { icon, background, border, text } = data;

    const styles = {
        '--atk-tabs-selected-icon-color': icon.color,
        '--atk-tabs-selected-icon-size': generateUnitValue(icon.size),
        background: generateBackground(background),
        border: generateBorder(border),
        color: text.color,
        'text-decoration': text.textDecoration,
        ...(text.textShadow && { 'text-shadow': generateTextShadow(text.textShadow) }),
    };

    const keyIgnore = ['background', 'border'];

    return getCssString(styles, keyIgnore);
};
