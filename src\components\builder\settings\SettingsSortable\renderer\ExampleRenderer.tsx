/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-unused-vars */
// @ts-nocheck
import { SortableItemRequireProps, RendererProps } from '@/components/Sortable/types';

export function ExampleRenderer<T extends SortableItemRequireProps>({
    blockId,
    index,
    clone,
    data,
    isDragging,
    onRemove,
    insertPosition,
}: RendererProps<T> & {
    blockId: string;
}) {
    return <div>{data.name}</div>;
}
