import { FC, useMemo } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { getBlockProperty } from '@/utils/shared';
import { SettingsToggle } from '@/components/builder/settings/SettingsToggle';

interface SyncProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
}

const DEVICES = ['Desktop', 'Mobile'] as const;

export const Sync: FC<SyncProps> = ({ path, blockId, isUpdateConfigs, label }) => {
    const deviceSettings = useMemo(() => {
        return DEVICES.map((device) => {
            const value = getBlockProperty(`configs.${path}${device}`, blockId) || false;
            return {
                device,
                isActive: value,
                pathKey: `${path}${device}`,
                id: `sync-${blockId}-${device.toLowerCase()}`,
            };
        });
    }, [path, blockId]);

    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BlockStack gap="300">
                    {deviceSettings.map(({ device, pathKey, id }) => (
                        <SettingsToggle
                            key={device}
                            isUpdateConfigs={isUpdateConfigs}
                            label={device}
                            path={pathKey}
                            blockId={blockId}
                            toggleProps={{
                                id: id,
                            }}
                        />
                    ))}
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
