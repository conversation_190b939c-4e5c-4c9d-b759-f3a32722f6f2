import { FC } from 'react';
import { RadioGroupItem } from './RadioGroupItem';
import { RadioDataProps } from './types';

interface BaseRadioGroupProps {
    data: RadioDataProps[];
    onChangeRadio(value: string): void;
    activeRadio: string;
}

export const BaseRadioGroup: FC<BaseRadioGroupProps> = ({ data, onChangeRadio, activeRadio }) => {
    return (
        <>
            {data.map((item) => (
                <RadioGroupItem
                    key={item.value}
                    isActive={activeRadio === item.value}
                    data={item}
                    onChangeRadio={onChangeRadio}
                />
            ))}
        </>
    );
};
