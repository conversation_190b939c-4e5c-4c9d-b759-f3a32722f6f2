import { BreadcrumbItem } from './types';

export const DEFAULT_BREADCRUMB_ITEMS: BreadcrumbItem[] = [
    { label: 'Home', url: '/' },
    { label: 'Products', url: '/products' },
    { label: 'Categories', url: '/products/categories' },
    { label: 'Subcategories', url: '/products/categories/subcategories' },
    { label: 'Subsubcategories', url: '/products/categories/subcategories/subsubcategories' },
];

export const DEFAULT_BREADCRUMB_CONFIG = {
    showHome: true,
    showSeparator: true,
    showLastItem: true,
    showCurrentPage: true,
    icon: ``,
    color: 'rgba(0, 0, 0, 0.45)',
    iconSize: { val: '20', unit: 'px' },
    beforeEllipsis: { val: 0 },
    afterEllipsis: { val: 0 },
};

export const SEPARATOR_STYLES = {
    container: {
        margin: '0 3px',
        display: 'flex',
        alignItems: 'center',
        color: 'black',
    },
    ellipsis: {
        margin: '0 4px',
        color: 'rgba(0, 0, 0, 0.45)',
    },
} as const;

export const DATA_SET_AUTO_ID_SPACE_BETWEEN_TEXT_AND_ICON = 'data-auto-id-space-between-text-and-icon';
export const DATA_SET_AUTO_ID_BREADCRUMB_SPACING = 'data-auto-id-breadcrumb-spacing';
export const DATA_SET_BREADCRUMB_POSITION = 'data-breadcrumb-position';
export const DATA_SET_BREADCRUMB_CUSTOM_CSS_STYLE = 'data-breadcrumb-custom-css-style';
export const DATA_SET_BREADCRUMB_CUSTOM_CSS_CLASS = 'data-breadcrumb-custom-css-class';
export const DATA_SET_BREADCRUMB_CUSTOM_CSS_ENABLE = 'data-breadcrumb-custom-css-enable';
export const DATA_SET_BREADCRUMB_TYPOGRAPHY = 'data-breadcrumb-typography';
