import { genRandomBlockId } from '@giaminhautoketing/auto-builder';
import { ButtonSettings } from './types';

export const ButtonSettingsConfigs: ButtonSettings[] = [
    {
        id: genRandomBlockId(),
        cname: 'button',
        label: 'Button',
        type: 'button',
        bpConfigs: {
            desktop: {
                backgroundButton: {
                    type: 'color',
                    color: '#000000',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'scroll',
                        fill: 'cover',
                    },
                },

                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#ffffff',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '1',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    textShadow: undefined,
                    fontStyle: 'default',
                },
                borderButton: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                buttonsSpace: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
            },
            mobile: {
                backgroundButton: {
                    type: 'color',
                    color: '#000000',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'scroll',
                        fill: 'cover',
                    },
                },
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#ffffff',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '1',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    textShadow: undefined,
                    fontStyle: 'default',
                },
                borderButton: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                buttonsSpace: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
            },
        },
        configs: {
            content: {
                type: 'text-and-icon',
                text: 'Button',
                icon: '<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1664 1896.0833"> <path d="M1664 647q0 22-26 48l-363 354 86 500q1 7 1 20 0 21-10.5 35.5T1321 1619q-19 0-40-12l-449-236-449 236q-22 12-40 12-21 0-31.5-14.5T301 1569q0-6 2-20l86-500L25 695Q0 668 0 647q0-37 56-46l502-73L783 73q19-41 49-41t49 41l225 455 502 73q56 9 56 46z"></path> </svg>',
                iconColor: '#4A4A4A',
                iconSize: { val: '20', unit: 'px' },
                direction: 'row',
                reverse: false,
                spacing: {
                    val: '0',
                    unit: 'px',
                },
            },

            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            customCSS: {
                enable: false,
                className: '',
                style: '',
            },
            events: {},
        },
        overlay: {
            desktop: {
                width: 120,
                height: 40,
            },
            mobile: {
                width: 120,
                height: 40,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'button',
        label: 'Button',
        type: 'button',
        bpConfigs: {
            desktop: {
                backgroundButton: {
                    type: 'color',
                    color: '#6daeff',
                    image: {
                        url: '',
                        repeat: '',
                        position: '',
                        attachment: '',
                        fill: '',
                    },
                },
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#ffffff',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '2.5',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    textShadow: undefined,
                    fontStyle: 'default',
                },

                borderButton: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                buttonsSpace: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
            },
            mobile: {
                backgroundButton: {
                    type: 'color',
                    color: '#6daeff',
                    image: {
                        url: '',
                        repeat: '',
                        position: '',
                        attachment: '',
                        fill: '',
                    },
                },

                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#ffffff',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '2.5',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    textShadow: undefined,
                    fontStyle: 'default',
                },

                borderButton: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                buttonsSpace: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
            },
        },
        configs: {
            content: {
                type: 'text-only',
                text: 'Button',
                icon: '',
                iconColor: '#4A4A4A',
                iconSize: {
                    val: '8',
                    unit: 'px',
                },
                direction: 'row',
                reverse: false,
                spacing: {
                    val: '0',
                    unit: 'px',
                },
            },
            displayOnDesktop: false,
            displayOnMobile: false,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '2',
                delay: { val: '0', unit: 's' },
            },

            customCSS: {
                enable: false,
                className: '',
                style: '',
            },
            events: {},
        },
        overlay: {
            desktop: {
                width: 120,
                height: 40,
            },
            mobile: {
                width: 120,
                height: 40,
            },
        },
    },
];
