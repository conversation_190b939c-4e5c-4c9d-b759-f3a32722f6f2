import { FC } from 'react';

import EmptyImage from '@/assets/images/no-image.png?url';

interface ButtonTriggerProps {
    type: 'color' | 'image';
    colorValue: string;
    imageUrl: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    path: string;
    children?: React.ReactNode;
    onClick?: () => void;
}

export const ButtonTrigger: FC<ButtonTriggerProps> = ({ type, colorValue, imageUrl, children, onClick }) => {
    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '4px',
                width: '140px',
                height: '32px',
                borderRadius: '8px',
                border: '1px solid #8A8A8A',
                cursor: 'pointer',
            }}
            onClick={onClick}
        >
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: '8px',
                }}
            >
                {type === 'color' ? (
                    colorValue ? (
                        <div
                            style={{
                                width: '24px',
                                height: '24px',
                                borderRadius: '4px',
                                border: '1px solid #CCCCCC',
                                backgroundColor: colorValue,
                                backgroundImage:
                                    colorValue === 'rgba(255, 255, 255, 0)'
                                        ? 'linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc), linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc)'
                                        : 'none',
                                backgroundSize: '10px 10px',
                                backgroundPosition: '0 0, 5px 5px',
                                opacity: colorValue === 'rgba(255, 255, 255, 0)' ? 0.5 : 1,
                            }}
                        />
                    ) : (
                        <img
                            src={EmptyImage}
                            alt="no-background-color"
                            style={{ width: '24px', height: '24px', borderRadius: '4px', objectFit: 'cover' }}
                        />
                    )
                ) : (
                    <img
                        src={imageUrl || EmptyImage}
                        alt="no-background-image"
                        style={{ width: '24px', height: '24px', borderRadius: '4px', objectFit: 'cover' }}
                    />
                )}
                <div>
                    <p>{type === 'color' ? 'Color' : 'Image'}</p>
                </div>
            </div>
            <div
                style={{
                    borderLeft: '1px solid #8A8A8A',
                    padding: '6px',
                    paddingRight: '1px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                {children}
            </div>
        </div>
    );
};
