import { FC } from 'react';
import { Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BlockStack, Box } from '@shopify/polaris';

import { SettingsColorPicker, SettingsSelect } from '@/components/builder/settings';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';
import {
    textAlignOptions,
    textFontWeightOptions,
    textFontStyleOptions,
    textTransformOptions,
    textDecorationOptions,
} from '@/components/builder/data/options';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { SettingsSliderInput } from '@/components/builder/settings/SettingsSliderInput';
import { SettingsFontFamily } from '@/components/builder/settings/SettingsFontFamily';
import { SettingsShadow } from '@/components/builder/settings/SettingsShadow';
import './style.scss';
interface TypographyProps {
    id: string;
    selectedBlockTarget: HTMLElement;
}

const DEFAULT_PATH_CONFIGS_TYPOGRAPHY = 'typography';

export const Typography: FC<TypographyProps> = ({ id, selectedBlockTarget }) => {
    return (
        <BaseCollapse
            label="Typography"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BlockStack gap="400">
                    <SettingsFontFamily
                        blockId={id}
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.fontFamily`}
                        isUpdateConfigs={false}
                    />
                    <SettingsSelect
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.fontWeight`}
                        blockId={id}
                        label="Font weight"
                        isUpdateConfigs={false}
                        options={textFontWeightOptions}
                    />
                    <SettingsInput
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.fontSize`}
                        blockId={id}
                        isUpdateConfigs={false}
                        label="Font size"
                        inputProps={{
                            suffix: 'px',
                            min: 4,
                        }}
                    />
                    <SettingsSelect
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.fontStyle`}
                        blockId={id}
                        label="Font style"
                        isUpdateConfigs={false}
                        options={textFontStyleOptions}
                    />
                    <SettingsSwitchTab
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.textAlign`}
                        blockId={id}
                        label="Text align"
                        isUpdateConfigs={false}
                        options={textAlignOptions}
                    />
                    <SettingsSwitchTab
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.textDecoration`}
                        blockId={id}
                        label="Text decoration"
                        isUpdateConfigs={false}
                        options={textDecorationOptions}
                    />
                    <SettingsSelect
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.textTransform`}
                        blockId={id}
                        label="Text transform"
                        isUpdateConfigs={false}
                        options={textTransformOptions}
                    />
                    <SettingsShadow
                        type="text-shadow"
                        blockId={id}
                        isUpdateConfigs={false}
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.textShadow`}
                        label="Text shadow"
                    />
                    <SettingsColorPicker
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.color`}
                        blockId={id}
                        label="Text color"
                        isUpdateConfigs={false}
                    />
                    <SettingsSliderInput
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.lineHeight`}
                        blockId={id}
                        isUpdateConfigs={false}
                        cssVariable={`--atk-button-line-height`}
                        selectedBlockTarget={selectedBlockTarget}
                        title="Line height"
                        direction="column"
                        max={5}
                        step={0.1}
                        min={0}
                        inputProps={{
                            align: 'center',
                            step: 0.1,
                            min: 0,
                        }}
                    />
                    <SettingsSliderInput
                        path={`${DEFAULT_PATH_CONFIGS_TYPOGRAPHY}.letterSpacing`}
                        blockId={id}
                        isUpdateConfigs={false}
                        cssVariable={`--atk-button-letter-spacing`}
                        selectedBlockTarget={selectedBlockTarget}
                        title="Letter spacing"
                        direction="column"
                        max={10}
                        step={0.25}
                        min={0}
                        inputProps={{
                            suffix: 'px',
                            step: 0.25,
                            min: 0,
                        }}
                    />
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
