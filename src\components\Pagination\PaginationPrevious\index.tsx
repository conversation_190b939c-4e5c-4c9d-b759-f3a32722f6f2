import { FC } from 'react';
import { css } from '@emotion/react';
import { PaginationControl } from '../PaginationControl';
import { usePaginationContext } from '../Pagination.context';
import { ReactComponent as NextIcon } from '@/assets/svgs/pagination-next.svg';

export const PaginationPrevious: FC = () => {
    const ctx = usePaginationContext();
    return (
        <PaginationControl
            icon={<NextIcon css={{ transform: 'rotate(180deg)' }} />}
            disabled={!ctx.hasPrevious}
            css={css`
                .Polaris-Button {
                    padding-left: 12px;
                }
                .Polaris-Button__Icon {
                    display: flex;
                    ${!ctx.hasPrevious &&
                    css`
                        color: rgba(204, 204, 204, 1);
                    `}
                }
            `}
            onClick={ctx.onPrevious}
        >
            Previous
        </PaginationControl>
    );
};
