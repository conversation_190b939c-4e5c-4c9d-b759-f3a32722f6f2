import { createNode, NodeItem } from '@/utils/nodes';
import {
    SettingBorder,
    SettingsBackgroundColor,
    SettingsColorPicker,
    SettingsInput,
    SettingsShadow,
    SettingsSliderInput,
    SettingsToggle,
    SettingsIcon,
    SettingsFontFamily,
    SettingsSelect,
    SettingsSwitchTab,
} from '@/components/builder/settings';
import {
    textAlignOptions,
    textDecorationOptions,
    textFontStyleOptions,
    textFontWeightOptions,
    textTransformOptions,
} from '@/components/builder/data/options';
import { SettingsAlign } from '../components';

export type DisplayType = 'tabContainer' | 'tabs' | 'containers';
export type TabStatus = 'normal' | 'hover' | 'selected';

export type DisplaySettings = {
    styles?: NodeItem[];
    border?: NodeItem[];
    text?: NodeItem[];
};

export type RenderMapType = {
    tabContainer: DisplaySettings;
    tabs: Record<TabStatus, DisplaySettings>;
    containers: DisplaySettings;
};

export const displayOptions = [
    {
        id: 'tabContainer',
        content: 'Tab Container',
    },
    {
        id: 'tabs',
        content: 'Tabs',
    },
    {
        id: 'containers',
        content: 'Containers',
    },
];

export const statusOptions = [
    { id: 'normal', contentTooltip: 'Normal' },
    { id: 'hover', contentTooltip: 'Hover' },
    { id: 'selected', contentTooltip: 'Selected' },
];
export const getDisplaySettings = (
    renderMap: RenderMapType,
    display: DisplayType,
    status: TabStatus = 'normal',
): DisplaySettings => {
    return display !== 'tabs' ? renderMap[display] : renderMap.tabs[status];
};

export const createRenderMap = (selectedBlockId: string | null, display: DisplayType, status: TabStatus) => {
    if (!selectedBlockId) return { tabContainer: {}, tabs: { normal: {}, hover: {}, selected: {} }, containers: {} };

    const mapStatus = {
        normal: 'tabNormal',
        hover: 'tabHover',
        selected: 'tabSelected',
    };

    const createPath = (property: string) => {
        if (display === 'tabs') {
            return `basic.${mapStatus[status]}.${property}`;
        }
        return `basic.${display}.${property}`;
    };

    return {
        tabContainer: {
            styles: [
                createNode(SettingsAlign, {
                    path: createPath('align'),
                    blockId: selectedBlockId,
                }),
                createNode(SettingsInput, {
                    path: createPath('gap'),
                    blockId: selectedBlockId,
                    label: 'Gap',
                    inputProps: { suffix: 'px' },
                }),
                createNode(SettingsToggle, {
                    path: createPath('direction'),
                    blockId: selectedBlockId,
                    label: 'Vertical',
                    valueMap: { true: 'column', false: 'row' },
                }),
                createNode(SettingsBackgroundColor, {
                    path: createPath('background'),
                    blockId: selectedBlockId,
                    label: 'Background',
                }),
                createNode(SettingsShadow, {
                    type: 'box-shadow',
                    path: createPath('boxShadow'),
                    blockId: selectedBlockId,
                    label: 'Box shadow',
                }),
            ],
            border: [
                createNode(SettingBorder, {
                    path: createPath('border'),
                    blockId: selectedBlockId,
                    label: 'Border',
                }),
            ],
        },
        tabs: {
            normal: {
                styles: [
                    createNode(SettingsIcon, {
                        path: createPath('icon.source'),
                        blockId: selectedBlockId,
                        label: 'Icon',
                        direction: 'column',
                    }),

                    createNode(SettingsColorPicker, {
                        path: createPath('icon.color'),
                        blockId: selectedBlockId,
                        label: 'Icon color',
                    }),
                    createNode(SettingsSliderInput, {
                        path: createPath('icon.size'),
                        blockId: selectedBlockId,
                        title: 'Icon size',
                        max: 64,
                        min: 8,
                        inputProps: { suffix: 'px' },
                        direction: 'column',
                    }),
                    createNode(SettingsBackgroundColor, {
                        path: createPath('background'),
                        blockId: selectedBlockId,
                        label: 'Background',
                    }),
                    createNode(SettingsShadow, {
                        type: 'box-shadow',
                        path: createPath('boxShadow'),
                        blockId: selectedBlockId,
                        label: 'Box shadow',
                    }),
                    createNode(SettingsToggle, {
                        path: createPath('fitted'),
                        blockId: selectedBlockId,
                        label: 'Fitted',
                    }),
                    createNode(SettingsSliderInput, {
                        path: createPath('spacing'),
                        blockId: selectedBlockId,
                        title: 'Spacing',
                        max: 100,
                        min: 0,
                        inputProps: { suffix: 'px' },
                        direction: 'column',
                    }),
                ],
                border: [
                    createNode(SettingBorder, {
                        path: createPath('border'),
                        blockId: selectedBlockId,
                        label: 'Border',
                    }),
                ],
                text: [
                    createNode(SettingsFontFamily, {
                        path: createPath('text.fontFamily'),
                        blockId: selectedBlockId,
                    }),
                    createNode(SettingsSelect, {
                        path: createPath('text.fontWeight'),
                        blockId: selectedBlockId,
                        options: textFontWeightOptions,
                        label: 'Font weight',
                    }),
                    createNode(SettingsInput, {
                        path: createPath('text.fontSize'),
                        blockId: selectedBlockId,
                        label: 'Font size',
                        inputProps: { min: 4, suffix: 'px' },
                    }),
                    createNode(SettingsSelect, {
                        path: createPath('text.fontStyle'),
                        blockId: selectedBlockId,
                        options: textFontStyleOptions,
                        label: 'Font style',
                    }),
                    createNode(SettingsSwitchTab, {
                        path: createPath('text.textAlign'),
                        blockId: selectedBlockId,
                        options: textAlignOptions,
                        label: 'Text align',
                    }),
                    createNode(SettingsSwitchTab, {
                        path: createPath('text.textDecoration'),
                        blockId: selectedBlockId,
                        options: textDecorationOptions,
                        label: 'Text decoration',
                    }),
                    createNode(SettingsSelect, {
                        path: createPath('text.textTransform'),
                        blockId: selectedBlockId,
                        options: textTransformOptions,
                        label: 'Text transform',
                    }),
                    createNode(SettingsShadow, {
                        type: 'text-shadow',
                        path: createPath('text.textShadow'),
                        blockId: selectedBlockId,
                        label: 'Text shadow',
                    }),
                    createNode(SettingsColorPicker, {
                        path: createPath('text.color'),
                        blockId: selectedBlockId,
                        label: 'Text color',
                    }),
                    createNode(SettingsSliderInput, {
                        path: createPath('text.lineHeight'),
                        blockId: selectedBlockId,
                        title: 'Line height',
                        min: 0,
                        max: 5,
                        step: 0.1,
                    }),
                    createNode(SettingsSliderInput, {
                        path: createPath('text.letterSpacing'),
                        blockId: selectedBlockId,
                        title: 'Letter spacing',
                        min: -20,
                        max: 20,
                        step: 1,
                    }),
                ],
            },
            hover: {
                styles: [
                    createNode(SettingsColorPicker, {
                        path: createPath('icon.color'),
                        blockId: selectedBlockId,
                        label: 'Icon color',
                    }),
                    createNode(SettingsBackgroundColor, {
                        path: createPath('background'),
                        blockId: selectedBlockId,
                        label: 'Background',
                    }),
                    createNode(SettingsShadow, {
                        type: 'box-shadow',
                        path: createPath('boxShadow'),
                        blockId: selectedBlockId,
                        label: 'Box shadow',
                    }),
                ],
                border: [
                    createNode(SettingBorder, {
                        path: createPath('border'),
                        blockId: selectedBlockId,
                        label: 'Border',
                    }),
                ],
                text: [
                    createNode(SettingsSwitchTab, {
                        path: createPath('text.textDecoration'),
                        blockId: selectedBlockId,
                        options: textDecorationOptions,
                        label: 'Text decoration',
                    }),
                    createNode(SettingsColorPicker, {
                        path: createPath('text.color'),
                        blockId: selectedBlockId,
                        label: 'Text color',
                    }),
                    createNode(SettingsShadow, {
                        type: 'text-shadow',
                        path: createPath('text.textShadow'),
                        blockId: selectedBlockId,
                        label: 'Text shadow',
                    }),
                ],
            },
            selected: {
                styles: [
                    createNode(SettingsIcon, {
                        path: createPath('icon.source'),
                        blockId: selectedBlockId,
                        label: 'Icon',
                        direction: 'column',
                    }),

                    createNode(SettingsColorPicker, {
                        path: createPath('icon.color'),
                        blockId: selectedBlockId,
                        label: 'Icon color',
                    }),
                    createNode(SettingsSliderInput, {
                        path: createPath('icon.size'),
                        blockId: selectedBlockId,
                        title: 'Icon size',
                        max: 64,
                        min: 8,
                        inputProps: { suffix: 'px' },
                        direction: 'column',
                    }),
                    createNode(SettingsBackgroundColor, {
                        path: createPath('background'),
                        blockId: selectedBlockId,
                        label: 'Background',
                    }),
                    createNode(SettingsShadow, {
                        type: 'box-shadow',
                        path: createPath('boxShadow'),
                        blockId: selectedBlockId,
                        label: 'Box shadow',
                    }),
                ],
                border: [
                    createNode(SettingBorder, {
                        path: createPath('border'),
                        blockId: selectedBlockId,
                        label: 'Border',
                    }),
                ],
                text: [
                    createNode(SettingsSwitchTab, {
                        path: createPath('text.textDecoration'),
                        blockId: selectedBlockId,
                        options: textDecorationOptions,
                        label: 'Text decoration',
                    }),
                    createNode(SettingsColorPicker, {
                        path: createPath('text.color'),
                        blockId: selectedBlockId,
                        label: 'Text color',
                    }),
                    createNode(SettingsShadow, {
                        type: 'text-shadow',
                        path: createPath('text.textShadow'),
                        blockId: selectedBlockId,
                        label: 'Text shadow',
                    }),
                ],
            },
        },
        containers: {
            styles: [
                createNode(SettingsBackgroundColor, {
                    path: createPath('background'),
                    blockId: selectedBlockId,
                    label: 'Background',
                }),
                createNode(SettingsShadow, {
                    type: 'box-shadow',
                    path: createPath('boxShadow'),
                    blockId: selectedBlockId,
                    label: 'Box shadow',
                }),
            ],
            border: [
                createNode(SettingBorder, {
                    path: createPath('border'),
                    blockId: selectedBlockId,
                    label: 'Border',
                }),
            ],
            text: [],
        },
    };
};
