.settings-font-family {
    .Polaris-InlineStack > .<PERSON>is-<PERSON><PERSON> {
        margin: 0;
        padding: 0;
    }
    .Polaris-<PERSON><PERSON>[data-state='open'] {
        box-shadow: -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset,
            0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset,
            0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset !important;
        border-color: transparent !important;
    }
    .Polaris-Button--sizeLarge {
        min-width: 140px;
        width: 140px;
        border: 1px solid var(--p-color-input-border);
        padding: 5px 12px;
        .Polaris-Text--root {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    &__popover {
        height: 100%;
        background-color: #fff;
        border-radius: 0.5rem;
        border: 1px solid #ebebeb;
        min-height: 20rem;
        &__title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #ebebeb;
            padding: 0.75rem;
        }
        &__content {
            padding: 1rem 0.75rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            .switch-tabs__container--list {
                max-width: 100%;
                li {
                    width: 100%;
                }
                &--button {
                    width: 105px;
                    padding: 0.5rem 20px;
                    white-space: nowrap;
                }
            }
            &__search {
                width: 100%;
                .Polaris-TextField__Backdrop {
                    border: none;
                    outline: none !important;
                    background-color: transparent !important;
                }
                input {
                    font-size: 0.75rem;
                }
            }
            &__list {
                display: flex;
                flex-direction: column;
                max-height: 20rem;
                overflow-y: auto;
                margin-right: -0.625rem;
                position: relative;
                padding-bottom: 4.625rem;
                scrollbar-width: thin;
                scrollbar-color: #d9d9d9 transparent;
                &__loading {
                    display: flex;
                    justify-content: center;
                    padding: 1rem;
                }
                &__empty {
                    display: flex;
                    justify-content: center;
                    padding: 1rem;
                }

                &::-webkit-scrollbar {
                    width: 0;
                }
                &::-webkit-scrollbar-button {
                    display: none;
                    height: 0;
                    width: 0;
                }

                &:hover {
                    &::-webkit-scrollbar {
                        width: 4px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background-color: #d9d9d9;
                        border-radius: 2px;
                    }

                    &::-webkit-scrollbar-track {
                        background: transparent;
                    }
                }
            }
        }
        &__footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4.625rem;
            padding-top: 1.6875rem;
            padding-left: 0.75rem;
            margin-right: 0.75rem;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 2.85%, #fff 99.67%);
            .Polaris-Button {
                width: 100%;
                background-color: #75a434;
                color: #fff;
                box-shadow: none;
                svg {
                    fill: #fff;
                }
                &:hover {
                    background-color: #6c9535;
                    svg {
                        fill: #fff;
                    }
                }
            }
            .Polaris-DropZone--sizeLarge {
                min-height: auto;
            }
            .Polaris-DropZone--hasOutline:not(.Polaris-DropZone--isDisabled)::after {
                border: none;
            }
        }
    }
}

.list-item-font {
    position: relative;
    &__item {
        position: relative;
        width: 100%;
        cursor: pointer;
        border-bottom: 1px solid var(--p-border);
        padding: 0.625rem 0;
        .Polaris-InlineStack {
            .Polaris-Icon {
                margin: 0;
            }
        }
    }
    &__delete {
        visibility: hidden;
        position: absolute;
        right: 28px;
        top: 0.625rem;
        cursor: pointer;
    }
    &:hover {
        .list-item-font__delete {
            visibility: visible;
        }
        .list-item-font__item {
            background-color: #f1f1f1;
        }
    }
}
