import { Button } from '@shopify/polaris';
import { DragHandleIcon, XIcon } from '@shopify/polaris-icons';
import { SortableItemRequireProps, RendererProps } from '@/components/Sortable/types';
import './styles.scss';
import { useAppStore } from '@/stores/appStore';
import { FormField } from '@/components/builder/blocks/form/types';

export function SortableFormField<T extends SortableItemRequireProps>({
    // blockId,
    // index,
    clone,
    data,
    isDragging,
    onRemove,
    insertPosition,
    handleProps,
}: RendererProps<T> & {
    blockId: string;
}) {
    const isGhost = isDragging && !clone;
    const setSelectedFormField = useAppStore((state) => state.setSelectedFormField);
    return (
        <div
            className={`form-field-item ${insertPosition ? `insert-${insertPosition}` : ''} ${isGhost ? 'ghost' : ''}`}
            onClick={() => {
                setSelectedFormField(data as unknown as <PERSON><PERSON>ield);
            }}
        >
            <div className="form-field-item-label">
                <div className="form-field-item-label-drag-handle" {...handleProps}>
                    <Button icon={DragHandleIcon} variant="plain" pressed={false} />
                </div>
                <div className="form-field-item-label-text">{isGhost ? '' : data.label}</div>
            </div>
            <div className="form-field-item-remove" onClick={(e) => e.stopPropagation()}>
                <Button icon={XIcon} variant="plain" pressed={false} onClick={() => onRemove?.()} />
            </div>
        </div>
    );
}
