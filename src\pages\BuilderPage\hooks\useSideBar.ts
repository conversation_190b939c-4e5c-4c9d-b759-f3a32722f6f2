import { useState, useCallback } from 'react';
import { SideBarItemProps } from '../types/sidebar';
import { ConfigTabs } from '../configs/tabs';

export interface SectionState {
    activeTab: string;
    searchTerm: string;
    isSearchOpen: boolean;
}

export interface DrawerContentState {
    sections?: SectionState;
    elements?: SectionState;
}

const defaultDrawerContentState: DrawerContentState = {
    sections: {
        activeTab: ConfigTabs[0].id,
        searchTerm: '',
        isSearchOpen: false,
    },
    elements: {
        activeTab: ConfigTabs[0].id,
        searchTerm: '',
        isSearchOpen: false,
    },
};

export const useSideBar = () => {
    const [menuActive, setMenuActive] = useState<SideBarItemProps | null>(null);
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [drawerContentState, setDrawerContentState] = useState<DrawerContentState>(defaultDrawerContentState);

    const closeDrawer = useCallback(() => {
        setIsDrawerOpen(false);

        setMenuActive(null);
        setDrawerContentState(defaultDrawerContentState);
    }, []);

    const handleSelectMenu = useCallback((id: string, items: SideBarItemProps[]) => {
        const selectedItem = items.find((item) => item.id === id);
        if (selectedItem) {
            setMenuActive(selectedItem);
            setIsDrawerOpen(true);
            setDrawerContentState(defaultDrawerContentState);
        }
    }, []);

    const updateDrawerContentState = useCallback(
        <K extends keyof DrawerContentState>(key: K, newState: Partial<NonNullable<DrawerContentState[K]>>) => {
            setDrawerContentState((prev) => ({
                ...prev,
                [key]: {
                    ...prev[key],
                    ...newState,
                },
            }));
        },
        [],
    );

    return {
        menuActive,
        isDrawerOpen,
        drawerContentState,
        handleSelectMenu,
        updateDrawerContentState,
        closeDrawer,
        setIsDrawerOpen,
    };
};
