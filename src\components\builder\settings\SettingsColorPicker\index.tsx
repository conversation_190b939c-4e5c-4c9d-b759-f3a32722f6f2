import { FC } from 'react';
import { TextProps } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout, BaseColorPickerV2 } from '@/components/builder/base';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];

interface SettingsColorPickerProps
    extends Pick<
        BaseItemLayoutProps,
        'direction' | 'containerClassName' | 'tooltipContent' | 'hasTooltip' | 'tooltipChildren'
    > {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
}

export const SettingsColorPicker: FC<SettingsColorPickerProps> = ({
    blockId,
    path,
    isUpdateConfigs,
    label,
    textProps,
    ...otherProps
}) => {
    const colorValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChangeColor = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, value);
        } else {
            updateBlockProperty(blockId, currentDevice, path, value);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseColorPickerV2
                color={colorValue ?? '#f37321'}
                onChange={handleChangeColor}
                popoverOptions={{
                    offsetOptions: { mainAxis: 10 },
                }}
            />
        </BaseItemLayout>
    );
};
