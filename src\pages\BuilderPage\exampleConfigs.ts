/* eslint-disable @typescript-eslint/no-explicit-any */
import { genRandomBlockId } from '@giaminhautoketing/auto-builder';

// Auto_BlockToolbar
export const exampleToolbarOptions: any[] = [
    {
        id: genRandomBlockId(),
        cname: 'button',
        label: 'Button',
        type: 'button',
        configs: {
            buttonText: 'Button',
            displayOnDesktop: true,
            displayOnMobile: true,
        },
        bpConfigs: {
            desktop: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
            },
            mobile: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: 120,
                height: 40,
            },
            mobile: {
                width: 120,
                height: 40,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'text',
        label: 'Text',
        type: 'text',
        configs: {
            content: '<h1>Hello World</h1>',
            displayOnDesktop: true,
            displayOnMobile: true,
        },
        bpConfigs: {
            desktop: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '120', unit: 'px' },
                height: 'auto',
                fontFamily: '',
                fontWeight: '500',
                fontSize: { val: '16', unit: 'px' },
                fontStyle: 'default',
                textAlign: 'left',
                textDecoration: 'default',
                textTransform: 'default',
                textColor: '#000000',
                lineHeight: { val: '1', unit: '' },
                letterSpacing: { val: '0', unit: 'px' },
            },
            mobile: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: 120,
                height: 40,
            },
            mobile: {
                width: 120,
                height: 40,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'image',
        label: 'Image',
        type: 'image',
        configs: {
            displayOnDesktop: true,
            displayOnMobile: true,
            image: 'https://r2.nucuoimekong.com/wp-content/uploads/buc-anh-dep-can-bang-sang-tot-1.jpg',
        },
        bpConfigs: {
            desktop: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '300', unit: 'px' },
                height: { val: '180', unit: 'px' },
            },
            mobile: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '300', unit: 'px' },
                height: { val: '180', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: 300,
                height: 180,
            },
            mobile: {
                width: 300,
                height: 180,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'shape',
        label: 'Shape',
        type: 'shape',
        configs: {
            content: {
                type: 'svg',
                svg: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="36" height="36" fill="currentColor"/>
            </svg>
            `,
            },
            border: {
                color: '#000000',
                top: { val: '1', unit: 'px' },
                right: { val: '1', unit: 'px' },
                bottom: { val: '1', unit: 'px' },
                left: { val: '1', unit: 'px' },
                type: 'default',
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            syncOnDesktop: false,
            syncOnMobile: false,
            customCSS: {
                classNames: '',
                style: '',
            },
            events: {},
        },
        bpConfigs: {
            desktop: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
                border: {
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
            },
            mobile: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: '',
                        position: '',
                        attachment: '',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
            },
        } as any,
        overlay: {
            desktop: {
                width: 50,
                height: 50,
            },
            mobile: {
                width: 50,
                height: 50,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'html-code',
        label: 'HTML code',
        type: 'html-code',
        configs: {
            content: '<h1>Hello World</h1>',
            displayOnDesktop: true,
            displayOnTablet: true,
            displayOnMobile: true,
        },
        bpConfigs: {
            desktop: {
                background: '#6daeff',
                color: '#ffffff',
                width: { val: '120', unit: 'px' },
                height: { val: '40', unit: 'px' },
            },
            tablet: {},
            mobile: {},
        },
        overlay: {
            desktop: {
                width: 120,
                height: 40,
            },
            tablet: {
                width: 120,
                height: 40,
            },
            mobile: {
                width: 120,
                height: 40,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'accordion',
        label: 'Accordion',
        type: 'accordion',
        configs: {
            content: {
                type: 'svg',
                svg: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="36" height="36" fill="currentColor"/>
            </svg>
            `,
            },
            border: {
                color: '#000000',
                top: { val: '1', unit: 'px' },
                right: { val: '1', unit: 'px' },
                bottom: { val: '1', unit: 'px' },
                left: { val: '1', unit: 'px' },
                type: 'default',
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            customCSS: {
                classNames: '',
                style: '',
            },
            events: {},
        },
        bpConfigs: {
            desktop: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
            },
            mobile: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                },
            },
        },
        overlay: {
            desktop: {
                width: 50,
                height: 50,
            },
            mobile: {
                width: 50,
                height: 50,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'video',
        label: 'Video',
        type: 'video',
        configs: {},
        bpConfigs: {},
    },
    {
        id: genRandomBlockId(),
        cname: 'tabs',
        label: 'Tabs',
        type: 'tabs',
        configs: {},
        bpConfigs: {},
    },
];
