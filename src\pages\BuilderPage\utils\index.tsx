/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode } from 'react';
import { TabProps } from '@shopify/polaris';
import { Auto_BlockData, Auto_BlockType } from '@giaminhautoketing/auto-builder';
import * as TEXT from '@/components/builder/blocks/text/settings';
import * as BUTTON from '@/components/builder/blocks/button/settings';
import * as IMAGE from '@/components/builder/blocks/image/settings';
import * as SHAPE from '@/components/builder/blocks/shape/settings';
import * as HTMLCODE from '@/components/builder/blocks/htmlCode/settings';
import * as VIDEO from '@/components/builder/blocks/video/settings';
import * as FORM from '@/components/builder/blocks/form/settings';
import * as ACCORDION from '@/components/builder/blocks/accordion/settings';
import * as BREADCRUMB from '@/components/builder/blocks/breadcrumb/settings';
import * as TABS from '@/components/builder/blocks/tabs/settings';

import { generatePageHTML } from '@/components/builder/blocks/page/templates/generatePageHTML';
import { generateSectionHTML } from '@/components/builder/blocks/section/templates/generateSectionHTML';
import { generateFormHTML } from '@/components/builder/blocks/form/templates/generateFormHTML';
import { generateButtonHTML } from '@/components/builder/blocks/button/templates/generateButtonHTML';
import { generateShapeHTML } from '@/components/builder/blocks/shape/templates/generateShapeHTML';

interface BlockRelated {
    basic: ReactNode | null;
    advanced: ReactNode | null;
    contents?: ReactNode | null;
}

// Auto_BlockType
export const getBlockRelated = (blockType: any) => {
    let blockRelated: BlockRelated = {
        basic: null,
        advanced: null,
    };

    switch (blockType) {
        case 'text':
            blockRelated = {
                basic: <TEXT.Basic />,
                advanced: <TEXT.Advanced />,
            };
            break;
        case 'button':
            blockRelated = {
                basic: <BUTTON.Basic />,
                advanced: <BUTTON.Advanced />,
            };
            break;
        case 'image':
            blockRelated = {
                basic: <IMAGE.Basic />,
                advanced: <IMAGE.Advanced />,
            };
            break;
        case 'shape' as Auto_BlockType:
            blockRelated = {
                basic: <SHAPE.Basic />,
                advanced: <SHAPE.Advanced />,
            };
            break;
        case 'html-code':
            blockRelated = {
                basic: <HTMLCODE.Basic />,
                advanced: <HTMLCODE.Advanced />,
                contents: <HTMLCODE.Contents />,
            };
            break;
        case 'video':
            blockRelated = {
                basic: <VIDEO.Basic />,
                advanced: <VIDEO.Advanced />,
            };
            break;
        case 'form' as Auto_BlockType:
            blockRelated = {
                basic: <FORM.Basic />,
                contents: <FORM.Contents />,
                advanced: <FORM.Advanced />,
            };
            break;
        case 'accordion':
            blockRelated = {
                basic: <ACCORDION.Basic />,
                contents: <ACCORDION.Contents />,
                advanced: <ACCORDION.Advanced />,
            };
            break;
        case 'breadcrumb' as Auto_BlockType:
            blockRelated = {
                basic: <BREADCRUMB.Basic />,
                advanced: <BREADCRUMB.Advanced />,
                contents: <BREADCRUMB.Contents />,
            };
            break;
        case 'tabs':
            blockRelated = {
                basic: <TABS.Basic />,
                contents: <TABS.Contents />,
                advanced: <TABS.Advanced />,
            };
            break;
    }
    return blockRelated;
};

export const getTabsInspectorOptions = (blockType?: Auto_BlockType): TabProps[] => {
    const baseTabs = [
        { id: 'basic', content: 'Basic' },
        { id: 'advanced', content: 'Advanced' },
    ];

    if (
        blockType === ('form' as Auto_BlockType) ||
        blockType === ('accordion' as Auto_BlockType) ||
        blockType === ('html-code' as Auto_BlockType)
    ) {
        return [{ id: 'contents', content: 'Contents' }, ...baseTabs];
    }

    if (blockType === ('breadcrumb' as Auto_BlockType)) {
        return [{ id: 'contents', content: 'Content' }, ...baseTabs];
    }

    if (blockType === ('tabs' as Auto_BlockType)) {
        return [{ id: 'contents', content: 'Contents' }, ...baseTabs];
    }

    // TODO: Add more tabs for other block types

    return baseTabs;
};

export const getBlockTypeClassName = (blockType: Auto_BlockType): string => {
    return `setting-tabs-inspector__${blockType}`;
};

export const getDefaultActiveTab = (blockType: Auto_BlockType): string => {
    const defaultTabs = {
        form: 'contents',
        button: 'basic',
        text: 'basic',
        image: 'basic',
        shape: 'basic',
        accordion: 'contents',
        breadcrumb: 'contents',
        'html-code': 'contents',
        tabs: 'contents',
        // TODO: Add more default tabs for other block types
    };

    return defaultTabs[blockType as keyof typeof defaultTabs] || 'basic';
};

export const generateTemplates = () => ({
    page: (blockId: string, block: Auto_BlockData, isLiquid: boolean, children: string) =>
        generatePageHTML(blockId, block, isLiquid, children),
    section: (blockId: string, block: Auto_BlockData, isLiquid: boolean, children?: string) =>
        generateSectionHTML(blockId, block, isLiquid, children as string),
    form: (blockId: string, block: Auto_BlockData, isLiquid: boolean) => generateFormHTML(blockId, block, isLiquid),
    button: async (blockId: string, block: Auto_BlockData) => await generateButtonHTML(blockId, block),
    shape: async (blockId: string, block: Auto_BlockData) => await generateShapeHTML(blockId, block),
});
