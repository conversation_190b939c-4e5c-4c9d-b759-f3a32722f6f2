/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import { BlockStack, Button } from '@shopify/polaris';
import { PlusCircleIcon } from '@shopify/polaris-icons';
import { genRandomBlockId, useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base';
import { SettingsSortable } from '@/components/builder/settings';
import { SettingsToggle } from '@/components/builder/settings/SettingsToggle';
import { BaseNoBorderLayout } from '@/components/builder/base/BaseNoBorderLayout';
import { SettingsRadioGroup } from '@/components/builder/settings/SettingsRadioGroup';
import { SettingsSliderInput } from '@/components/builder/settings/SettingsSliderInput';
import { AccordionRenderer } from '@/components/builder/settings/SettingsSortable/renderer/AccordionRenderer';
import { configsChooseDefault } from '../Basic/configs';
import './styles.scss';

const DEFAULT_PATH_CONFIGS_CONTENT = 'content';

export const Contents = () => {
    const [loading, setLoading] = useState(false);
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;

    const defaultAccordion = getBlockProperty(
        `configs.${DEFAULT_PATH_CONFIGS_CONTENT}.defaultAccordion`,
        selectedBlockId,
    );

    const applyTransitionEffect = getBlockProperty(
        `configs.${DEFAULT_PATH_CONFIGS_CONTENT}.applyTransition.effect`,
        selectedBlockId,
    );
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const handleAddAccordion = async () => {
        setLoading(true);
        try {
            const blockData = getBlockProperty(`configs.blockData`, selectedBlockId) || [];
            const newItem = {
                title: 'New Accordion',
                content: 'Add your content here',
                id: genRandomBlockId(),
            };
            const newBlockData = [...blockData, newItem];
            await updateBlockConfigsProperty(selectedBlockId, 'blockData', newBlockData as any);
        } finally {
            setLoading(false);
        }
    };

    return (
        <BaseNoBorderLayout>
            <BaseItemLayout
                direction="column"
                textProps={{ as: 'p', variant: 'bodyMd', fontWeight: 'medium', children: 'Accordion' }}
                containerClassName="settings-contents-accordion"
            >
                <BlockStack gap="300">
                    <div className="contents-accordion">
                        <SettingsSortable
                            blockId={selectedBlockId}
                            path="blockData"
                            isUpdateConfigs
                            containerProps={{ className: 'accordion-sortable' }}
                            itemRenderer={(props) => <AccordionRenderer {...props} blockId={selectedBlockId} />}
                        />
                        <Button
                            loading={loading}
                            icon={PlusCircleIcon}
                            variant="plain"
                            size="slim"
                            onClick={handleAddAccordion}
                        >
                            Add
                        </Button>
                    </div>

                    <SettingsRadioGroup
                        data={configsChooseDefault}
                        blockId={selectedBlockId}
                        path={`${DEFAULT_PATH_CONFIGS_CONTENT}.defaultAccordion`}
                        isUpdateConfigs={true}
                        label="Select default state"
                        textProps={{ as: 'p', variant: 'bodyMd', fontWeight: 'medium' }}
                    />
                    {defaultAccordion !== 'all-items-opened' && (
                        <SettingsToggle
                            blockId={selectedBlockId}
                            path={`${DEFAULT_PATH_CONFIGS_CONTENT}.allowMultipleOpen`}
                            label="Allow multiple items"
                            isUpdateConfigs={true}
                            toggleProps={{
                                id: 'allow-multiple-items',
                            }}
                            textProps={{ as: 'p', variant: 'bodyMd', fontWeight: 'medium' }}
                        />
                    )}

                    <SettingsToggle
                        blockId={selectedBlockId}
                        path={`${DEFAULT_PATH_CONFIGS_CONTENT}.applyTransition.effect`}
                        label="Apply transition effects"
                        isUpdateConfigs={true}
                        toggleProps={{
                            id: 'apply-transition-effect',
                        }}
                        textProps={{ as: 'p', variant: 'bodyMd', fontWeight: 'medium' }}
                    />
                    {applyTransitionEffect && (
                        <SettingsSliderInput
                            path={`${DEFAULT_PATH_CONFIGS_CONTENT}.applyTransition.duration`}
                            blockId={selectedBlockId}
                            isUpdateConfigs={true}
                            title="Time"
                            direction="column"
                            max={10}
                            step={0.1}
                            min={0}
                            inputProps={{
                                align: 'center',
                                step: 0.1,
                                min: 0,
                            }}
                        />
                    )}
                </BlockStack>
            </BaseItemLayout>
        </BaseNoBorderLayout>
    );
};
