import { genRandomBlockId } from '@giaminhautoketing/auto-builder';

type valueUnit = {
    val: string;
    unit: string;
};

interface HtmlCodeToolbarOption {
    id: string;
    cname: string;
    label: string;
    type: string;
    configs: {
        content: string;
        displayOnDesktop: boolean;
        displayOnMobile: boolean;
    };
    bpConfigs: {
        [key in 'desktop' | 'tablet' | 'mobile']: {
            width: valueUnit;
            height: valueUnit;
            alignment: {
                alignSelf: 'left' | 'right' | 'center';
                justifySelf: 'top' | 'bottom' | 'center';
            };
        };
    };
    overlay: {
        [key in 'desktop' | 'tablet' | 'mobile']: {
            width: number;
            height: number;
        };
    };
}

export const htmlCodeToolbarOptions: HtmlCodeToolbarOption[] = [
    {
        id: genRandomBlockId(),
        cname: 'html-code',
        label: 'HTML code',
        type: 'html-code',
        configs: {
            content: '',
            displayOnDesktop: true,
            displayOnMobile: true,
        },
        bpConfigs: {
            desktop: {
                width: { val: '306', unit: 'px' },
                height: { val: '180', unit: 'px' },
                alignment: {
                    alignSelf: 'left',
                    justifySelf: 'top',
                },
            },
            tablet: {
                width: { val: '306', unit: 'px' },
                height: { val: '180', unit: 'px' },
                alignment: {
                    alignSelf: 'left',
                    justifySelf: 'top',
                },
            },
            mobile: {
                width: { val: '306', unit: 'px' },
                height: { val: '180', unit: 'px' },
                alignment: {
                    alignSelf: 'left',
                    justifySelf: 'top',
                },
            },
        },
        overlay: {
            desktop: {
                width: 306,
                height: 180,
            },
            tablet: {
                width: 306,
                height: 180,
            },
            mobile: {
                width: 306,
                height: 180,
            },
        },
    },
];
