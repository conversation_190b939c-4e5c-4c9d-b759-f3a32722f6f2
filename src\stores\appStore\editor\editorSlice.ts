import { StateCreator } from 'zustand';
import { EditorState, EditorActions } from '@/stores/appStore/editor/types';
import { AppState, AppActions } from '@/stores/appStore/types';
import { FormField } from '@/components/builder/blocks/form/types';

const MIN_ZOOM = 0.1;
const MAX_ZOOM = 3;
const ZOOM_STEP = 0.1;

export const DEVICE_WIDTHS = {
    desktop: 1140,
    mobile: 480,
};

export const createEditorSlice: StateCreator<AppState & AppActions, [], [], EditorState & EditorActions> = (set) => ({
    zoomLevel: 1,
    screenWidth: DEVICE_WIDTHS.desktop,
    currentDevice: 'desktop',
    isOpenModalShape: {
        isOpen: false,
        tabs: 0,
    },
    selectedFormField: null,
    isAddNewFormField: false,
    tempTab: {},
    setTempTab: (blockId: string, activeId: string) =>
        set((state) => ({
            ...state,
            tempTab: {
                ...state.tempTab,
                [blockId]: { activeId },
            },
        })),

    setZoomLevel: (level: number) =>
        set((state) => {
            const baseWidth = DEVICE_WIDTHS[state.currentDevice];
            const newZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, level));

            return {
                ...state,
                zoomLevel: newZoom,
                screenWidth: Math.round(baseWidth / newZoom),
            };
        }),

    zoomIn: () =>
        set((state) => {
            const baseWidth = DEVICE_WIDTHS[state.currentDevice];
            const newZoom = Math.min(MAX_ZOOM, state.zoomLevel + ZOOM_STEP);
            return {
                ...state,
                zoomLevel: newZoom,
                screenWidth: Math.round(baseWidth / newZoom),
            };
        }),

    zoomOut: () =>
        set((state) => {
            const baseWidth = DEVICE_WIDTHS[state.currentDevice];
            const newZoom = Math.max(MIN_ZOOM, state.zoomLevel - ZOOM_STEP);
            return {
                ...state,
                zoomLevel: newZoom,
                screenWidth: Math.round(baseWidth / newZoom),
            };
        }),

    resetZoom: () =>
        set((state) => {
            const baseWidth = DEVICE_WIDTHS[state.currentDevice];
            return {
                ...state,
                zoomLevel: 1,
                screenWidth: baseWidth,
            };
        }),

    setScreenWidth: (width: number) =>
        set((state) => {
            const baseWidth = DEVICE_WIDTHS[state.currentDevice];
            return {
                ...state,
                screenWidth: width,
                zoomLevel: width < baseWidth ? 1 : baseWidth / width,
            };
        }),

    setDeviceType: (deviceType: keyof typeof DEVICE_WIDTHS) =>
        set(() => {
            const width = DEVICE_WIDTHS[deviceType];
            return {
                currentDevice: deviceType,
                zoomLevel: 1,
                screenWidth: width,
            };
        }),

    setIsOpenModalShape: (isOpen: boolean, tabs?: number) =>
        set((state) => ({
            ...state,
            isOpenModalShape: {
                isOpen,
                tabs: tabs,
            },
        })),

    setSelectedFormField: (formField: FormField) =>
        set((state) => ({
            ...state,
            selectedFormField: formField,
        })),

    setIsAddNewFormField: (isAddNewFormField: boolean) =>
        set((state) => ({
            ...state,
            isAddNewFormField: isAddNewFormField,
        })),
});
