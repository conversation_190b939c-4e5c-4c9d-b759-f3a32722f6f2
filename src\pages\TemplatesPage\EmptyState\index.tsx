import { FC } from 'react';
import { BlockStack, Text } from '@shopify/polaris';
import imgDataEmptyUrl from '@/assets/svgs/search-empty.svg?url';

interface EmptyStateProps {
    title?: string;
    description?: string;
}

export const EmptyState: FC<EmptyStateProps> = ({ title, description }) => {
    return (
        <div css={{ display: 'flex', alignItems: 'center', justifyContent: 'center', paddingBlock: '80px' }}>
            <BlockStack inlineAlign="center">
                <img src={imgDataEmptyUrl} alt="" />
                <BlockStack gap="150">
                    <Text as="h3" alignment="center" variant="headingMd">
                        {title}
                    </Text>
                    <Text as="p" alignment="center" variant="bodySm">
                        {description}
                    </Text>
                </BlockStack>
            </BlockStack>
        </div>
    );
};
