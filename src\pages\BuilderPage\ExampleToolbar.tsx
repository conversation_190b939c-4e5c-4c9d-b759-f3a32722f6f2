import { FC } from 'react';
import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { exampleToolbarOptions } from './exampleConfigs';

export const ExampleToolbar: FC = () => {
    return (
        <div>
            {exampleToolbarOptions.map((option) => (
                <BlockToolbar
                    {...option}
                    key={option.id}
                    style={{
                        width: '100%',
                        height: '80px',
                        background: 'while',
                        borderRadius: '8px',
                        border: '1px solid #e5e5e5',
                        cursor: 'move',
                        display: 'flex',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        alignItems: 'center',
                    }}
                >
                    {option.label}
                </BlockToolbar>
            ))}
        </div>
    );
};
