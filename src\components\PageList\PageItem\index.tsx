import { useState, type FC } from 'react';
import { Badge, BadgeProps, BlockStack, Box, InlineStack, ResourceItem, Text, TextField } from '@shopify/polaris';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { capitalizeFirstLetter } from '@/utils';
import { ApiActionConfig, PageItemProps } from '@/components/PageList/types';
import imgDataEmptyUrl from '@/assets/svgs/empty-image.svg?url';
import { PageItemActions } from '@/components/PageList/PageItem/PageItemActions';
import { RequestName } from '@/stores/appStore/pages/types';
import { useRequestStatus } from '@/pages/BuilderPage/hooks/editor/useRequestStatus';
import { BuilderModal } from '@/pages/BuilderPage/components/Modals/BuilderModal';
import './style.scss';

export const PageItem: FC<PageItemProps> = (props) => {
    const {
        title,
        updatedAt,
        status,
        pageType,
        id,
        thumbnail,
        selectMode,
        isHomePage,
        setSelectedItems,
        popoverActive,
        setPopoverActive,
    } = props;
    const updatePage = useAppStore((state) => state.updatePage);
    const deletePage = useAppStore((state) => state.deletePage);
    const unfavoriteTemplate = useAppStore((state) => state.unfavoriteTemplate);
    const duplicatePage = useAppStore((state) => state.duplicatePage);
    const exportPage = useAppStore((state) => state.exportPage);

    const currentParams = useAppStore((state) => state.currentParams);

    const { getStatus, isLoading } = useRequestStatus();
    const [openModal, setOpenModal] = useState(false);
    const [modalState, setModalState] = useState({
        kind: 'rename',
        inputText: '',
    });

    const contentsMap: {
        [key: string]: {
            title: string;
            message: React.ReactNode;
            cancelText: string;
            okText: string;
            showInput?: boolean;
            disabled?: boolean;
        };
    } = {
        export: {
            title: 'Export page',
            message: <p>Do you want to export selected page?</p>,
            cancelText: 'No',
            okText: 'Yes, export',
        },
        rename: {
            title: 'Rename this page',
            message: null,
            cancelText: 'Cancel',
            okText: 'Confirm',
            showInput: true,
            disabled: modalState.inputText === title || !modalState.inputText,
        },
        duplicate: {
            title: 'Duplicate',
            message: (
                <p>
                    Are you sure you want to duplicate{' '}
                    <Text as="span" variant="bodyMd" fontWeight="medium">
                        "{title}"
                    </Text>{' '}
                    ?
                </p>
            ),
            cancelText: 'Cancel',
            okText: 'Confirm',
            showInput: true,
        },
        delete: {
            title: 'Delete page',
            message: (
                <p>
                    Are you sure you want to delete page{' '}
                    <Text as="span" variant="bodyMd" fontWeight="medium">
                        "{title}"
                    </Text>{' '}
                    ?
                </p>
            ),
            cancelText: 'Cancel',
            okText: 'Delete',
        },
    };

    const toneMap: Record<string, string> = {
        published: 'success',
        draft: 'info',
        favorite: 'default',
        default: 'default',
    };

    const handleActionPopover = (kind: string) => {
        setModalState((prev) => ({
            ...prev,
            kind: kind,
            inputText: kind === 'duplicate' ? `Copy of ${title}` : title,
        }));
        setOpenModal(true);
    };

    const handleApiAction = async ({ action, successMsg, errorMsg, onSuccess, requestName }: ApiActionConfig) => {
        if (!id) return;
        try {
            const response = await action();
            if (getStatus(requestName) === 'success' || response) {
                setOpenModal(false);
                setPopoverActive(false);
                setSelectedItems?.([]);
                shopify.toast.show(successMsg, { duration: 1500 });
                onSuccess?.(response);
            }
        } catch (error) {
            console.error(`${errorMsg}:`, error);
            shopify.toast.show(errorMsg, { duration: 1500, isError: true });
            setOpenModal(false);
        }
    };

    const handleRename = () =>
        handleApiAction({
            action: () => updatePage(id.toString(), { title: modalState.inputText }, currentParams),
            successMsg: 'Page renamed successfully',
            errorMsg: 'Rename failed',
            requestName: 'updatePage',
        });

    const handleDuplicate = () =>
        handleApiAction({
            action: () => duplicatePage(id.toString(), { title: modalState.inputText }, currentParams),
            successMsg: 'Page duplicated successfully',
            errorMsg: 'Duplicate failed',
            requestName: 'duplicatePage',
        });

    const handleDelete = () =>
        handleApiAction({
            action: () =>
                currentParams.status === 3
                    ? unfavoriteTemplate([id.toString()], currentParams)
                    : deletePage([id.toString()], currentParams),
            successMsg: currentParams.status === 3 ? 'Removed from favorites' : 'Page deleted successfully',
            errorMsg: currentParams.status === 3 ? 'Unfavorite failed' : 'Delete failed',
            requestName: currentParams.status === 3 ? 'unfavoriteTemplate' : 'deletePage',
        });

    const handleExport = () =>
        handleApiAction({
            action: async () => {
                const response = await exportPage([id.toString()], currentParams);
                setTimeout(() => {
                    if (response?.data?.downloadUrl) {
                        window.open(response.data.downloadUrl, '_blank');
                    }
                }, 1500);
                return response;
            },
            successMsg: 'Page exported',
            errorMsg: 'Export failed',
            requestName: 'exportPage',
        });

    const media = thumbnail ? (
        <img className="page-item__image" src={`${import.meta.env.VITE_BASE_URL}${thumbnail}`} alt={title} />
    ) : (
        <img className="page-item__image" src={imgDataEmptyUrl} alt={title} />
    );

    const handleOpenBuilder = (templateId: string) => {
        localStorage.setItem('templateId', templateId);
        shopify.modal.show('builder-modal');
    };

    const getRequestNameFromModalKind = (kind: string): RequestName => {
        switch (kind) {
            case 'rename':
                return 'updatePage';
            case 'delete':
                return currentParams.status === 3 ? 'unfavoriteTemplate' : 'deletePage';
            case 'duplicate':
                return 'duplicatePage';
            case 'export':
                return 'exportPage';
            default:
                return '';
        }
    };

    return (
        <div
            className="page-item"
            onClickCapture={(e) => {
                const target = e.target as HTMLElement;
                const stopPropagation = selectMode && !(target.tagName === 'INPUT') && !target.closest('button');
                if (stopPropagation) {
                    e.stopPropagation();
                }
            }}
        >
            <ResourceItem
                id={id.toString()}
                media={media}
                onClick={handleOpenBuilder}
                disabled={status === 'default'}
                verticalAlignment="center"
            >
                <div className="page-item__content">
                    <BlockStack gap="150">
                        <InlineStack gap="300">
                            <Text as="h3" variant="headingMd" fontWeight="bold" breakWord>
                                {title}
                            </Text>
                            {currentParams.status !== 3 && (
                                <Badge tone={toneMap[status] as BadgeProps['tone']}>
                                    {capitalizeFirstLetter(String(status))}
                                </Badge>
                            )}
                        </InlineStack>
                        <Text as="p" variant="bodyMd">
                            {pageType}
                        </Text>
                        <Text as="span" variant="bodyMd">
                            {updatedAt}
                        </Text>
                    </BlockStack>
                </div>
            </ResourceItem>
            <PageItemActions
                isHomePage={isHomePage ?? false}
                status={status}
                popoverActive={popoverActive}
                setPopoverActive={setPopoverActive}
                handleActionPopover={handleActionPopover}
                id={id}
            />
            <Modal open={openModal} onHide={() => setOpenModal(false)}>
                <Box padding="400" paddingBlockEnd="800">
                    <BlockStack {...(modalState.kind === 'duplicate' && { gap: '500' })}>
                        {contentsMap[modalState.kind].message}
                        {contentsMap[modalState.kind].showInput && (
                            <div className="page-item__text-field-wrapper">
                                <TextField
                                    label="Page title"
                                    value={modalState.inputText}
                                    onChange={(inputText) => setModalState((prev) => ({ ...prev, inputText }))}
                                    autoComplete="off"
                                    autoFocus
                                    clearButton
                                    maxLength={255}
                                    showCharacterCount
                                    onClearButtonClick={() => setModalState((prev) => ({ ...prev, inputText: '' }))}
                                />
                            </div>
                        )}
                    </BlockStack>
                </Box>
                <TitleBar title={contentsMap[modalState.kind].title}>
                    <button onClick={() => setOpenModal(false)}>{contentsMap[modalState.kind].cancelText}</button>
                    <button
                        variant="primary"
                        tone={modalState.kind === 'delete' ? 'critical' : 'default'}
                        disabled={contentsMap[modalState.kind].disabled}
                        onClick={() => {
                            if (modalState.kind === 'rename') {
                                handleRename();
                            }
                            if (modalState.kind === 'delete') {
                                handleDelete();
                            }
                            if (modalState.kind === 'duplicate') {
                                handleDuplicate();
                            }
                            if (modalState.kind === 'export') {
                                handleExport();
                            }
                        }}
                        loading={isLoading(getRequestNameFromModalKind(modalState.kind)) ? '' : undefined}
                    >
                        {contentsMap[modalState.kind].okText}
                    </button>
                </TitleBar>
            </Modal>
            <BuilderModal />
        </div>
    );
};
