import { FC } from 'react';
import { Box, BlockStack } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BorderWeight } from '@/components/builder/settings/SettingBorder/components/BorderWeight';
import { BorderRadius } from '@/components/builder/settings/SettingBorder/components/BorderRadius';

interface SettingBorderProps {
    isUpdateConfigs?: boolean;
    path: string;
    blockId: string;
    label?: string;
}

export const SettingBorder: FC<SettingBorderProps> = ({ isUpdateConfigs, path, blockId }) => {
    return (
        <Box paddingBlockStart="400">
            <BlockStack gap="400">
                <BaseItemLayout direction="column" textProps={{ as: 'p', variant: 'bodyMd', children: 'Weight' }}>
                    <BorderWeight isUpdateConfigs={isUpdateConfigs} path={path} blockId={blockId} />
                </BaseItemLayout>
                <BaseItemLayout direction="column" textProps={{ as: 'p', variant: 'bodyMd', children: 'Radius' }}>
                    <BorderRadius isUpdateConfigs={false} path={`${path}.radius`} blockId={blockId} />
                </BaseItemLayout>
            </BlockStack>
        </Box>
    );
};
