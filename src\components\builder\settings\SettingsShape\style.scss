.settings-shape {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.75rem;
    &__buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        height: 30px;
        width: 100%;
        > .Polaris-But<PERSON> {
            width: 50%;
        }
        .Polaris-DropZone--sizeMedium {
            min-height: 28px;
        }
        .Polaris-Labelled--hidden {
            width: 50%;
            .Polaris-Button {
                width: 100%;
            }
        }
    }
}
