import { useState } from 'react';
import clsx from 'clsx';
import { useMutation, useQuery, useScrollToLoadmore } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
import { useMediaManager } from '../../useMediaManager';
import { EmptyState, MediaGrid } from '../../components';
import { MediaType, SystemMeidaResponse } from '../../types';

export const Autoketing = () => {
    const [items, setItems] = useState<MediaType[]>([]);
    const [hasMore, setHasMore] = useState(false);
    const page = useMediaManager((state) => state.page);
    const sortBy = useMediaManager((state) => state.sortBy);
    const setPage = useMediaManager((state) => state.setPage);
    const setCurrentTab = useMediaManager((state) => state.setCurrentTab);

    const limit = 20;
    const type = 'atk';

    const { isLoading: loading } = useQuery<SystemMeidaResponse>({
        url: clsx(
            apiAddress.media.index,
            `?type=${type}`,
            `&currentPage=${page}`,
            `&perPage=${limit}`,
            sortBy && sortBy !== 'all' && `&mediaType=${sortBy}`,
        ).replace(/\s+/g, ''),
        method: 'GET',
        onSuccess(data) {
            const { edges, pageInfo } = data.result.data;
            setItems(page === 1 ? edges : (prev) => [...prev, ...edges]);
            setHasMore(pageInfo.hasNextPage);
        },
    });

    const mutation = useMutation({
        url: apiAddress.media.index,
        method: 'POST',
        timeout: 50000,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        onMutate: () => {
            shopify.toast.show('Uploading...');
        },
        onSuccess: () => {
            shopify.toast.show('Uploaded successfully');
            setCurrentTab('upload');
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
    });

    const loadMoreItems = () => {
        if (loading || !hasMore) return;
        setPage(page + 1);
    };

    const { containerRef } = useScrollToLoadmore({
        hasMore,
        loading,
        loadmore: loadMoreItems,
        resetDependencies: [sortBy],
    });

    return (
        <MediaGrid
            items={items}
            loading={loading}
            containerRef={containerRef}
            renderEmptyState={
                <EmptyState
                    onDrop={(acceptedFiles) => {
                        const formData = new FormData();
                        formData.append('mediaType', sortBy === 'all' ? 'image' : sortBy);
                        acceptedFiles.forEach((file) => {
                            formData.append('files[]', file);
                        });
                        mutation.mutate(formData);
                    }}
                    accept={sortBy === 'all' || sortBy === 'image' ? 'image/*' : 'video/*'}
                />
            }
        />
    );
};
