import { Icon, IconSource, Tooltip } from '@shopify/polaris';
import clsx from 'clsx';

interface BaseToolProps {
    icon?: IconSource;
    tooltip?: string;
    isActive?: boolean;
    customIcon?: React.ReactNode;
    onClick?: () => void;
}

export const BaseTool = ({ onClick, icon, tooltip, isActive, customIcon }: BaseToolProps) => {
    return (
        <Tooltip content={tooltip} dismissOnMouseOut>
            <button onClick={onClick} className={clsx('Polaris-Button', 'rte-tool', isActive && 'active')}>
                {customIcon ? customIcon : icon ? <Icon source={icon} /> : null}
            </button>
        </Tooltip>
    );
};
