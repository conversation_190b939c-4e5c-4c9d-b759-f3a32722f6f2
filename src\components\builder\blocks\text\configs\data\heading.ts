import { Auto_BlockToolbar, genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const heading: Auto_BlockToolbar = {
    id: genRandomBlockId(),
    cname: 'text',
    label: 'Text',
    type: 'text',
    configs: {
        content: '<h1>Heading</h1>',
        displayOnDesktop: true,
        displayOnMobile: true,
        animation: {
            type: 'none',
            duration: { val: '0', unit: 's' },
            loop: '1',
            delay: { val: '0', unit: 's' },
        },
    },
    bpConfigs: {
        desktop: {
            alignment: {
                alignSelf: 'start',
                justifySelf: 'start',
            },
            background: {
                type: 'color',
                color: '#6daeff',
                image: {
                    url: 'https://images.unsplash.com/photo-1746730341411-03b1f6b8f1f0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                    repeat: 'no',
                    position: 'center center',
                    attachment: 'fixed',
                    fill: 'cover',
                },
            },
            color: '#BD3C3C',
            width: { val: '120', unit: 'px' },
            height: 'auto',
            fontFamily: '',
            fontWeight: '500',
            fontSize: { val: '20', unit: 'px' },
            fontStyle: 'default',
            textAlign: 'left',
            textDecoration: 'default',
            textTransform: 'default',
            textColor: '#000000',
            lineHeight: { val: '1', unit: '' },
            letterSpacing: { val: '0', unit: 'px' },
        },
        mobile: {
            alignment: {
                alignSelf: 'start',
                justifySelf: 'start',
            },
            background: {
                type: 'color',
                color: '#6daeff',
                image: {
                    url: 'https://images.unsplash.com/photo-1746730341411-03b1f6b8f1f0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                    repeat: 'no',
                    position: 'center center',
                    attachment: 'fixed',
                    fill: 'cover',
                },
            },
            color: '#ffffff',
            width: { val: '120', unit: 'px' },
            height: { val: '40', unit: 'px' },
        },
    },
    overlay: {
        desktop: {
            width: 120,
            height: 40,
        },
        mobile: {
            width: 120,
            height: 40,
        },
    },
};
