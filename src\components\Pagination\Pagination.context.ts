import { createSafeContext } from '@/utils';
import { ButtonProps } from '@shopify/polaris';

interface PaginationContext {
    total: number;
    range: (number | 'dots')[];
    active: number;
    disabled: boolean | undefined;
    size: ButtonProps['size'];
    onChange: (page: number) => void;
    onNext: () => void;
    onPrevious: () => void;
    onFirst: () => void;
    onLast: () => void;
    hasNext: boolean;
    hasPrevious: boolean;
}

export const [PaginationProvider, usePaginationContext] = createSafeContext<PaginationContext>(
    'Pagination.Root component was not found in tree',
);
