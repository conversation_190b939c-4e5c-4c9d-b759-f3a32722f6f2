import axios from 'axios';
import { useAppStore } from '@/stores';

export const httpRequest = axios.create({
    baseURL: import.meta.env.BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});

httpRequest.interceptors.request.use(
    (config) => {
        const accessToken = useAppStore.getState().token;
        if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);

httpRequest.interceptors.response.use(
    (response) => {
        // You can modify the response data here
        console.log('Response received:', response.status);
        return response;
    },
    (error) => {
        // Handle errors globally
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            console.error('Response error:', error.response.status, error.response.data);

            // Handle specific status codes
            switch (error.response.status) {
                case 401:
                    // Handle unauthorized
                    console.error('Unauthorized access');
                    break;
                case 404:
                    // Handle not found
                    console.error('Resource not found');
                    break;
                default:
                    // Handle other errors
                    break;
            }
        } else if (error.request) {
            // The request was made but no response was received
            console.error('Request error:', error.request);
        } else {
            // Something happened in setting up the request that triggered an Error
            console.error('Error:', error.message);
        }

        return Promise.reject(error);
    },
);
