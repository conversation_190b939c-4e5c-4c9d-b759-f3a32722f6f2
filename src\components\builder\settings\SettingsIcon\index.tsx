/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useCallback } from 'react';
import { TextProps, InlineStack, Button, DropZone } from '@shopify/polaris';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseIcon } from '@/components/builder/base/BaseIcon';
import { ReactComponent as ChangeIcon } from '@/assets/svgs/changeColor.svg';
import { IconModal } from './components/IconModal';
import { useAppStore } from '@/stores/appStore';
import './style.scss';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { useMutation } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseIconProps = Parameters<typeof BaseIcon>[0];

interface SettingsIconProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    iconProps?: Omit<BaseIconProps, 'icon'>;
}

export const SettingsIcon: FC<SettingsIconProps> = ({
    path,
    blockId,
    isUpdateConfigs,
    textProps,
    label,
    iconProps,
    ...otherProps
}) => {
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const iconValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const isOpenModalShape = useAppStore((state) => state.isOpenModalShape);
    const setIsOpenModalShape = useAppStore((state) => state.setIsOpenModalShape);

    const handleChangeIcon = useCallback(() => {
        setIsOpenModalShape(true, 0);
    }, [setIsOpenModalShape]);

    const mutation = useMutation({
        url: apiAddress.shape,
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        timeout: 50000,
        onMutate: () => {
            shopify.toast.show('Uploading...');
        },
        onSuccess: (response: any) => {
            const newSvg = response?.result?.data?.[0]?.value || '';
            updateBlockConfigsProperty(blockId, path, newSvg);
            shopify.toast.show('Uploaded successfully');
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
    });

    const handleUploadIcon = (files: File[]) => {
        const formData = new FormData();
        formData.append('mediaType', 'svg');
        files.forEach((file) => {
            formData.append('files[]', file);
        });
        mutation.mutate(formData);
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };
    return (
        <>
            <BaseItemLayout
                containerClassName="settings-icon"
                textProps={{ ...defaultTextProps, ...textProps } as TextProps}
                {...otherProps}
            >
                <InlineStack align="space-between" blockAlign="center" gap="200">
                    <div
                        css={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '48px',
                            height: '48px',
                            borderRadius: '4px',
                            backgroundColor: '#F1F1F1',
                            cursor: 'pointer',
                        }}
                    >
                        <BaseIcon source={iconValue} {...iconProps} size={32} color="#4a4a4a" />
                    </div>
                    <InlineStack align="space-between" blockAlign="center" gap="200">
                        <Button icon={<ChangeIcon />} variant="plain" onClick={handleChangeIcon} />
                        <DropZone
                            accept="image/svg+xml"
                            type="image"
                            allowMultiple={false}
                            outline={false}
                            errorOverlayText="File type must be .svg"
                            onDrop={handleUploadIcon}
                        >
                            <DropZone.FileUpload />
                        </DropZone>
                    </InlineStack>
                </InlineStack>
            </BaseItemLayout>
            {isOpenModalShape && (
                <IconModal
                    blockId={blockId}
                    path={path}
                    isUpdateConfigs={isUpdateConfigs}
                    isOpenModalShape={isOpenModalShape.isOpen}
                    onOpenModalShape={setIsOpenModalShape}
                />
            )}
        </>
    );
};
