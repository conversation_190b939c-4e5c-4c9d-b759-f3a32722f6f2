export const normalizeSvg = (svgContent: string) => {
    let normalized = svgContent.replace(/<svg([^>]*)>/, '<svg$1 style="width: 100%; height: 100%;">');

    normalized = normalized.replace(/<path([^>]*)>/g, '<path$1></path>');

    normalized = normalized.replace(/<svg([^>]*)>/, (_match, attributes) => {
        const attrs = {
            xmlns: 'http://www.w3.org/2000/svg',
            width: attributes.match(/width="([^"]*)"/)?.[1] || '16',
            height: attributes.match(/height="([^"]*)"/)?.[1] || '16',
            viewBox: attributes.match(/viewBox="([^"]*)"/)?.[1] || '0 0 16 16',
            fill: attributes.match(/fill="([^"]*)"/)?.[1] || 'none',
            style: 'width: 100%; height: 100%;',
        };
        return `<svg xmlns="${attrs.xmlns}" width="${attrs.width}" height="${attrs.height}" viewBox="${attrs.viewBox}" fill="${attrs.fill}" style="${attrs.style}">`;
    });

    return normalized;
};
