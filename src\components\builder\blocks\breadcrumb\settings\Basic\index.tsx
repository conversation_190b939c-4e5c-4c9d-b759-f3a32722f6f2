import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { SettingElementID } from '@/components/builder/settings';
import { Styles } from './components/Styles';
import { Typography } from './components/Typography';
import { Icons } from './components/Icon';
// import { States } from './components/States';
import { Space } from './components/Space';
export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);
    return (
        <BaseHasBorderLayout hasElementID>
            <SettingElementID value={selectedBlockId || ''} />
            <Typography id={selectedBlockId || ''} />
            <Icons id={selectedBlockId || ''} />
            <Styles id={selectedBlockId || ''} />
            {/* <States id={selectedBlockId || ''} /> */}
            <Space id={selectedBlockId || ''} />
        </BaseHasBorderLayout>
    );
};
