import { FC } from 'react';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { ElementID } from '@/components/builder/settings/components/ElementID';

interface SettingElementIDProps {
    value: string;
}
export const SettingElementID: FC<SettingElementIDProps> = ({ value }) => {
    return (
        <BaseItemLayout textProps={{ as: 'p', children: 'Element ID', fontWeight: 'medium' }}>
            <ElementID value={value} />
        </BaseItemLayout>
    );
};
