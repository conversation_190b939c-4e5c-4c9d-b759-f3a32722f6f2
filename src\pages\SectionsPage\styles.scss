.sections-wrapper {
    .Polaris-ResourceList__HeaderWrapper {
        padding-inline: 16px;
    }
    .Polaris-ResourceList__HeaderContentWrapper {
        left: 16px;
    }
    .Polaris-IndexFilters__IndexFiltersWrapper {
        .Polaris-Filters__FiltersWrapper {
            height: auto !important;
            overflow: visible !important;
            .Polaris-Filters-FilterPill__ToggleButton {
                height: auto;
                min-height: 22px;
                .Polaris-Text--root {
                    text-align: start;
                    text-wrap: auto;
                }
            }
        }
        .Polaris-TextField__Input {
            font-size: var(--p-font-size-325);
            line-height: var(--p-font-line-height-500);
        }
    }
    @media (min-width: 48em) {
        .Polaris-ResourceList__HeaderWrapper {
            min-height: 44px;
        }
    }
}

.pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 16px 16px 20px;
    border-top: 0.0625rem solid rgba(227, 227, 227, 1);
    .<PERSON>is-Button--disabled {
        background-color: rgba(0, 0, 0, 0.04);
    }
}

.skeleton {
    &-header {
        width: 16.66667%;
        padding: 16px;
    }
    &-item {
        padding: 22px 16px;
        border-bottom: 1px solid rgba(235, 235, 235, 1);
    }
    &-footer {
        width: 16.66667%;
        padding: 32px 16px;
        margin-left: auto;
    }
}

.hidden {
    display: none;
}
