import { FC, useState } from 'react';
import { Text } from '@shopify/polaris';
import { DeleteIcon, DuplicateIcon, EditIcon, ExportIcon, PageRemoveIcon, PageUpIcon } from '@shopify/polaris-icons';
import { GenericResourceItem, GenericResourceItemProps } from '@/components';
import { useMutation } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
import { Section } from '@/pages/SectionsPage/types';
import imgNull from '@/assets/svgs/empty-image.svg?url';

interface SectionItemProps {
    data: Section;
    selectMode?: boolean;
    refetch?: () => void;
    __type__?: unknown;
}

export const SectionItem: FC<SectionItemProps> = (props) => {
    const { selectMode, data, refetch } = props;
    const [openModal, setOpenModal] = useState(false);

    const renameMutation = useMutation({
        url: `${apiAddress.shopSections.rename}/${data.id}`,
        method: 'PUT',
        onSuccess: () => {
            shopify.toast.show('Section renamed successfully');
            setOpenModal(false);
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to rename section', { isError: true });
        },
    });

    const duplicateMutation = useMutation({
        url: `${apiAddress.shopSections.duplicate}/${data.id}`,
        method: 'POST',
        onSuccess: () => {
            shopify.toast.show('Section duplicated successfully');
            setOpenModal(false);
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to duplicate section', { isError: true });
        },
    });

    const deleteMutation = useMutation({
        url: apiAddress.shopSections.index,
        method: 'DELETE',
        onSuccess: () => {
            shopify.toast.show('Section deleted successfully');
            setOpenModal(false);
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to delete section', { isError: true });
        },
    });

    const statusMutation = useMutation({
        url: apiAddress.shopSections.status,
        method: 'PUT',
        onSuccess: () => {
            shopify.toast.show('Section status updated successfully');
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to update section status', { isError: true });
        },
    });

    const renderers: GenericResourceItemProps<Section>['renderers'] = {
        id: (section) => section.id,
        title: (section) => section.title,
        badge: () => ({ content: data.status, tone: data.status === 'Published' ? 'success' : undefined }),
        subtitle: () => '0 pages',
        metadata: () => (
            <Text as="span" variant="bodyMd">
                Last modified - Monday at 2:49 PM
            </Text>
        ),
        media: (data) => (
            <img
                src={`${import.meta.env.VITE_BASE_URL}${data.thumbnail}` || imgNull}
                alt={data.title}
                css={{
                    width: '90px',
                    height: '70px',
                    objectFit: 'contain',
                    objectPosition: 'center',
                    marginRight: '4px',
                    borderRadius: '6px',
                    border: '1px solid rgb(225, 226, 227)',
                }}
            />
        ),
    };

    const actions: GenericResourceItemProps<Section>['actions'] = [
        {
            content: 'Rename',
            icon: EditIcon,
            popoverAction: true,
        },
        {
            content: 'Duplicate',
            icon: DuplicateIcon,
            popoverAction: true,
        },
        {
            content: 'Publish',
            icon: PageUpIcon,
            disabled: data.status === 'Published',
            onAction: (section) => {
                statusMutation.mutate({
                    ids: [section.id],
                    status: 'Published',
                });
            },
        },
        {
            content: 'Unpublish',
            icon: PageRemoveIcon,
            disabled: data.status === 'Unpublished',
            onAction: (section) => {
                statusMutation.mutate({
                    ids: [section.id],
                    status: 'Unpublished',
                });
            },
        },
        {
            content: 'Export',
            icon: ExportIcon,
            popoverAction: true,
        },
        {
            content: 'Delete',
            icon: DeleteIcon,
            destructive: true,
            popoverAction: true,
        },
    ];

    const modalConfigs = {
        rename: {
            title: 'Rename this section',
            cancelText: 'Cancel',
            okText: 'Confirm',
            showInput: true,
            disableOkWhen: (inputText: string, originalTitle: string) => inputText === originalTitle || !inputText,
        },
        duplicate: {
            title: 'Duplicate',
            message: (title: string) => (
                <p>
                    Are you sure you want to duplicate{' '}
                    <Text as="span" variant="bodyMd" fontWeight="medium">
                        "{title}"
                    </Text>{' '}
                    ?
                </p>
            ),
            cancelText: 'Cancel',
            okText: 'Confirm',
            showInput: true,
        },
        export: {
            title: 'Export section',
            message: <p>Do you want to export selected section?</p>,
            cancelText: 'No',
            okText: 'Yes, export',
        },
        delete: {
            title: 'Delete section',
            message: (title: string) => (
                <p>
                    Are you sure you want to delete section{' '}
                    <Text as="span" variant="bodyMd" fontWeight="medium">
                        "{title}"
                    </Text>{' '}
                    ?
                </p>
            ),
            cancelText: 'Cancel',
            okText: 'Delete',
        },
    };

    const actionHandlers: GenericResourceItemProps<Section>['actionHandlers'] = {
        onRename: ({ newTitle }) => {
            renameMutation.mutate({
                title: newTitle,
            });
        },
        onDuplicate: ({ newTitle }) => {
            duplicateMutation.mutate({
                title: newTitle,
            });
        },
        onDelete: ({ data }) => {
            deleteMutation.mutate({
                ids: [data.id],
            });
        },
        onExport: ({ data }) => console.log('Export section', data.id),
    };

    const isLoading =
        renameMutation.isLoading || duplicateMutation.isLoading || deleteMutation.isLoading || statusMutation.isLoading;

    return (
        <GenericResourceItem<Section>
            loading={isLoading}
            data={data}
            typeName="Section"
            selectMode={selectMode}
            renderers={renderers}
            actions={actions}
            modalConfigs={modalConfigs}
            actionHandlers={actionHandlers}
            openModal={openModal}
            onChangeOpenModal={setOpenModal}
            onPreview={(section) => console.log('Preview section', section.id)}
        />
    );
};
