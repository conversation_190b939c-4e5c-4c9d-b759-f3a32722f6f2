import { FC } from 'react';
import { BaseIcon } from '@/components/builder/base/BaseIcon';
import { UnitValue } from '@/components/builder/blocks/button/types';

interface ButtonIconProps {
    source: string;
    size?: UnitValue;
    color?: string;
    autoId: string;
}

export const ButtonIcon: FC<ButtonIconProps> = ({ source, size, color, autoId }) => {
    return <BaseIcon source={source} size={`${size?.val}`} color={color} autoId={autoId} />;
};
