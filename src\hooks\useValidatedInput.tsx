import { KeyboardEvent, useRef, useState } from 'react';

interface UseValidatedInputProps<T> {
    initialValue: T;
    validator?: (value: T, acceptedValue: T) => boolean;
    converter?: (value: T) => T;
    onApply?: (value: T) => void;
}

interface UseValidatedInputReturn<T> {
    value: T;
    acceptedValue: T;
    onChange: (value: T) => void;
    onBlur: () => void;
    onKeyDown: (e: KeyboardEvent<HTMLInputElement>, callback?: () => void) => void;
    forceUpdate: (value: T) => void;
}

export function useValidatedInput<T>({
    initialValue,
    validator = () => true,
    converter = (value) => value,
    onApply = () => {},
}: UseValidatedInputProps<T>): UseValidatedInputReturn<T> {
    const [value, setValue] = useState<T>(initialValue);
    const acceptedValueRef = useRef<T>(initialValue);

    const onChange = (value: T) => {
        setValue(value);
    };

    const applyValue = () => {
        if (validator(value, acceptedValueRef.current)) {
            setValue(value);
            onApply(value);
            acceptedValueRef.current = value;
        } else {
            setValue(acceptedValueRef.current);
        }
    };

    const onBlur = () => {
        applyValue();
    };

    const onKeyDown = (e: KeyboardEvent<HTMLInputElement>, callback?: () => void) => {
        if (e.key === 'Enter') {
            applyValue();
            callback?.();
        }
    };

    const forceUpdate = (value: T) => {
        setValue(value);
        acceptedValueRef.current = value;
    };

    return {
        value: converter(value),
        acceptedValue: converter(acceptedValueRef.current),
        forceUpdate,
        onChange,
        onBlur,
        onKeyDown,
    };
}
