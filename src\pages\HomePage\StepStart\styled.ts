import styled from '@emotion/styled';

export const StepStartContainer = styled.div<{ isHidden: boolean }>`
    display: ${(props) => (props.isHidden ? 'none' : 'block')};
    .Polaris-ShadowBevel > .Polaris-Box {
        padding: 0;
    }
`;

export const StepStartHeader = styled.div`
    padding: 20px 16px;
    .Polaris-InlineGrid {
        padding-bottom: 4px;
        .Polaris-Text--root {
            font-size: 14px;
        }
    }
    .Polaris-ButtonGroup {
        column-gap: 4px;
        .Polaris-ButtonGroup__Item {
            margin: 0;
            height: 20px;
            border-radius: 4px;
            transition: all 0.2s ease-in-out;
            .Polaris-Icon {
                svg {
                    fill: #4a4a4a;
                }
            }
            &:hover {
                background: #f1f1f1;
            }
        }
    }
    .Polaris-Icon {
        transition: all 0.2s ease-in-out;
    }
    #button-down-true {
        .Polaris-Icon {
            transform: rotate(180deg);
        }
    }
`;
export const ProgressBar = styled.div`
    display: flex;
    align-items: center;
    column-gap: 20px;
    margin-top: 16px;

    p.<PERSON>is-Text--root {
        width: max-content;
        font-size: 13px;
        min-width: 143px;
    }
`;
export const CustomProgressBar = styled.div`
    width: 100%;
    height: 4px;
    border-radius: 4px;
    background: #e3e3e3;
    position: relative;
`;
export const ProgressIndicator = styled.div`
    border-radius: 4px;
    height: 4px !important;
    position: absolute;
    background: #1a1a1a;
    transition: all 0.5s ease-in-out;
`;
export const Line = styled.div`
    height: 1px;
    background: #ebebeb;
    width: 100%;
`;
export const StepStartContent = styled.div`
    padding: 8px 16px 20px;
    > .Polaris-Box {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
    }
`;
export const ButtonRight = styled.div`
    min-width: fit-content;
    margin-top: 8px;
    margin-right: 14px;
    visibility: hidden;
`;
export const StepStartContentItem = styled.div<{ active: boolean }>`
    display: flex;
    align-items: start;
    column-gap: 12px;
    transition: all 0.2s ease-in-out;
    border-radius: 12px;
    ${({ active }) =>
        active &&
        `
        background: #f7f7f7;
        & ${ButtonRight}{
            visibility: visible;
        }
        .Polaris-Text--headingSm {
            font-weight: 700;
        }
        & ${TextMiddle} {
            cursor: default;
        }
    `}

    &:hover {
        background: #f7f7f7;
        ${ButtonRight} {
            visibility: visible;
        }
    }
    #button-true {
        .Polaris-Button__Icon {
            svg {
                width: 20px;
                height: 20px;
                fill: #616161 !important;
                animation: var(--p-motion-keyframes-spin) var(--p-motion-duration-500) linear infinite;
            }
        }
    }
    .Polaris-Button {
        font-weight: 600;
        padding: 6px 12px;
        min-width: 87px;
        svg {
            fill: none !important;
        }
        &.Polaris-Button--iconOnly {
            background: none;
            margin: 12px 0 0 14px;
            padding: 0;
            min-width: 20px;
            min-height: 20px;
            display: block;
            .Polaris-Button__Icon {
                display: block;
                height: 20px;
            }
            &:active {
                transform: scale(1);
            }
        }
    }
    .Polaris-Button--disabled.Polaris-Button--disabled svg {
        fill: #cccccc;
    }
`;

export const TextMiddle = styled.div`
    padding: 12px 0;
    width: 100%;
    cursor: pointer;
    .Polaris-Text--headingSm {
        font-size: 14px;
        font-weight: 550;
        color: #303030;
    }
    .Polaris-Collapsible {
        .Polaris-Text--subdued {
            margin-top: 8px;
            display: block;
        }
    }
`;
