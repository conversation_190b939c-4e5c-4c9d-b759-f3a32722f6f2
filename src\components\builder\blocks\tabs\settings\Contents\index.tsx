import { Box, Button } from '@shopify/polaris';
import { PlusCircleIcon } from '@shopify/polaris-icons';
import { genRandomBlockId, useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseItemLayout } from '@/components/builder/base';
import { SettingsSortable } from '@/components/builder/settings/SettingsSortable';
import { TabItemRenderer, TabItem } from '@/components/builder/settings/SettingsSortable/renderer/TabItemRenderer';
import { useAppStore } from '@/stores/appStore/useAppStore';
import './styles.scss';

export const Contents = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const blocks = useBlockStore((state) => state.blocks);
    const setTempTab = useAppStore((state) => state.setTempTab);

    if (!selectedBlockId) return null;

    const items = (blocks[selectedBlockId].configs.items as TabItem[]) || [];

    const onCreateTab = () => {
        const newItem = {
            id: genRandomBlockId(),
            name: 'New',
        };
        setTempTab(selectedBlockId, newItem.id);
        updateBlockConfigsProperty(selectedBlockId, 'items', [...items, newItem] as unknown as Record<string, unknown>);
    };

    return (
        <Box paddingInline="200" paddingBlock="400">
            <BaseItemLayout direction="column" textProps={{ as: 'p', variant: 'headingMd', children: 'Tabs' }}>
                <SettingsSortable
                    blockId={selectedBlockId}
                    path="items"
                    isUpdateConfigs
                    containerProps={{ className: 'tabs-sortable' }}
                    itemProps={{ style: { transform: 'none' } }}
                    itemRenderer={(props) => <TabItemRenderer {...props} blockId={selectedBlockId} />}
                    extendNode={
                        <div css={{ order: 1, marginTop: '4px' }}>
                            <Button
                                textAlign="left"
                                variant="plain"
                                pressed={false}
                                icon={PlusCircleIcon}
                                onClick={onCreateTab}
                            >
                                Add new
                            </Button>
                        </div>
                    }
                />
            </BaseItemLayout>
        </Box>
    );
};
