import { ActionListItemDescriptor } from '@shopify/polaris';

export const configsChooseDefault = [
    {
        value: 'all-items-closed',
        title: 'All items closed',
    },
    {
        value: 'first-items-opened',
        title: 'First items opened',
    },
    {
        value: 'all-items-opened',
        title: 'All items opened',
    },
];

export const lineHeightOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: '1', id: '1' },
    { content: '1.5', id: '1.5' },
    { content: 'Double', id: 'double' },
    { content: 'Custom', id: 'custom' },
];

export const letterSpacingOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Auto', id: 'auto' },
    { content: 'Narrow', id: 'narrow' },
    { content: 'Wide', id: 'wide' },
    { content: 'Custom', id: 'custom' },
];
