import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { getBlockClassName } from '@/utils/cssBlockGenerator';

export const generateCss = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile'): string => {
    if (!block.bpConfigs?.[device]) return '';

    const config = block.bpConfigs[device];
    const className = getBlockClassName(blockId);
    let css = '';

    // Page container styles
    css += `.${className} {
      background: ${config.background};
      color: ${config.color};
      width: ${config.width?.val}${config.width?.unit};
      height: ${config.height?.val}${config.height?.unit};
      position: ${config.position};
      z-index: ${config.zIndex};
      padding: ${config.pt?.val}${config.pt?.unit} ${config.pr?.val}${config.pr?.unit} ${config.pb?.val}${config.pb?.unit} ${config.pl?.val}${config.pl?.unit};
    }\n`;
    return css;
};
