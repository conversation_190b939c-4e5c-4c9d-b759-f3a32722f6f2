import { CSSProperties } from 'react';
import {
    generateBackground,
    generateBaseCss,
    generateBorder,
    generateTypography,
    getResponsiveValue,
} from '@/stores/appStore/cssSystem';
import type { Generator, Background, Border, Typography, UnitValue, Spacing } from '@/stores/appStore/cssSystem';

export const buttonCssGenerators: Generator[] = [
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            return {
                ...generateBaseCss(blockData, breakpoint),
            };
        },
    },
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const { displayOnDesktop, displayOnMobile } = blockData.configs;
            const display = breakpoint === 'desktop' ? displayOnDesktop : displayOnMobile;
            return {
                display: display ? 'block' : 'none',
            };
        },
        applyTo: 'publish',
    },
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const background = getResponsiveValue<Background>(blockData, 'backgroundButton', breakpoint);
            const border = getResponsiveValue<Border>(blockData, 'borderButton', breakpoint);
            const width = getResponsiveValue<{ val: string; unit: string }>(blockData, 'width', breakpoint);
            const height = getResponsiveValue<{ val: string; unit: string }>(blockData, 'height', breakpoint);
            const content = blockData.configs.content as { direction: string; reverse: boolean; spacing: UnitValue };
            //prettier-ignore
            const flexDirection = content?.direction? content.reverse? `${content.direction}-reverse`: content.direction: undefined;
            const padding = getResponsiveValue<Spacing>(blockData, 'buttonsSpace.padding', breakpoint);

            return {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'var(--button-content-align)',
                ...generateBackground(background),
                ...generateBorder(border),
                ...(width && { width: `${width.val}${width.unit || 'px'}` }),
                ...(height && { height: `${height.val}${height.unit || 'px'}` }),
                flexDirection: flexDirection as CSSProperties['flexDirection'],
                ...(content?.spacing && {
                    columnGap: `var(--atk-button-gap, ${content.spacing.val}${content.spacing.unit || 'px'})`,
                }),
                ...(padding && {
                    padding: `var(--atk-button-spacing-padding, var(--atk-button-spacing-padding-top, ${
                        padding.top.val
                    }${padding.top.unit || 'px'}) var(--atk-button-spacing-padding-right, ${padding.right.val}${
                        padding.right.unit || 'px'
                    }) var(--atk-button-spacing-padding-bottom, ${padding.bottom.val}${
                        padding.bottom.unit || 'px'
                    }) var(--atk-button-spacing-padding-left, ${padding.left.val}${padding.left.unit || 'px'}))`,
                }),
            };
        },
    },
    {
        selector: (blockId) => `[data-atk-button-text="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const typography = getResponsiveValue<Typography>(blockData, 'typography', breakpoint);
            return generateTypography(typography, 'atk-button-line-height', 'atk-button-letter-spacing');
        },
    },
    {
        selector: (blockId) => `[data-atk-id-icon="${blockId}"]`,
        generator: (blockData) => {
            const buttonIcon = blockData.configs.content as { iconSize: UnitValue };
            return {
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: `var(--atk-button-icon-size, ${buttonIcon.iconSize.val}${buttonIcon.iconSize.unit || 'px'})`,
                height: `var(--atk-button-icon-size, ${buttonIcon.iconSize.val}${buttonIcon.iconSize.unit || 'px'})`,
                flexShrink: 0,
            };
        },
    },
    {
        selector: (blockId) => `[data-atk-id-icon="${blockId}"] svg, [data-atk-id-icon="${blockId}"] path`,
        generator: (blockData) => {
            const buttonIcon = blockData.configs.content as { iconColor: string };
            return {
                fill: buttonIcon.iconColor,
            };
        },
        applyTo: 'publish',
    },
];
