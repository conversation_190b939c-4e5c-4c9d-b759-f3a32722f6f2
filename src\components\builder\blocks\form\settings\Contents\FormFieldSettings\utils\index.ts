/* eslint-disable @typescript-eslint/no-explicit-any */
import { genRandomBlockId, useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { useSettings } from '@/pages/BuilderPage/hooks';

interface FormFieldPaths {
    DEFAULT_PATH: string;
    DEFAULT_PATH_FORM: string;
}
type FormFieldWithOptions = 'checkbox' | 'radio' | 'dropdown';

export const useFormFieldPaths = (formField: FormFieldType): FormFieldPaths => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const formArr = useBlockStore((state) => state.blocks[selectedBlockId]?.configs?.form) as FormFieldType[];
    const fieldIndex = formArr?.findIndex((field) => field.key === formField.key);

    return {
        DEFAULT_PATH: `configs.form.${fieldIndex}` as const,
        DEFAULT_PATH_FORM: `form.${fieldIndex}` as const,
    };
};

export function findIndexFormField(formArr: FormFieldType[], optionData: any, type: FormFieldWithOptions) {
    return formArr.findIndex((field) => {
        if (field.type === type && field.options) {
            return field.options.some((option) => option.id === optionData.id);
        }
        return false;
    });
}

export const useHandleAddOption = (formField: FormFieldType, opts: any[]) => {
    const { DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { updateValue: updateItems } = useSettings({
        blockId: selectedBlockId,
        path: `${DEFAULT_PATH_FORM}.options`,
        isUpdateConfigs: true,
    });
    return () => {
        if (!['radio', 'checkbox', 'dropdown'].includes(formField.type)) return;

        const currentOptions = Array.isArray(opts) ? opts : [];
        const newOption = {
            id: genRandomBlockId(),
            key: genRandomBlockId(),
            value: `option${currentOptions.length + 1}`,
            label: `Option ${currentOptions.length + 1}`,
            ...(formField.type === 'checkbox' && {
                defaultChecked: false,
                required: true,
                image: '',
            }),
        };

        const updatedOptions = [...currentOptions, newOption];
        updateItems(updatedOptions);
    };
};
