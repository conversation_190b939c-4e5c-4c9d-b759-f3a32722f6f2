import { BaseHasBorderLayout } from '@/components/builder/base';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BlockStack } from '@shopify/polaris';
import { FC } from 'react';
import { getBlockProperty } from '@/utils/shared';
import OverflowToggle from '../../components/OverviewItem';

const DEFAULT_PATH = {
    CONTENT: 'content',
};

export const Contents: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const isCollapse = getBlockProperty(`configs.${DEFAULT_PATH.CONTENT}.isCollapse`, selectedBlockId);

    return (
        <BaseHasBorderLayout>
            <BlockStack gap="400">
                <OverflowToggle isUpdateConfigs isCollapse={isCollapse} id={selectedBlockId} />
            </BlockStack>
        </BaseHasBorderLayout>
    );
};
