import { FC } from 'react';
import { TextF<PERSON>, Card, BlockStack, Text, ChoiceList, Box } from '@shopify/polaris';
import { type FormField } from '../hooks/useFormField';
interface EmailSettingsCardProps {
    emailField: FormField<string>;
    emailNotificationsField: FormField<string[]>;
    validateEmail: (value: string) => string | false;
}
export const EmailSettingsCard: FC<EmailSettingsCardProps> = ({
    emailField,
    emailNotificationsField,
    validateEmail,
}) => {
    return (
        <Card padding="0">
            <Box paddingBlock="500" paddingInline="400">
                <BlockStack gap="600">
                    <BlockStack gap="400">
                        <Text as="h2" variant="headingSm">
                            Account email
                        </Text>
                        <TextField
                            label="Email address"
                            value={emailField.value || ''}
                            onChange={emailField.handleChange}
                            autoComplete="email"
                            type="email"
                            helpText="We will use this information to contact you."
                            error={validateEmail(emailField.value as string)}
                            maxLength={100}
                            showCharacterCount={true}
                        />
                    </BlockStack>
                    <BlockStack gap="200">
                        <Text as="h2" variant="headingSm">
                            Email notifications
                        </Text>
                        <ChoiceList
                            title=""
                            choices={[
                                { label: 'Subscribe', value: 'subscribe' },
                                { label: 'Unsubscribe', value: 'unsubscribe' },
                            ]}
                            selected={emailNotificationsField.value as string[]}
                            onChange={emailNotificationsField.handleChange}
                        />
                    </BlockStack>
                </BlockStack>
            </Box>
        </Card>
    );
};
