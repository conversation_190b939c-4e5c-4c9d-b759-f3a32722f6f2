import { FC } from 'react';
import { InlineStack } from '@shopify/polaris';
import { UseCaseFilterItem } from './UseCaseFilterItem';

interface UseCaseFilterProps {
    options: { value: string; label: string }[];
    value: string;
    onChange: (value: string) => void;
}

export const UseCaseFilter: FC<UseCaseFilterProps> = ({ options, value, onChange }) => {
    return (
        <InlineStack wrap gap="200">
            {options.map((option, i) => (
                <UseCaseFilterItem
                    key={i}
                    label={option.label}
                    isActive={value === option.value}
                    onClick={() => onChange(option.value)}
                />
            ))}
        </InlineStack>
    );
};
