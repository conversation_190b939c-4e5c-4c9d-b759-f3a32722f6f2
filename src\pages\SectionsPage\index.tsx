import { FC } from 'react';
import { TitleBar, Modal } from '@shopify/app-bridge-react';
import { Page, Box, IndexFilters, Card, Pagination, Button, ButtonGroup } from '@shopify/polaris';
import { IncomingIcon } from '@shopify/polaris-icons';
import { useNavigate } from 'react-router-dom';
import clsx from 'clsx';
import { useMutation, useShopSections } from '@/hooks';
import { pathnames } from '@/configs';
import { apiAddress } from '@/configs/apiAddress';
import { SectionsList } from './SectionsList';
import './styles.scss';

export const SectionsPage: FC = () => {
    const navigate = useNavigate();
    const {
        isLoading,
        sections,
        page,
        limit,
        total,
        mode,
        tabSelected,
        sortSelected,
        queryValue,
        selectedItems,
        tabs,
        sortOptions,
        skeletonCount,
        setMode,
        setTabSelected,
        setPage,
        setQueryValue,
        setSortSelected,
        setSelectedItems,
        refetch,
    } = useShopSections();

    const createMutation = useMutation({
        url: apiAddress.shopSections.index,
        method: 'POST',
        onMutate: () => {
            shopify.loading(true);
        },
        onSuccess: () => {
            shopify.toast.show('Section created successfully');
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to create section', { isError: true });
        },
        onSettled: () => {
            shopify.loading(false);
        },
    });

    const statusMutation = useMutation({
        url: apiAddress.shopSections.status,
        method: 'PUT',
        onMutate: () => {
            shopify.loading(true);
        },
        onSuccess: () => {
            shopify.toast.show('Section status updated successfully');
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to update section status', { isError: true });
        },
        onSettled: () => {
            shopify.loading(false);
        },
    });

    const deleteMutation = useMutation({
        url: apiAddress.shopSections.index,
        method: 'DELETE',
        onSuccess: () => {
            shopify.toast.show('Section deleted successfully');
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Failed to delete section', { isError: true });
        },
        onSettled: () => {
            shopify.modal.hide('modal-delete-sections');
        },
    });

    return (
        <Page
            title="Shopify Section"
            primaryAction={{
                content: 'Create from template',
                onAction: () => navigate(pathnames.sectionsCreate),
            }}
            secondaryActions={
                <ButtonGroup>
                    <Button icon={IncomingIcon} onClick={() => {}}>
                        Import
                    </Button>
                    <Button
                        loading={createMutation.isLoading}
                        onClick={() => {
                            createMutation.mutate({
                                title: `Example section ${Math.ceil(Math.random() * 1000000)}`,
                                thumbnail: '',
                                htmlData: '<h1>{{ section.settings.title }}</h1>',
                                jsonData: '<h1>title</h1>',
                                status: 1,
                            });
                        }}
                    >
                        Create blank section
                    </Button>
                </ButtonGroup>
            }
        >
            <div className="sections-wrapper">
                <Card padding="0">
                    <Box paddingBlockStart="500">
                        <IndexFilters
                            tabs={tabs}
                            mode={mode}
                            selected={tabSelected}
                            queryValue={queryValue}
                            filters={[]}
                            sortSelected={sortSelected}
                            sortOptions={sortOptions}
                            filteringAccessibilityTooltip="Search"
                            queryPlaceholder="Searching all sections"
                            setMode={setMode}
                            onSelect={(selectedTabIndex) => {
                                setTabSelected(selectedTabIndex);
                                setPage(1);
                            }}
                            onSort={setSortSelected}
                            onQueryChange={setQueryValue}
                            onQueryClear={() => setQueryValue('')}
                            onClearAll={() => setQueryValue('')}
                            cancelAction={{
                                onAction: () => setQueryValue(''),
                                disabled: false,
                                loading: false,
                            }}
                            canCreateNewView={false}
                        />
                    </Box>
                    <SectionsList
                        sections={sections}
                        isLoading={isLoading}
                        skeletonCount={skeletonCount}
                        selectedItems={selectedItems}
                        onSelectionChange={setSelectedItems}
                        onPublish={() => {
                            statusMutation.mutate({
                                ids: selectedItems,
                                status: 'Published',
                            });
                        }}
                        onUnpublish={() => {
                            statusMutation.mutate({
                                ids: selectedItems,
                                status: 'Unpublished',
                            });
                        }}
                        onDelete={() => {
                            shopify.modal.show('modal-delete-sections');
                        }}
                        refetch={refetch}
                    />
                    <div className={clsx('pagination-wrapper', (isLoading || sections.length === 0) && 'hidden')}>
                        <Pagination
                            hasNext={page * limit < total}
                            hasPrevious={page > 1}
                            onNext={() => setPage(page + 1)}
                            onPrevious={() => setPage(page - 1)}
                        />
                    </div>
                </Card>
            </div>
            <Modal id="modal-delete-sections">
                <Box padding="400" paddingBlockEnd="800">
                    <p>Are you sure you want to delete selected sections?</p>
                </Box>
                <TitleBar title="Title">
                    <button
                        variant="primary"
                        tone="critical"
                        loading={deleteMutation.isLoading ? '' : undefined}
                        onClick={() => {
                            deleteMutation.mutate({
                                ids: selectedItems,
                            });
                        }}
                    >
                        Delete
                    </button>
                    <button onClick={() => shopify.modal.hide('modal-delete-sections')}>Cancel</button>
                </TitleBar>
            </Modal>
        </Page>
    );
};

export { CreatePage as SectionsCreatePage } from './CreatePage';
