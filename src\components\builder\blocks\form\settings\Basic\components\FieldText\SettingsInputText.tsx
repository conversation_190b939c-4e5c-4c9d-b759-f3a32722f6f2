import { SettingsColorPicker } from '@/components/builder/settings';
import {
    textAlignOptions,
    textDecorationOptions,
    textDirectionOptions,
    textFontWeightOptions,
    textTransformOptions,
} from '@/components/builder/data/options';
import { SettingsInput, SettingsSelect, SettingsSwitchTab } from '@/components/builder/settings';
import { SettingsFontFamily } from '@/components/builder/settings';
import { Box } from '@shopify/polaris';
import { BlockStack } from '@shopify/polaris';
import { FC } from 'react';

interface SettingsInputTextProps {
    label: string;
    blockId: string;
    path: string;
    isUpdateConfigs: boolean;
}

export const SettingsInputText: FC<SettingsInputTextProps> = ({ blockId, path, isUpdateConfigs }) => {
    return (
        <BlockStack gap="400">
            <Box paddingBlock="300" paddingInline="200" borderWidth="025" borderColor="border" borderRadius="200">
                <BlockStack gap="400">
                    <SettingsFontFamily
                        blockId={blockId}
                        path={`${path}.inputFontFamily`}
                        isUpdateConfigs={isUpdateConfigs}
                    />
                    <SettingsSelect
                        label="Text Transform"
                        blockId={blockId}
                        path={`${path}.inputTextTransform`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textTransformOptions}
                    />
                    <SettingsSelect
                        label="Font Weight"
                        blockId={blockId}
                        path={`${path}.inputFontWeight`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textFontWeightOptions}
                    />
                    <SettingsInput
                        label="Font Size"
                        blockId={blockId}
                        path={`${path}.inputFontSize`}
                        isUpdateConfigs={isUpdateConfigs}
                        inputProps={{
                            min: 4,
                            suffix: 'px',
                        }}
                    />
                    <SettingsSwitchTab
                        label="Text Align"
                        blockId={blockId}
                        path={`${path}.inputTextAlign`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textAlignOptions}
                    />
                    <SettingsSwitchTab
                        label="Text Decoration"
                        blockId={blockId}
                        path={`${path}.inputTextDecoration`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textDecorationOptions}
                    />
                    <SettingsSwitchTab
                        label="Text Direction"
                        blockId={blockId}
                        path={`${path}.inputTextDirection`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textDirectionOptions}
                    />
                    <SettingsColorPicker
                        label="Text Color"
                        blockId={blockId}
                        path={`${path}.inputColor`}
                        isUpdateConfigs={isUpdateConfigs}
                    />
                </BlockStack>
            </Box>
        </BlockStack>
    );
};
