import { Divider, Icon, InlineStack, Text, Tooltip } from '@shopify/polaris';
import { Card } from '@shopify/polaris';
import { Grid } from '@shopify/polaris';
import { FC } from 'react';
import { ArrowDownIcon, ArrowUpIcon, InfoIcon } from '@shopify/polaris-icons';
import { useCountUp } from '@/hooks';

interface AnalyticsItemProps {
    item: {
        title: string;
        number: number;
        tooltip: string;
        percentage: {
            fluctuate: 'up' | 'down';
            value: number;
        };
    };
}

export const AnalyticsItem: FC<AnalyticsItemProps> = ({ item }) => {
    return (
        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
            <Card padding="400" background="bg-surface-secondary">
                <InlineStack gap="050">
                    <Text as="h2" variant="headingSm">
                        {item.title}
                    </Text>
                    <Tooltip content={item.tooltip}>
                        <Icon tone="subdued" source={InfoIcon} />
                    </Tooltip>
                </InlineStack>
                <Divider borderColor="border" />
                <InlineStack align="space-between">
                    <Text as="h2" variant="headingXl" fontWeight="semibold">
                        {useCountUp(item.number)}
                    </Text>
                    <InlineStack align="center" gap="050">
                        <Icon
                            tone={item.percentage.fluctuate === 'up' ? 'success' : 'critical'}
                            source={item.percentage.fluctuate === 'up' ? ArrowUpIcon : ArrowDownIcon}
                        />
                        <Text tone={item.percentage.fluctuate === 'up' ? 'success' : 'critical'} as="span">
                            {useCountUp(item.percentage.value)}%
                        </Text>
                    </InlineStack>
                </InlineStack>
            </Card>
        </Grid.Cell>
    );
};
