import { FC, memo } from 'react';
import { Button, Collapsible, Text } from '@shopify/polaris';
import { ReactComponent as Checkbox } from '@/assets/svgs/checkbox.svg';
import { ReactComponent as EllipseCheck } from '@/assets/svgs/ellipse-check.svg';
import { ReactComponent as Loading } from '@/assets/svgs/loading.svg';
import { StepStartContentItemProps as BaseStepStartContentItemProps } from './types';
import * as sc from './styled';

interface StepStartContentItemProps extends BaseStepStartContentItemProps {
    handleCheck: (id: string) => void;
    onOpenItem: (id: string) => void;
}

export const StepStartContentItem: FC<StepStartContentItemProps> = memo(
    ({ id, isCollapsible, isChecked, des, text, loading, handleCheck, onOpenItem }) => {
        return (
            <sc.StepStartContentItem active={isCollapsible}>
                <Button
                    variant="tertiary"
                    pressed={isChecked}
                    onClick={() => handleCheck(id)}
                    id={`button-${loading}`}
                    icon={loading ? <Loading /> : isChecked ? <Checkbox /> : <EllipseCheck />}
                />
                <sc.TextMiddle onClick={() => onOpenItem(id)}>
                    <Text as="h2" variant="headingSm">
                        {text}
                    </Text>
                    <Collapsible
                        open={isCollapsible}
                        id="step-start-content-collapsible"
                        transition={{ duration: '200ms', timingFunction: 'ease-in-out' }}
                        expandOnPrint
                    >
                        <Text tone="subdued" as="span">
                            {des}
                        </Text>
                    </Collapsible>
                </sc.TextMiddle>
                <sc.ButtonRight>
                    <Button variant={isCollapsible ? 'primary' : undefined}>Call action</Button>
                </sc.ButtonRight>
            </sc.StepStartContentItem>
        );
    },
);
