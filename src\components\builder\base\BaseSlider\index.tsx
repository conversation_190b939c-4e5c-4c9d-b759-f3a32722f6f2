import { FC } from 'react';
import { RangeSlider, RangeSliderProps } from '@shopify/polaris';

export const BaseSlider: FC<Partial<RangeSliderProps>> = ({
    max,
    min,
    value = 0,
    onChange = () => {},
    step = 1,
    labelHidden = true,
    label,
    ...props
}) => {
    return (
        <RangeSlider
            max={max}
            min={min}
            value={value}
            onChange={onChange}
            output
            label={label}
            step={step}
            labelHidden={labelHidden}
            {...props}
        />
    );
};
