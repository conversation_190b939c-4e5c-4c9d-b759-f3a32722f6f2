.option-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.option {
    border: 1px solid #8a8a8a;
    padding: 6px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    height: 32px;
    &.insert-before::before {
        content: '';
        position: absolute;
        top: -6px;
        left: 0;
        right: 0;
        height: 2px;
        background: #f37321;
        border-radius: 2px;
    }

    &.insert-after::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        right: 0;
        height: 2px;
        background: #f37321;
        border-radius: 2px;
    }
    &.ghost {
        background: #ffe8d9;
        border: 1px dashed #f37321;
    }

    &:hover {
        .option-label-drag-handle {
            visibility: visible;
            opacity: 1;
            transition: all 0.2s ease;
        }
        .option-remove {
            visibility: visible;
            opacity: 1;
            transition: all 0.2s ease;
        }
    }
    &-label {
        display: flex;
        align-items: center;
        &-drag-handle {
            flex-shrink: 0;
            visibility: hidden;
            opacity: 0;
            display: flex;
            align-items: center;
        }
    }
    &-remove {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        visibility: hidden;
        opacity: 0;
        &:hover {
            background: #f0f0f0;
            border-radius: 50%;
        }
    }
}
