import { PageItem, RequestName } from '@/stores/appStore/pages/types';

export type PageItemProps = PageItem & {
    __type__?: unknown;
    templateId: number | null;
    selectMode?: boolean;
    isHomePage?: boolean;
    setSelectedItems?: (items: string[]) => void;
    popoverActive: boolean;
    setPopoverActive: (active: boolean) => void;
};

export type SortType = `${string} asc` | `${string} desc`;

export type PageListParams = {
    currentPage: number;
    perPage: number;
    status?: number | undefined;
    keyword?: string;
    sortName?: string;
    order?: string;
    pageType?: string;
};

export type ToastOptionsProps = {
    duration?: number;
    isError?: boolean;
};

export interface ApiActionConfig {
    action: () => Promise<unknown>;
    successMsg: string;
    errorMsg: string;
    onSuccess?: (response?: unknown) => void;
    requestName: RequestName;
}
