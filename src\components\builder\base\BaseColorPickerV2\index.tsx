import { FC, useEffect, useState } from 'react';
import { HsvaColor, validHex, color as getColor, rgbStringToHsva, hsvaToHexa } from '@uiw/color-convert';
import { Popover, PopoverOptions } from '@/components';
import { useUncontrolled, useCallbackDebounce } from '@/hooks';
import { Saturation, Hue, Alpha, Editable, ColorLump, Header } from './components';

interface BaseColorPickerV2Props {
    open?: boolean;
    defaultOpen?: boolean;
    color: string;
    onChange: (color: string) => void;
    onOpenChange?: (open: boolean) => void;
    triggerRender?: React.ReactNode;
    contentRender?: React.ReactNode;
    extraHeaderRender?: React.ReactNode;
    popoverOptions?: Omit<PopoverOptions, 'open' | 'onOpenChange'>;
}

export const BaseColorPickerV2: FC<BaseColorPickerV2Props> = ({
    color,
    open,
    defaultOpen,
    onChange,
    onOpenChange,
    triggerRender,
    contentRender,
    extraHeaderRender,
    popoverOptions,
}) => {
    const [isOpen, setIsOpen] = useUncontrolled({
        value: open,
        defaultValue: defaultOpen,
        onChange: onOpenChange,
    });

    // const hsva = validHex(color) ? getColor(color).hsva : rgbStringToHsva(color);

    // const setHsva = (newColor: HsvaColor) => {
    //     onChange(hsvaToHexa(newColor));
    // };

    // console.log(hsva, hsvaToHexa(hsva), hsvaToRgba(hsva));

    const [hsva, setHsva] = useState<HsvaColor>(validHex(color) ? getColor(color).hsva : rgbStringToHsva(color));
    const [h, setH] = useState(hsva.h);

    const onChangeDebounce = useCallbackDebounce(onChange, 100);

    const handleChangeColor = (newColor: HsvaColor) => {
        setHsva(newColor);
        onChangeDebounce(hsvaToHexa(newColor));
    };

    const defaultPopoverOptions: PopoverOptions = {
        placement: 'right-start',
        offsetOptions: { mainAxis: 10 },
    };

    useEffect(() => {
        const newHsva = validHex(color) ? getColor(color).hsva : rgbStringToHsva(color);
        setHsva(newHsva);
        setH(newHsva.h);
    }, [color]);

    return (
        <Popover open={isOpen} onOpenChange={setIsOpen} {...{ ...defaultPopoverOptions, ...popoverOptions }}>
            <Popover.Trigger onClick={() => setIsOpen(!isOpen)} asChild>
                {triggerRender ? (
                    <div>{triggerRender}</div>
                ) : (
                    <svg
                        width="28"
                        height="28"
                        viewBox="0 0 28 28"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        style={{ cursor: 'pointer' }}
                    >
                        <circle cx="14" cy="13.9987" r="11.6667" stroke="#CCCCCC" />
                        <circle cx="14" cy="13.9987" r="8.16667" fill={color} />
                    </svg>
                )}
            </Popover.Trigger>
            <Popover.Content css={{ zIndex: 99, background: 'white', width: '267px' }}>
                <div
                    css={{
                        borderRadius: '12px',
                        boxShadow:
                            '0 4px 6px -2px rgba(26, 26, 26, 0.2), 0 1px 0 0 rgba(204, 204, 204, 0.5), 0 -1px 0 0 rgba(0, 0, 0, 0.17), -1px 0 0 0 rgba(0, 0, 0, 0.13), 1px 0 0 0 rgba(0, 0, 0, 0.13)',
                    }}
                >
                    <Header
                        onClose={() => setIsOpen(false)}
                        children={extraHeaderRender}
                        setHsva={handleChangeColor}
                        setH={setH}
                    />
                    <div
                        css={{
                            display: 'flex',
                            flexDirection: 'column',
                            rowGap: '16px',
                            padding: '16px',
                        }}
                    >
                        {contentRender || (
                            <>
                                <Saturation
                                    hsva={{ ...hsva, h }}
                                    onChange={(newColor) => {
                                        handleChangeColor({ ...hsva, ...newColor, a: hsva.a });
                                    }}
                                />
                                <div
                                    css={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        columnGap: '16px',
                                    }}
                                >
                                    <ColorLump hsva={hsva} />
                                    <div
                                        css={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            width: '100%',
                                            rowGap: '16px',
                                        }}
                                    >
                                        <Hue
                                            hue={h}
                                            onChange={(newHue) => {
                                                setH(newHue.h);
                                                handleChangeColor({ ...hsva, ...newHue });
                                            }}
                                        />
                                        <Alpha
                                            hsva={hsva}
                                            onChange={(newAlpha) => {
                                                handleChangeColor({ ...hsva, ...newAlpha });
                                            }}
                                        />
                                    </div>
                                </div>
                                <Editable hsva={hsva} setHsva={handleChangeColor} />
                            </>
                        )}
                    </div>
                </div>
            </Popover.Content>
        </Popover>
    );
};
