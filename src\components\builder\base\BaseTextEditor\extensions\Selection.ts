import { Editor, Extension } from '@tiptap/core';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import { Plugin, PluginKey, Transaction, TextSelection } from '@tiptap/pm/state';

const NAME = 'selection';
const ACTION_TYPES = {
    BLUR: 'blur',
    FOCUS: 'focus',
    SPOTLIGHT: 'spotlight',
    CLEAR: 'clear',
    TOGGLE: 'toggle',
    CLEAR_SELECTION: 'clearSelection',
};

export interface SelectionExtensionOptions {
    enabled: boolean;
    applyOnBlur: boolean;
    clearOnFocus: boolean;
    spotlightClass: string;
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        selection: {
            spotlightSelection: () => ReturnType;
            clearSpotlight: () => ReturnType;
            toggleSpotlightSelection: () => ReturnType;
            setSpotlightEnabled: (enabled: boolean) => ReturnType;
            clearSelection: () => ReturnType;
        };
    }
}

export const Selection = Extension.create<SelectionExtensionOptions>({
    name: NAME,

    addOptions() {
        return {
            enabled: false,
            applyOnBlur: true,
            clearOnFocus: true,
            spotlightClass: 'spotlight',
        };
    },

    addCommands() {
        return {
            spotlightSelection:
                () =>
                ({ editor, tr }: { editor: Editor; tr: Transaction }) => {
                    if (!this.options.enabled) return false;

                    const { from, to } = editor.state.selection;
                    const hasSelection = from !== to;

                    if (!hasSelection) return false;

                    tr.setMeta(NAME, {
                        from,
                        to,
                        action: ACTION_TYPES.SPOTLIGHT,
                    });

                    return true;
                },

            clearSpotlight:
                () =>
                ({ tr }: { tr: Transaction }) => {
                    if (!this.options.enabled) return false;

                    tr.setMeta(NAME, {
                        action: ACTION_TYPES.CLEAR,
                    });

                    return true;
                },

            toggleSpotlightSelection:
                () =>
                ({ editor, tr }: { editor: Editor; tr: Transaction }) => {
                    if (!this.options.enabled) return false;

                    const { from, to } = editor.state.selection;
                    const hasSelection = from !== to;

                    if (!hasSelection) return false;

                    tr.setMeta(NAME, {
                        from,
                        to,
                        action: ACTION_TYPES.TOGGLE,
                    });

                    return true;
                },

            setSpotlightEnabled: (enabled: boolean) => () => {
                this.options.enabled = enabled;
                return true;
            },

            clearSelection:
                () =>
                ({ editor, tr }: { editor: Editor; tr: Transaction }) => {
                    const { from } = editor.state.selection;
                    const newSelection = TextSelection.create(tr.doc, from, from);
                    tr.setSelection(newSelection);
                    tr.setMeta(NAME, {
                        action: ACTION_TYPES.CLEAR_SELECTION,
                    });
                    return true;
                },
        };
    },

    addProseMirrorPlugins() {
        const pluginKey = new PluginKey(NAME);
        let activeDecoration = null;

        return [
            new Plugin({
                key: pluginKey,

                state: {
                    init() {
                        return DecorationSet.empty;
                    },

                    apply: (transaction, oldState) => {
                        const { selection, doc } = transaction;
                        const decoTransform = transaction.getMeta(NAME);

                        if (!this.options.enabled) {
                            return DecorationSet.empty;
                        }

                        if (!decoTransform) {
                            return oldState;
                        }

                        const hasSelection = selection && selection.from !== selection.to;

                        switch (decoTransform.action) {
                            case ACTION_TYPES.CLEAR:
                            case ACTION_TYPES.CLEAR_SELECTION:
                                activeDecoration = null;
                                return DecorationSet.empty;

                            case ACTION_TYPES.FOCUS:
                                if (this.options.clearOnFocus) {
                                    activeDecoration = null;
                                    return DecorationSet.empty;
                                }
                                return oldState;

                            case ACTION_TYPES.BLUR:
                                if (!hasSelection || !this.options.applyOnBlur) {
                                    return oldState;
                                }

                                activeDecoration = Decoration.inline(selection.from, selection.to, {
                                    class: this.options.spotlightClass,
                                });

                                return DecorationSet.create(doc, [activeDecoration]);

                            case ACTION_TYPES.SPOTLIGHT:
                                if (!hasSelection) {
                                    return oldState;
                                }

                                activeDecoration = Decoration.inline(selection.from, selection.to, {
                                    class: this.options.spotlightClass,
                                });

                                return DecorationSet.create(doc, [activeDecoration]);

                            case ACTION_TYPES.TOGGLE:
                                if (oldState.find().length > 0) {
                                    activeDecoration = null;
                                    return DecorationSet.empty;
                                }

                                if (hasSelection) {
                                    activeDecoration = Decoration.inline(selection.from, selection.to, {
                                        class: this.options.spotlightClass,
                                    });

                                    return DecorationSet.create(doc, [activeDecoration]);
                                }

                                return oldState;

                            default:
                                return oldState;
                        }
                    },
                },

                props: {
                    decorations(state) {
                        return this.getState(state);
                    },
                    handleDOMEvents: {
                        blur: (view) => {
                            if (!this.options.enabled || !this.options.applyOnBlur) {
                                return false;
                            }

                            const { tr } = view.state;
                            const transaction = tr.setMeta(NAME, {
                                from: tr.selection.from,
                                to: tr.selection.to,
                                action: ACTION_TYPES.BLUR,
                            });

                            view.dispatch(transaction);
                            return false;
                        },

                        focus: (view) => {
                            if (!this.options.enabled || !this.options.clearOnFocus) {
                                return false;
                            }

                            const { tr } = view.state;
                            const transaction = tr.setMeta(NAME, {
                                from: tr.selection.from,
                                to: tr.selection.to,
                                action: ACTION_TYPES.FOCUS,
                            });

                            view.dispatch(transaction);
                            return false;
                        },
                    },
                },
            }),
        ];
    },
});
