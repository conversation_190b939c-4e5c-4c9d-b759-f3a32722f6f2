import { FC } from 'react';
import { Modal, TitleBar, useAppBridge } from '@shopify/app-bridge-react';
import { BlockStack, Page } from '@shopify/polaris';
import { PageList } from '@/components';
import { Welcome } from './Welcome';
import { StepStart } from './StepStart';
import { Integrated } from './Integrated';
import { SupportChannels } from './SupportChannels';

export const HomePage: FC = () => {
    const shopify = useAppBridge();

    const handleOpen = () => {
        shopify.modal.show('builder-modal');
    };

    const handleClose = () => {
        shopify.modal.hide('builder-modal');
    };

    return (
        <>
            <Page>
                <BlockStack gap="500">
                    <button onClick={handleOpen}>Builder</button>
                    <StepStart />
                    <Welcome />
                    <PageList />
                    <Integrated />
                    <SupportChannels />
                </BlockStack>
            </Page>
            <Modal id="builder-modal" variant="max" src="/builder">
                <TitleBar title="Autoketing Builder">
                    <button onClick={handleClose}>Discard</button>
                    <button variant="primary">Save</button>
                </TitleBar>
            </Modal>
        </>
    );
};
