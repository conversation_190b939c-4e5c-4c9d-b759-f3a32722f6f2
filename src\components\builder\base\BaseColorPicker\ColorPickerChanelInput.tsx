import { forwardRef, HTMLAttributes, useEffect, useState } from 'react';
import type { ChannelInputProps } from '@zag-js/color-picker';
import { mergeProps } from '@zag-js/react';
import { CSSObject } from '@emotion/react';
import { Text } from '@shopify/polaris';
import { createSplitProps } from './types';
import { useColorPickerContext } from './use-color-picker-context.ts';

export type ColorPickerChannelInputProps = HTMLAttributes<HTMLInputElement> & ChannelInputProps;

export const ColorPickerChannelInput = forwardRef<HTMLInputElement, ColorPickerChannelInputProps>((props, ref) => {
    const [channelProps, localProps] = createSplitProps<ChannelInputProps>()(props, ['channel', 'orientation']);
    const colorPicker = useColorPickerContext();
    const [alpha, setAlpha] = useState(colorPicker.alpha * 100);

    const mergedProps = mergeProps(colorPicker.getChannelInputProps(channelProps), localProps, {
        onBlur: () => colorPicker.onValueChangeEnd?.(colorPicker),
    });

    useEffect(() => {
        setAlpha(Math.round(colorPicker.alpha * 100));
    }, [colorPicker.alpha]);

    if (props.channel === 'alpha') {
        return (
            <div css={{ position: 'relative' }}>
                <input
                    ref={ref}
                    data-scope="color-picker"
                    data-part="channel-input"
                    css={
                        {
                            width: '48px !important',
                            borderRight: 'none !important',
                            textAlign: 'left !important',
                            paddingLeft: colorPicker.alpha * 100 < 100 ? '12px' : '8px',
                        } as unknown as CSSObject
                    }
                    value={alpha}
                    onChange={(e) => {
                        setAlpha(+e.target.value);
                        colorPicker.setAlpha(+e.target.value / 100);
                    }}
                    onFocus={(e) => e.target.select()}
                    onBlur={() => colorPicker.onValueChangeEnd?.(colorPicker)}
                />
                <div
                    css={{
                        position: 'absolute',
                        right: '8px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: '#8A8A8A',
                        pointerEvents: 'none',
                    }}
                >
                    <Text as="span" variant="bodySm" tone="inherit">
                        %
                    </Text>
                </div>
            </div>
        );
    }

    return <input {...mergedProps} ref={ref} />;
});

ColorPickerChannelInput.displayName = 'ColorPickerChannelInput';
