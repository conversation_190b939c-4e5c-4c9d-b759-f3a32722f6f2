import { FC } from 'react';
import { Box, BoxProps } from '@shopify/polaris';
import { SettingsInput } from '@/components/builder/settings';
import './style.scss';

interface SettingsDimensionProps {
    blockId: string;
    boxProps?: BoxProps;
    isUpdateConfigs?: boolean;
    isForm?: boolean;
}

export const SettingsDimension: FC<SettingsDimensionProps> = ({ blockId, boxProps, isUpdateConfigs, isForm }) => {
    return (
        <Box {...boxProps}>
            <div className="settings-dimension">
                <SettingsInput
                    path="mt"
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="X"
                    direction="column"
                    inputProps={{
                        suffix: 'px',
                    }}
                />
                <SettingsInput
                    path="ml"
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Y"
                    direction="column"
                    inputProps={{
                        suffix: 'px',
                    }}
                />
                <SettingsInput
                    path="width"
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Width"
                    direction="column"
                    inputProps={{
                        suffix: 'px',
                    }}
                />
                <SettingsInput
                    path={isForm ? 'formHeight' : 'height'}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Height"
                    direction="column"
                    inputProps={{
                        suffix: isForm ? '%' : 'px',
                        disabled: isForm,
                    }}
                />
            </div>
        </Box>
    );
};
