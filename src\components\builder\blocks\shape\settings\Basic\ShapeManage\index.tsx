/* eslint-disable react-hooks/exhaustive-deps */
import { FC, ReactNode, useState, useEffect, useCallback, useMemo, Fragment } from 'react';
import { GridCellRenderer, MultiGrid } from 'react-virtualized';
import { Box, Button, InlineStack, TextField, Tabs, Icon, Modal } from '@shopify/polaris';
import { SearchIcon, XIcon } from '@shopify/polaris-icons';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';
import { useAppStore } from '@/stores/appStore';
import { BaseModal } from '@/components/builder/base/BaseModal';
import { apiAddress } from '@/configs/apiAddress';
import { ItemManageProps } from '@/stores/appStore/types';
import { httpRequest } from '@/configs/api';
import './styles.scss';
interface ShapeManageProps {
    elementTriggerProps?: { children: ReactNode } & Partial<typeof InlineStack>;
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

const SHAPE_TYPES = [
    { key: 'socials', label: 'Socials', flexPrefix: 'flex1-' },
    { key: 'pattern', label: 'Pattern', flexPrefix: 'flex2-' },
    { key: 'background', label: 'Background', flexPrefix: 'flex3-' },
    { key: 'arrows', label: 'Arrows', flexPrefix: 'flex4-' },
    { key: 'icons', label: 'Icons', flexPrefix: 'flex5-' },
    { key: 'customer', label: 'Customer', flexPrefix: 'flex6-' },
];

const fetchShapeData = async (type: string) => {
    const response = await httpRequest.get(`${apiAddress.shape}?currentPage=1&perPage=10000&type=${type}`);
    return response.data.result.data.data;
};

export const ShapeManage: FC<ShapeManageProps> = ({ elementTriggerProps, blockId, path }) => {
    const [valueSearchIcon, setValueSearchIcon] = useState('');
    const isOpenModalShape = useAppStore((state) => state.isOpenModalShape);
    const setIsOpenModalShape = useAppStore((state) => state.setIsOpenModalShape);
    const svgValue = getBlockProperty(`configs.${path}.svg`, blockId);
    const typeValue = getBlockProperty(`configs.${path}.type`, blockId);
    const imageValue = getBlockBPProperty(`background.image.url`, blockId);

    const [shapes, setShapes] = useState<Record<string, ItemManageProps[]>>({
        socials: [],
        pattern: [],
        background: [],
        arrows: [],
        icons: [],
        customer: [],
    });

    const [selectedTab, setSelectedTab] = useState(0);
    const [value, setValue] = useState(typeValue === 'svg' ? svgValue : imageValue);
    const [gridDimensions, setGridDimensions] = useState({ width: 0, height: 0, columns: 0 });

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const [iconToDelete, setIconToDelete] = useState<ItemManageProps | null>(null);
    const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);

    useEffect(() => {
        const updateGridDimensions = () => {
            const width = Math.min(document.documentElement.clientWidth - 140, 1288);
            const height = Math.min(document.documentElement.clientHeight - 314, 706);
            const columns = Math.min(Math.floor(document.documentElement.clientWidth / 130 - 1), 9);
            setGridDimensions({ width, height, columns });
        };
        updateGridDimensions();
        window.addEventListener('resize', updateGridDimensions);
        return () => window.removeEventListener('resize', updateGridDimensions);
    }, []);

    useEffect(() => {
        setValue(typeValue === 'svg' ? svgValue : imageValue);
    }, [isOpenModalShape, typeValue, svgValue, imageValue]);

    // Optimize: Fetch all shape types in parallel
    useEffect(() => {
        if (!isOpenModalShape.isOpen) return;

        let isMounted = true;
        const isFirstLoad =
            !shapes.customer ||
            shapes.customer.length === 0 ||
            Object.values(shapes).every((arr) => !arr || arr.length === 0);

        (async () => {
            if (isFirstLoad) {
                const results = await Promise.all(SHAPE_TYPES.map(({ key }) => fetchShapeData(key)));
                if (!isMounted) return;
                setShapes({
                    socials: results[0],
                    pattern: results[1],
                    background: results[2],
                    arrows: results[3],
                    icons: results[4],
                    customer: results[5],
                });
            } else {
                const customerData = await fetchShapeData('customer');
                if (!isMounted) return;
                setShapes((prevShapes) => ({
                    ...prevShapes,
                    customer: customerData,
                }));
            }
        })();

        return () => {
            isMounted = false;
        };
    }, [isOpenModalShape.isOpen]);

    // Optimize: Memoize filtered configs
    const filteredConfigs = useMemo(() => {
        const lowerSearchValue = valueSearchIcon.toLowerCase();
        return SHAPE_TYPES.reduce((acc, { key }) => {
            acc[key] = (shapes[key] || []).filter((item: { title: string }) =>
                item.title.toLowerCase().includes(lowerSearchValue),
            );
            return acc;
        }, {} as Record<string, ItemManageProps[]>);
    }, [valueSearchIcon, shapes]);

    // Optimize: Handlers
    const onTabsChange = useCallback((selectedTabIndex: number) => setSelectedTab(selectedTabIndex), []);
    const onChangeIcon = useCallback((val: string) => setValueSearchIcon(val), []);
    const onChange = useCallback((newValue: string) => setValue(newValue), []);
    const onClickDelete = useCallback(() => setValueSearchIcon(''), []);
    const onCancel = useCallback(() => setValue(''), []);
    const onOpenModalShape = useCallback((isOpen: boolean, tabs?: number) => setIsOpenModalShape(isOpen, tabs), []);
    const onClickAdd = useCallback(() => {
        updateBlockConfigsProperty(blockId, `${path}.svg`, value);
        if (selectedTab !== 0) {
            updateBlockConfigsProperty(blockId, `${path}.type`, 'svg');
            updateBlockProperty(blockId, currentDevice, `background.type`, 'color');
        } else {
            updateBlockProperty(blockId, currentDevice, `background.type`, 'image');
            updateBlockProperty(blockId, currentDevice, `background.image.url`, value);
            updateBlockConfigsProperty(blockId, `${path}.type`, 'image');
        }
        setIsOpenModalShape(false);
    }, [blockId, currentDevice, path, updateBlockConfigsProperty, value]);

    // Optimize: Single cell renderer for all tabs
    const createCellRenderer = useCallback(
        (configKey: string, flexPrefix: string) => {
            const cellRenderer: GridCellRenderer = ({ columnIndex, key, rowIndex, style }) => {
                const index = rowIndex * gridDimensions.columns + columnIndex;
                const iconItem = filteredConfigs[configKey][index];
                if (!iconItem) return null;

                const isSelected = value === (iconItem.image || iconItem.value);

                const commonStyle = {
                    ...style,
                    left: Number(style.left),
                    top: Number(style.top),
                    background: isSelected ? '#ebebeb' : 'transparent',
                };

                const handleClick = () => {
                    SHAPE_TYPES.forEach(({ flexPrefix: prefix }) => {
                        document.querySelectorAll(`[id^="${prefix}"]`).forEach((element) => {
                            (element as HTMLElement).style.background = 'transparent';
                        });
                    });
                    const element = document.querySelector(`#${flexPrefix}${key}`);
                    if (element instanceof HTMLElement) {
                        element.style.background = '#ebebeb';
                    }
                    onChange(iconItem.image || iconItem.value);
                };

                const handleDeleteIcon = (iconItem: ItemManageProps) => {
                    setIconToDelete(iconItem);
                    setIsConfirmDeleteOpen(true);
                };

                // Only show delete button for "customer" tab
                const isCustomerTab = configKey === 'customer';

                return (
                    <Fragment key={`${flexPrefix}-${key}`}>
                        {iconItem.image ? (
                            <div
                                className="shape-manage__item"
                                id={`${flexPrefix}${key}`}
                                style={commonStyle}
                                onClick={handleClick}
                            >
                                <img width="100%" height="100%" src={iconItem.image} alt={iconItem.title} />
                            </div>
                        ) : (
                            <div
                                className="shape-manage__item"
                                id={`${flexPrefix}${key}`}
                                style={commonStyle}
                                onClick={handleClick}
                            >
                                <div
                                    className="shape-manage__item__svg"
                                    style={{
                                        maskImage: `url("data:image/svg+xml,${encodeURIComponent(iconItem.value)}")`,
                                        WebkitMaskImage: `url("data:image/svg+xml,${encodeURIComponent(
                                            iconItem.value,
                                        )}")`,
                                    }}
                                />
                                {isCustomerTab && (
                                    <button
                                        className="shape-manage__item__delete"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteIcon(iconItem);
                                        }}
                                    >
                                        <Icon source={XIcon} />
                                    </button>
                                )}
                            </div>
                        )}
                    </Fragment>
                );
            };
            return cellRenderer;
        },
        [filteredConfigs, gridDimensions.columns, onChange, value],
    );

    // Optimize: Memoize tabs
    const tabs = useMemo(
        () =>
            SHAPE_TYPES.map(({ key, label }) => ({
                id: key,
                content: label,
                accessibilityLabel: label,
                panelID: `${key}-content`,
            })),
        [],
    );

    // Xác nhận xóa icon
    const handleConfirmDelete = async () => {
        if (!iconToDelete) return;
        const response = await httpRequest.delete(`${apiAddress.shape}/${iconToDelete.id}`);
        if (response.status === 200) {
            setShapes((prev) => ({
                ...prev,
                customer: prev.customer.filter((item) => item.id !== iconToDelete.id),
            }));
        }
        setIsConfirmDeleteOpen(false);
        setIconToDelete(null);
    };
    const handleCancelDelete = () => {
        setIsConfirmDeleteOpen(false);
        setIconToDelete(null);
    };

    return (
        <div>
            {/* Modal xác nhận xóa icon */}
            <Modal
                open={isConfirmDeleteOpen}
                onClose={handleCancelDelete}
                title="Confirm delete svg"
                primaryAction={{
                    content: 'Delete',
                    destructive: true,
                    onAction: handleConfirmDelete,
                }}
                secondaryActions={[
                    {
                        content: 'Cancel',
                        onAction: handleCancelDelete,
                    },
                ]}
            >
                <Modal.Section>Are you sure you want to delete this svg?</Modal.Section>
            </Modal>
            <BaseModal
                isOpen={isOpenModalShape.isOpen}
                onOpenChange={(isOpen) => {
                    if (isConfirmDeleteOpen) return;
                    onOpenModalShape(isOpen);
                }}
                onOk={onClickAdd}
                isDisabled={!value}
                onCancel={onCancel}
                okTitle="Change"
                cancelTitle="Cancel"
                elementContentProps={{}}
                elementTriggerProps={elementTriggerProps}
                textTooltip="Help"
                elementModalContentProps={{
                    title: 'Change shape',
                    children: (
                        <div className="shape-manage__content">
                            <div className="shape-manage__content__search">
                                <TextField
                                    label="Search for shape"
                                    labelHidden
                                    value={valueSearchIcon}
                                    onChange={onChangeIcon}
                                    prefix={<Icon source={SearchIcon} />}
                                    suffix={
                                        valueSearchIcon ? (
                                            <Button variant="tertiary" icon={XIcon} onClick={onClickDelete} />
                                        ) : null
                                    }
                                    placeholder="Search for shape"
                                    autoComplete="off"
                                />
                            </div>
                            <Tabs tabs={tabs} selected={selectedTab} onSelect={onTabsChange}>
                                <Box>
                                    {SHAPE_TYPES.map(({ key, flexPrefix }, idx) =>
                                        selectedTab === idx ? (
                                            <div key={key} className={`shape-manage__${key}`}>
                                                <MultiGrid
                                                    cellRenderer={createCellRenderer(key, flexPrefix)}
                                                    columnWidth={130}
                                                    columnCount={gridDimensions.columns}
                                                    height={gridDimensions.height}
                                                    rowHeight={130}
                                                    rowCount={Math.ceil(
                                                        (filteredConfigs[key]?.length || 0) / gridDimensions.columns,
                                                    )}
                                                    width={gridDimensions.width}
                                                    key={
                                                        key === 'customer' ? `customer-${shapes.customer.length}` : key
                                                    }
                                                />
                                            </div>
                                        ) : null,
                                    )}
                                </Box>
                            </Tabs>
                        </div>
                    ),
                }}
            />
        </div>
    );
};
