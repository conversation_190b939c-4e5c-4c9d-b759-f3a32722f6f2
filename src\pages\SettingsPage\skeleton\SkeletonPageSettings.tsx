import { SkeletonDisplayText, SkeletonBodyText, BlockStack, Card, Layout, SkeletonPage } from '@shopify/polaris';
import { FC } from 'react';

export const SkeletonPageSettings: FC = () => {
    return (
        <SkeletonPage>
            <Layout>
                <Layout.Section variant="oneThird">
                    <Card>
                        <BlockStack gap="200">
                            <SkeletonDisplayText size="large" />
                            <SkeletonBodyText lines={6} />
                        </BlockStack>
                    </Card>
                </Layout.Section>
                <Layout.Section>
                    <BlockStack gap="500">
                        <Card>
                            <BlockStack gap="200">
                                <SkeletonDisplayText size="small" />
                                <SkeletonBodyText />
                            </BlockStack>
                        </Card>
                        <Card>
                            <BlockStack gap="200">
                                <SkeletonDisplayText size="small" />
                                <SkeletonBodyText />
                            </BlockStack>
                        </Card>
                        <Card>
                            <BlockStack gap="200">
                                <SkeletonDisplayText size="small" />
                                <SkeletonBodyText />
                            </BlockStack>
                        </Card>
                        <Card>
                            <BlockStack gap="200">
                                <SkeletonDisplayText size="small" />
                                <SkeletonBodyText />
                            </BlockStack>
                        </Card>
                        <Card>
                            <BlockStack gap="200">
                                <SkeletonDisplayText size="small" />
                                <SkeletonBodyText />
                            </BlockStack>
                        </Card>
                    </BlockStack>
                </Layout.Section>
            </Layout>
        </SkeletonPage>
    );
};
