import { FC } from 'react';
import { TextProps } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseToggle } from '@/components/builder/base/BaseToggle';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseToggleProps = Parameters<typeof BaseToggle>[0];

interface SettingsToggleProps
    extends Pick<
        BaseItemLayoutProps,
        'direction' | 'containerClassName' | 'tooltipContent' | 'hasTooltip' | 'tooltipChildren'
    > {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    toggleProps?: Omit<BaseToggleProps, 'valueData' | 'onChange'>;
    valueMap?: {
        true: string;
        false: string;
    };
}

export const SettingsToggle: FC<SettingsToggleProps> = ({
    path,
    blockId,
    isUpdateConfigs,
    textProps,
    label,
    toggleProps,
    valueMap,
    ...otherProps
}) => {
    const value = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChange = (val: boolean) => {
        const valueToUpdate = valueMap ? valueMap[val ? 'true' : 'false'] : val;
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, valueToUpdate);
        } else {
            updateBlockProperty(blockId, currentDevice, path, valueToUpdate);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseToggle
                valueData={valueMap ? value === valueMap.true : value}
                onChange={handleChange}
                {...toggleProps}
            />
        </BaseItemLayout>
    );
};
