/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import {
    useFloating,
    offset,
    arrow,
    Placement,
    useClick,
    useDismiss,
    useInteractions,
    FloatingPortal,
    FloatingArrow,
} from '@floating-ui/react';
import './styles.scss';
import { BaseSlider } from '@/components/builder/base/BaseSlider';
import { BaseInput } from '@/components/builder/base/BaseInput';
import { RangeSliderValue } from '@shopify/polaris/build/ts/src/components/RangeSlider/types';
import { Suggestion } from '../Suggestion';
import { suggestionConfigs } from '../Suggestion/configs';
import { restrictValueInput } from '@/utils/restrictValueInput';
import { updateCssVariable, removeCssVariable } from '@/utils/cssVariableUtils';
export interface BaseModalShapeProps {
    type: 'margin' | 'padding';
    side: 'top' | 'right' | 'bottom' | 'left';
    isActive?: boolean;
    valueData?: number;
    unitData?: string;
    selectedBlockTarget?: HTMLElement;
    cssVariable?: string;
    onChangeValueData?(value: string): void;
    isOpen?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export const BaseModalShape: FC<BaseModalShapeProps> = ({
    type,
    side,
    isActive,
    valueData,
    unitData,
    onChangeValueData,
    isOpen,
    onOpenChange,
    selectedBlockTarget,
    cssVariable,
}) => {
    const [localValue, setLocalValue] = useState<string>(valueData?.toString() || '');

    const getPlacement = (): { placement: Placement; offset: any } => {
        switch (`${type}-${side}`) {
            case 'margin-top':
                return { placement: 'bottom', offset: { mainAxis: -160 } };
            case 'margin-right':
                return { placement: 'left', offset: -240 };
            case 'margin-bottom':
                return { placement: 'top', offset: { mainAxis: -140 } };
            case 'margin-left':
                return { placement: 'right', offset: -240 };
            case 'padding-top':
                return { placement: 'bottom', offset: { mainAxis: -75 } };
            case 'padding-right':
                return { placement: 'bottom-end', offset: { mainAxis: -34, alignmentAxis: -50 } };
            case 'padding-bottom':
                return { placement: 'top', offset: { mainAxis: -75 } };
            case 'padding-left':
                return { placement: 'bottom-start', offset: { mainAxis: -34, alignmentAxis: -50 } };
            default:
                return { placement: 'bottom', offset: 0 };
        }
    };

    const { placement, offset: offsetValue } = getPlacement();
    const arrowRef = useRef(null);
    const { refs, floatingStyles, context } = useFloating({
        open: isOpen,
        onOpenChange: onOpenChange,
        placement,
        middleware: [arrow({ element: arrowRef }), offset(offsetValue)],
    });

    const click = useClick(context, { event: 'click', toggle: true });
    const dismiss = useDismiss(context, {
        outsidePress: (event) => {
            const target = event.target as HTMLElement;
            if (target.tagName === 'INPUT') return false;
            return !target.closest('.base-modal-shape');
        },
    });
    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss]);

    const handleSliderChange = (value: RangeSliderValue) => {
        setLocalValue(value.toString());
        updateCssVariable(selectedBlockTarget as HTMLElement, cssVariable as string, value.toString(), 'px');
    };

    const handleInputChange = (value: number) => {
        setLocalValue(value.toString());
    };

    const handleBlur = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            const finalValue = restrictValueInput(e.currentTarget.value, 0, 100);
            setLocalValue(finalValue);
            onChangeValueData?.(finalValue);
        },
        [onChangeValueData],
    );

    const handleMouseUp = () => {
        const value = localValue === '' ? '0' : localValue;
        onChangeValueData?.(value);
        refs.floating.current?.focus();
        removeCssVariable(selectedBlockTarget as HTMLElement, cssVariable as string);
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            const finalValue = restrictValueInput((event.target as HTMLInputElement).value, 0, 100);
            onChangeValueData?.(finalValue);
            setLocalValue(finalValue);
        }
        if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            const finalValue = restrictValueInput((event.target as HTMLInputElement).value, 0, 100);
            updateCssVariable(selectedBlockTarget as HTMLElement, cssVariable as string, finalValue, 'px');
        }
    };

    const handleKeyUp = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, 0, 100);
                onChangeValueData?.(finalValue);
                setLocalValue(finalValue);
                removeCssVariable(selectedBlockTarget as HTMLElement, cssVariable as string);
            }
        },
        [onChangeValueData, selectedBlockTarget, cssVariable],
    );

    const onChooseSuggestion = (value: string) => {
        setLocalValue(value);
        onChangeValueData?.(value);
    };

    useEffect(() => {
        setLocalValue(valueData?.toString() || '');
    }, [valueData]);

    return (
        <>
            <button
                title={`${type}-${side}`}
                className={`base-modal-shape ${type}-${side} ${isActive ? 'active' : ''}`}
                ref={refs.setReference}
                {...getReferenceProps()}
            >
                <span className={`base-modal-shape-text ${type}-${side}`}>
                    {valueData ? `${valueData}${unitData}` : '-'}
                </span>
            </button>
            {isOpen && (
                <FloatingPortal>
                    <div ref={refs.setFloating} style={floatingStyles} {...getFloatingProps()} className="base-popover">
                        <div className="base-popover-arrow">
                            <FloatingArrow ref={arrowRef} context={context} width={12} height={6} />
                        </div>
                        <div className="base-popover-content">
                            <div className="base-popover-controls">
                                <div className="settings-slider-input__container__slider-input">
                                    <div onMouseUp={handleMouseUp}>
                                        <BaseSlider
                                            max={100}
                                            min={0}
                                            value={Number(localValue)}
                                            onChange={handleSliderChange}
                                            step={1}
                                        />
                                    </div>

                                    <BaseInput
                                        type="number"
                                        value={localValue}
                                        onChange={(value) => handleInputChange(Number(value))}
                                        max={100}
                                        min={0}
                                        step={1}
                                        inputMode="decimal"
                                        pattern="[0-9]*(.[0-9]+)?"
                                        suffix="px"
                                        onKeyDown={handleKeyDown}
                                        onBlur={handleBlur}
                                        onKeyUp={handleKeyUp}
                                        autoFocus
                                    />
                                </div>
                            </div>
                            <div className="base-popover-suggestions">
                                {suggestionConfigs.map((suggestion, index: number) => (
                                    <Suggestion
                                        key={index}
                                        selectedValue={localValue || ''}
                                        value={suggestion.value}
                                        onClick={() => onChooseSuggestion(suggestion.value)}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </FloatingPortal>
            )}
        </>
    );
};
