import { FC } from 'react';
import { SkeletonBodyText } from '@shopify/polaris';
import { SkeletonItem } from './SkeletonItem';
import './style.scss';

interface SkeletonProps {
    count: number;
    active?: boolean;
}

export const Skeleton: FC<SkeletonProps> = ({ count, active }) => {
    return (
        <div className={`page-list__skeleton ${active ? 'page-list__skeleton--active' : ''}`}>
            <div className="page-list__skeleton__item-header">
                <SkeletonBodyText lines={1} />
            </div>
            {Array.from({ length: count }).map((_, index) => (
                <SkeletonItem key={index} />
            ))}
            <div className="page-list__skeleton__item-footer">
                <SkeletonBodyText lines={1} />
            </div>
        </div>
    );
};
