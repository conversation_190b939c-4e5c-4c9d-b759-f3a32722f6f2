import { ChangeEvent, FC } from 'react';
import { ColorResult, HsvaColor, RgbaColor, hsvaToHex, hsvaToRgba, rgbaToHsva, color } from '@uiw/color-convert';
import { ChanelInput } from './ChanelInput';

interface HexaProps {
    hsva: HsvaColor;
    onChangeHex?(e: ChangeEvent<HTMLInputElement>, value: string | number): void;
    onChangeAlpha?(color: ColorResult): void;
}

export const Hexa: FC<HexaProps> = ({ hsva, onChangeHex, onChangeAlpha }) => {
    const rgba = (hsva ? hsvaToRgba(hsva) : {}) as RgbaColor;

    const handleChangeAlpha = (value: string | number) => {
        if (typeof value === 'number') {
            if (value < 0) value = 0;
            if (value > 100) value = 100;
            if (onChangeAlpha) {
                onChangeAlpha(color(rgbaToHsva({ ...rgba, a: value / 100 })));
            }
        }
    };

    return (
        <div
            css={{
                display: 'flex',
                width: '100%',
                height: '32px',
                border: '1px solid #E2E8F0',
                borderRadius: '8px',
                overflow: 'hidden',
            }}
        >
            <ChanelInput
                value={hsvaToHex(hsva)}
                onChange={onChangeHex}
                css={{
                    width: '100%',
                    flex: 1,
                    paddingInline: '12px',
                }}
            />
            <ChanelInput
                value={(rgba.a ? Math.round(rgba.a * 100) : 0) + '%'}
                onChange={(_, val) => handleChangeAlpha(val)}
                css={{
                    width: '48px',
                    textAlign: 'center',
                    borderRightStyle: 'none',
                }}
            />
        </div>
    );
};
