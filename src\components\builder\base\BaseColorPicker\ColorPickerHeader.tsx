import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import { Icon, Text } from '@shopify/polaris';
import { EyeDropperIcon, XIcon } from '@shopify/polaris-icons';
import { useColorPickerContext } from './use-color-picker-context.ts';

interface ColorPickerHeaderProps {
    isBackgroundColor?: boolean;
    onClose?: () => void;
    children?: React.ReactNode;
}
export const ColorPickerHeader = ({ isBackgroundColor, onClose, children }: ColorPickerHeaderProps) => {
    const colorPicker = useColorPickerContext();
    const isSupportEyeDropper = typeof window !== 'undefined' && 'EyeDropper' in window;
    return (
        <>
            <div
                css={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingBlock: isBackgroundColor ? '0px' : '12px',
                    paddingBlockEnd: '12px',
                    marginBottom: isBackgroundColor ? '0px' : '16px',
                    '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: '-16px',
                        right: '-16px',
                        height: '1px',
                        backgroundColor: '#ebebeb',
                    },
                }}
            >
                <Text as="span" variant="headingMd" fontWeight="medium">
                    Colors
                </Text>
                <div css={{ display: 'flex', alignItems: 'center', gap: '6px', marginRight: '-4px' }}>
                    <div
                        css={{
                            display: isSupportEyeDropper ? 'block' : 'none',
                            ':hover': {
                                background: '#F1F1F1',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            },
                        }}
                        onClick={
                            colorPicker.getEyeDropperTriggerProps()
                                .onClick as unknown as MouseEventHandler<HTMLDivElement>
                        }
                    >
                        <Icon source={EyeDropperIcon} />
                    </div>
                    <div
                        css={{
                            ':hover': {
                                background: '#F1F1F1',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            },
                        }}
                        onClick={isBackgroundColor ? onClose : () => colorPicker.setOpen(false)}
                    >
                        <Icon source={XIcon} />
                    </div>
                </div>
            </div>
            {isBackgroundColor && (
                <div
                    css={{
                        position: 'relative',
                        paddingBlock: '12px',
                        marginBottom: '16px',
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: 0,
                            left: '-16px',
                            right: '-16px',
                            height: '1px',
                            backgroundColor: '#ebebeb',
                        },
                    }}
                >
                    {children}
                </div>
            )}
        </>
    );
};
