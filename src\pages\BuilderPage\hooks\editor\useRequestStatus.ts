import { RequestName } from '@/stores/appStore/pages/types';
import { useAppStore } from '@/stores/appStore';
import { RequestStatus } from '@/stores/appStore/pages/types';

export const useRequestStatus = () => {
    const requestsStatus = useAppStore((state) => state.requestsStatus);
    const getStatus = (name: RequestName): RequestStatus => {
        return requestsStatus[name]?.status;
    };
    const isLoading = (name: RequestName = 'createPage'): boolean => {
        if (name === 'createPage') {
            return getStatus('createPage') === 'loading';
        }
        return false;
    };
    return { getStatus, isLoading };
};
