import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { SettingElementID } from '@/components/builder/settings/SettingElementID';
import { DATA_SET_AUTO_ID_INNER, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty } from '@/utils/shared';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import {
    SettingsBackgroundColor,
    SettingsColorPicker,
    SettingsFontFamily,
    SettingsInput,
    SettingsSelect,
    SettingsShadow,
    SettingsSliderInput,
    SettingBorder,
    SettingsSwitchTab,
} from '@/components/builder/settings';
import {
    textAlignOptions,
    textDecorationOptions,
    textFontStyleOptions,
    textFontWeightOptions,
    textTransformOptions,
    alignHorizontalOptions,
    lineSpacingOptions,
    letterSpacingOptions,
} from '@/components/builder/data/options';
import { SettingsLabel } from '@/components/builder/blocks/form/settings/Basic/components/FieldText/SettingsLabel';
import { SettingsPlaceholder } from '@/components/builder/blocks/form/settings/Basic/components/FieldText/SettingsPlaceholder';
import { SettingsInputText } from '@/components/builder/blocks/form/settings/Basic/components/FieldText/SettingsInputText';
import { SettingsSpace } from '@/components/builder/settings/SettingsSpace';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';

export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state?.selectedBlockId) as string;
    const typeTextField = getBlockBPProperty(`fieldType`, selectedBlockId);
    const buttonLetterSpacing = getBlockBPProperty(`buttonSubmit.buttonLetterSpacing.type`, selectedBlockId);
    const buttonLineSpacing = getBlockBPProperty(`buttonSubmit.buttonLineSpacing.type`, selectedBlockId);
    const selectedBlockTarget = document.querySelector(
        `[${DATA_SET_AUTO_ID_INNER}="${selectedBlockId}"]`,
    ) as HTMLElement;

    return (
        <BaseHasBorderLayout hasElementID>
            <SettingElementID value={selectedBlockId} />
            <BaseCollapse
                label="Styles"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <Box paddingBlockStart="300">
                    <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                        <BlockStack gap="400">
                            <SettingsBackgroundColor
                                blockId={selectedBlockId}
                                path="backgroundForm"
                                label="Background"
                                isUpdateConfigs={false}
                            />
                            <SettingsShadow
                                type="box-shadow"
                                blockId={selectedBlockId}
                                isUpdateConfigs={false}
                                path="boxShadow"
                                label="Box shadow"
                            />
                        </BlockStack>
                    </BaseItemLayout>
                </Box>
            </BaseCollapse>

            <BaseCollapse
                label="Border"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <SettingBorder isUpdateConfigs={false} path="formBorder" blockId={selectedBlockId} label="Border" />
            </BaseCollapse>

            <BaseCollapse
                label="General form"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <Box paddingBlockStart="300">
                    <BlockStack gap="300">
                        <SettingsSliderInput
                            isUpdateConfigs={false}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-space-title-field"
                            path="generalForm.spaceTitleField"
                            blockId={selectedBlockId}
                            direction="column"
                            title="Space between title and field"
                            min={0}
                            max={64}
                            step={1}
                            inputProps={{
                                type: 'number',
                                suffix: 'px',
                                min: 0,
                                max: 64,
                                step: 1,
                            }}
                        />
                        <SettingsSpace
                            label="Space"
                            path="generalForm.space"
                            blockId={selectedBlockId}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-general-space"
                            isMargin={false}
                            isPadding={true}
                            isUpdateConfig={false}
                        />
                        <SettingsSliderInput
                            isUpdateConfigs={false}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-vertical"
                            path="generalForm.vertical"
                            blockId={selectedBlockId}
                            direction="column"
                            title="Vertical"
                            min={0}
                            max={100}
                            step={1}
                            inputProps={{
                                type: 'number',
                                suffix: 'px',
                                min: 0,
                                max: 100,
                                step: 1,
                            }}
                        />
                        <SettingsSliderInput
                            isUpdateConfigs={false}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-horizontal"
                            path="generalForm.horizontal"
                            blockId={selectedBlockId}
                            direction="column"
                            title="Horizontal"
                            min={0}
                            max={100}
                            step={1}
                            inputProps={{
                                type: 'number',
                                suffix: 'px',
                                min: 0,
                                max: 100,
                                step: 1,
                            }}
                        />
                        <SettingsSwitchTab
                            options={alignHorizontalOptions}
                            isUpdateConfigs={false}
                            path="generalForm.align"
                            blockId={selectedBlockId}
                            label="Align"
                        />
                    </BlockStack>
                </Box>
            </BaseCollapse>

            <BaseCollapse
                label="Fields styles"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <Box paddingBlockStart="300">
                    <BlockStack gap="300">
                        <SettingsBackgroundColor
                            blockId={selectedBlockId}
                            path="fieldsStyles.background"
                            label="Background"
                            isUpdateConfigs={false}
                        />

                        <SettingBorder
                            isUpdateConfigs={false}
                            path="fieldsStyles.border"
                            blockId={selectedBlockId}
                            label="Border"
                        />

                        <SettingsSwitchTab
                            options={[
                                {
                                    id: 'label',
                                    contentTooltip: 'Label',
                                },
                                {
                                    id: 'input',
                                    contentTooltip: 'Input',
                                },
                                {
                                    id: 'placeholder',
                                    contentTooltip: 'Placeholder',
                                },
                            ]}
                            isUpdateConfigs={false}
                            path="fieldType"
                            blockId={selectedBlockId}
                            label="Text field"
                            switchTabProps={{
                                fullWidth: true,
                            }}
                        />
                        {typeTextField === 'label' && (
                            <SettingsLabel blockId={selectedBlockId} path="fieldsLabel" isUpdateConfigs={false} />
                        )}
                        {typeTextField === 'input' && (
                            <SettingsInputText
                                blockId={selectedBlockId}
                                path="fieldsInput"
                                isUpdateConfigs={false}
                                label="Input"
                            />
                        )}
                        {typeTextField === 'placeholder' && (
                            <SettingsPlaceholder
                                blockId={selectedBlockId}
                                path="fieldsPlaceholder"
                                isUpdateConfigs={false}
                            />
                        )}
                        <SettingsSpace
                            label="Space in input"
                            blockId={selectedBlockId}
                            path={`fieldsSpacing`}
                            isUpdateConfig={false}
                            isMargin={false}
                            isPadding={true}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-fields-spacing"
                        />
                    </BlockStack>
                </Box>
            </BaseCollapse>

            <BaseCollapse
                label="Form title"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <Box paddingBlockStart="300">
                    <BlockStack gap="300">
                        <SettingsSpace
                            path="titleForm.titleSpacing"
                            blockId={selectedBlockId}
                            isUpdateConfig={false}
                            label="Space"
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-title-spacing"
                        />
                        <SettingsFontFamily
                            blockId={selectedBlockId}
                            path={`titleForm.fontFamily`}
                            isUpdateConfigs={false}
                        />

                        <SettingsSelect
                            label="Font Weight"
                            blockId={selectedBlockId}
                            path={`titleForm.fontWeight`}
                            isUpdateConfigs={false}
                            options={textFontWeightOptions}
                        />
                        <SettingsInput
                            label="Font Size"
                            blockId={selectedBlockId}
                            path={`titleForm.fontSize`}
                            isUpdateConfigs={false}
                            inputProps={{
                                min: 4,
                                suffix: 'px',
                            }}
                        />
                        <SettingsSelect
                            label="Font style"
                            blockId={selectedBlockId}
                            path={`titleForm.fontStyle`}
                            isUpdateConfigs={false}
                            options={textFontStyleOptions}
                        />
                        <SettingsSwitchTab
                            label="Text Align"
                            blockId={selectedBlockId}
                            path={`titleForm.textAlign`}
                            isUpdateConfigs={false}
                            options={textAlignOptions}
                        />
                        <SettingsSwitchTab
                            label="Text Decoration"
                            blockId={selectedBlockId}
                            path={`titleForm.textDecoration`}
                            isUpdateConfigs={false}
                            options={textDecorationOptions}
                        />
                        <SettingsSelect
                            label="Font transform"
                            blockId={selectedBlockId}
                            path={`titleForm.textTransform`}
                            isUpdateConfigs={false}
                            options={textTransformOptions}
                        />
                        <SettingsShadow
                            type="text-shadow"
                            blockId={selectedBlockId}
                            isUpdateConfigs={false}
                            path={`titleForm.textShadow`}
                            label="Text shadow"
                        />
                        <SettingsColorPicker
                            label="Text Color"
                            blockId={selectedBlockId}
                            path={`titleForm.color`}
                            isUpdateConfigs={false}
                        />
                        <SettingsSliderInput
                            isUpdateConfigs={false}
                            path={`titleForm.lineHeight`}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-title-line-height"
                            blockId={selectedBlockId}
                            title="Line height"
                            direction="column"
                            min={0}
                            max={10}
                            step={0.1}
                            inputProps={{
                                min: 0,
                                max: 10,
                                step: 0.1,
                                align: 'center',
                            }}
                        />
                        <SettingsSliderInput
                            isUpdateConfigs={false}
                            path={`titleForm.letterSpacing`}
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-title-letter-spacing"
                            blockId={selectedBlockId}
                            title="Letter spacing"
                            direction="column"
                            min={0}
                            max={10}
                            step={0.1}
                            inputProps={{
                                min: 0,
                                max: 10,
                                step: 0.1,
                                suffix: 'px',
                            }}
                        />
                    </BlockStack>
                </Box>
            </BaseCollapse>

            <BaseCollapse
                label="Submit button"
                labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
            >
                <Box paddingBlockStart="300">
                    <BlockStack gap="400">
                        <SettingsSpace
                            path="buttonSubmit.buttonSpacing"
                            blockId={selectedBlockId}
                            isUpdateConfig={false}
                            label="Space"
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-button-spacing"
                        />

                        <SettingsSliderInput
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-space-title-field"
                            isUpdateConfigs={false}
                            path="arrangement.spaceTitleField"
                            blockId={selectedBlockId}
                            direction="column"
                            title="Space between title and field"
                            min={0}
                            max={64}
                            step={1}
                            inputProps={{
                                type: 'number',
                                suffix: 'px',
                                min: 0,
                                max: 64,
                                step: 1,
                            }}
                        />

                        <SettingsSwitchTab
                            options={alignHorizontalOptions}
                            isUpdateConfigs={false}
                            path="arrangement.buttonAlign"
                            blockId={selectedBlockId}
                            label="Align"
                        />

                        <SettingsFontFamily
                            blockId={selectedBlockId}
                            path={`buttonSubmit.fontFamily`}
                            isUpdateConfigs={false}
                        />

                        <SettingsSelect
                            label="Font Weight"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.fontWeight`}
                            isUpdateConfigs={false}
                            options={textFontWeightOptions}
                        />

                        <SettingsSelect
                            label="Font style"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.fontStyle`}
                            isUpdateConfigs={false}
                            options={textFontStyleOptions}
                        />

                        <SettingsSelect
                            label="Text transform"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.textTransform`}
                            isUpdateConfigs={false}
                            options={textTransformOptions}
                        />

                        <SettingsInput
                            label="Font Size"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.fontSize`}
                            isUpdateConfigs={false}
                            inputProps={{
                                min: 4,
                                suffix: 'px',
                            }}
                        />

                        <SettingsBackgroundColor
                            blockId={selectedBlockId}
                            path={`buttonSubmit.background`}
                            isUpdateConfigs={false}
                            label="Background"
                        />

                        <SettingsSliderInput
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-button-width"
                            isUpdateConfigs={false}
                            path={`buttonSubmit.buttonWidth`}
                            blockId={selectedBlockId}
                            title="Width"
                            direction="column"
                            min={10}
                            max={100}
                            step={1}
                            inputProps={{
                                min: 10,
                                step: 1,
                                max: 100,
                                suffix: '%',
                            }}
                        />
                        <SettingsSliderInput
                            selectedBlockTarget={selectedBlockTarget}
                            cssVariable="--form-button-height"
                            isUpdateConfigs={false}
                            path={`buttonSubmit.buttonHeight`}
                            blockId={selectedBlockId}
                            title="Height"
                            direction="column"
                            min={10}
                            step={1}
                            inputProps={{
                                min: 10,
                                step: 1,
                                suffix: 'px',
                            }}
                        />

                        <SettingsSwitchTab
                            label="Text Align"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.textAlign`}
                            isUpdateConfigs={false}
                            options={textAlignOptions}
                        />
                        <SettingsSwitchTab
                            label="Text Decoration"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.textDecoration`}
                            isUpdateConfigs={false}
                            options={textDecorationOptions}
                        />

                        <SettingsColorPicker
                            label="Text Color"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.color`}
                            isUpdateConfigs={false}
                        />

                        <SettingsSelect
                            label="Letter spacing"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.buttonLetterSpacing.type`}
                            isUpdateConfigs={false}
                            options={letterSpacingOptions}
                        />

                        {buttonLetterSpacing === 'custom' && (
                            <SettingsSliderInput
                                selectedBlockTarget={selectedBlockTarget}
                                cssVariable="--form-button-letter-spacing"
                                isUpdateConfigs={false}
                                path={`buttonSubmit.buttonLetterSpacing.value`}
                                blockId={selectedBlockId}
                                title="Letter spacing"
                                direction="column"
                                min={0}
                                max={10}
                                step={0.1}
                                inputProps={{
                                    min: 0,
                                    max: 10,
                                    step: 0.1,
                                    suffix: 'px',
                                }}
                            />
                        )}

                        <SettingsSelect
                            label="Line spacing"
                            blockId={selectedBlockId}
                            path={`buttonSubmit.buttonLineSpacing.type`}
                            isUpdateConfigs={false}
                            options={lineSpacingOptions}
                        />

                        {buttonLineSpacing === 'custom' && (
                            <SettingsSliderInput
                                selectedBlockTarget={selectedBlockTarget}
                                cssVariable="--form-button-line-height"
                                isUpdateConfigs={false}
                                path={`buttonSubmit.buttonLineSpacing.value`}
                                blockId={selectedBlockId}
                                title="Line spacing"
                                direction="column"
                                min={0}
                                max={10}
                                step={0.1}
                                inputProps={{
                                    min: 0,
                                    max: 10,
                                    step: 0.1,
                                    suffix: 'px',
                                }}
                            />
                        )}

                        <SettingsShadow
                            type="text-shadow"
                            blockId={selectedBlockId}
                            isUpdateConfigs={false}
                            path={`buttonSubmit.textShadow`}
                            label="Text shadow"
                        />
                    </BlockStack>
                    <BlockStack gap="400">
                        <SettingBorder
                            isUpdateConfigs={false}
                            path={`buttonSubmit.buttonBorder`}
                            blockId={selectedBlockId}
                            label="Border"
                        />
                        <SettingsShadow
                            type="box-shadow"
                            blockId={selectedBlockId}
                            isUpdateConfigs={false}
                            path={`buttonSubmit.boxShadow`}
                            label="Box shadow"
                        />
                    </BlockStack>
                </Box>
            </BaseCollapse>
        </BaseHasBorderLayout>
    );
};
