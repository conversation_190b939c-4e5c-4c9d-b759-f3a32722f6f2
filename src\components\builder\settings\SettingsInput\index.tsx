import { FC, useCallback, useEffect, useState } from 'react';
import { BaseInput } from '@/components/builder/base/BaseInput';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '../../base/BaseItemLayout';
import { TextProps } from '@shopify/polaris';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseInputProps = Parameters<typeof BaseInput>[0];
interface SettingsInputProps
    extends Pick<
        BaseItemLayoutProps,
        'direction' | 'containerClassName' | 'tooltipContent' | 'hasTooltip' | 'tooltipChildren'
    > {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    inputProps?: Omit<BaseInputProps, 'value' | 'onChange'>;
    textProps?: Omit<Partial<TextProps>, 'children'>;
}

export const SettingsInput: FC<SettingsInputProps> = ({
    path,
    blockId,
    isUpdateConfigs,
    inputProps,
    textProps,
    label,
    ...otherProps
}) => {
    const inputType = inputProps?.type || 'number';
    const pathFromTypeValue = inputType === 'number' ? `${path}.val` : path;
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const inputValue = isUpdateConfigs
        ? getBlockProperty(`configs.${pathFromTypeValue}`, blockId)
        : getBlockBPProperty(`${pathFromTypeValue}`, blockId);

    const [localValue, setLocalValue] = useState(inputValue);

    useEffect(() => {
        setLocalValue(inputValue);
    }, [inputValue]);

    const onChange = (value: string) => {
        setLocalValue(value);
    };

    const handleValueUpdate = useCallback(
        (rawValue: string) => {
            const numValue = Number(rawValue);
            const minValue = Number(inputProps?.min ?? 0);
            const finalValue = !isNaN(numValue) && numValue < minValue ? String(minValue) : rawValue;

            setLocalValue(finalValue);

            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, pathFromTypeValue, finalValue);
            } else {
                updateBlockProperty(blockId, currentDevice, pathFromTypeValue, finalValue);
            }
        },
        [
            blockId,
            currentDevice,
            inputProps?.min,
            isUpdateConfigs,
            pathFromTypeValue,
            setLocalValue,
            updateBlockConfigsProperty,
            updateBlockProperty,
        ],
    );

    const onBlur = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            handleValueUpdate(e.target.value);
        },
        [handleValueUpdate],
    );

    const onKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
                handleValueUpdate((e.target as HTMLInputElement).value);
            }
        },
        [handleValueUpdate],
    );

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };
    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseInput
                value={localValue}
                onChange={onChange}
                onBlur={onBlur}
                max={300}
                min={0}
                {...inputProps}
                onKeyDown={onKeyDown}
            />
        </BaseItemLayout>
    );
};
