import { useState, useMemo } from 'react';
import { BlockStack, Box } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { renderNodes } from '@/utils/nodes';
import { SettingElementID } from '@/components/builder/settings';
import {
    BaseCollapse,
    BaseHasBorderLayout,
    BaseItemLayout,
    BaseSelect,
    BaseSwitchTab,
} from '@/components/builder/base';
import {
    DisplayType,
    TabStatus,
    createRenderMap,
    displayOptions,
    getDisplaySettings,
    statusOptions,
} from '../../utils';

export const Basic = () => {
    const [display, setDisplay] = useState<DisplayType>('tabContainer');
    const [status, setStatus] = useState<TabStatus>('normal');
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    const handleDisplayChange = (value: string) => {
        setDisplay(value as DisplayType);
    };

    const handleStatusChange = (value: string) => {
        setStatus(value as TabStatus);
    };

    const renderMap = useMemo(
        () => createRenderMap(selectedBlockId, display, status),
        [selectedBlockId, display, status],
    );
    const currentSettings = useMemo(() => getDisplaySettings(renderMap, display, status), [renderMap, display, status]);

    if (!selectedBlockId) return null;

    return (
        <BaseHasBorderLayout hasElementID>
            <SettingElementID value={selectedBlockId} />
            <div css={{ display: 'flex', flexDirection: 'column', rowGap: '16px' }}>
                <BaseItemLayout
                    textProps={{ as: 'p', variant: 'bodyMd', fontWeight: 'medium', children: 'Tab display' }}
                >
                    <BaseSelect options={displayOptions} value={display} onChange={handleDisplayChange} />
                </BaseItemLayout>

                {display === 'tabs' && (
                    <BaseSwitchTab options={statusOptions} valueData={status} onChange={handleStatusChange} fullWidth />
                )}
            </div>

            {currentSettings.styles && currentSettings.styles.length > 0 && (
                <BaseCollapse label="Styles">
                    <Box paddingBlockStart="400">
                        <BlockStack gap="400">{renderNodes(currentSettings.styles)}</BlockStack>
                    </Box>
                </BaseCollapse>
            )}

            {currentSettings.border && currentSettings.border.length > 0 && (
                <BaseCollapse label="Border">
                    <Box paddingBlockStart="400">
                        <BlockStack gap="400">{renderNodes(currentSettings.border)}</BlockStack>
                    </Box>
                </BaseCollapse>
            )}

            {currentSettings.text && currentSettings.text.length > 0 && (
                <BaseCollapse label="Text">
                    <Box paddingBlockStart="400">
                        <BlockStack gap="400">{renderNodes(currentSettings.text)}</BlockStack>
                    </Box>
                </BaseCollapse>
            )}
        </BaseHasBorderLayout>
    );
};
