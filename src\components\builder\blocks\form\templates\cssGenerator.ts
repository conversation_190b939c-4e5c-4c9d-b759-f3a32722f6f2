/* eslint-disable @typescript-eslint/no-explicit-any */
import { getBlockClassName } from '@/utils/cssBlockGenerator';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { 
  generateBackground, 
  generateBorder, 
  generateSubmitButtonStyles, 
  generateTypography, 
  transformInputToTypography, 
  transformPlaceholderToTypography 
} from '@/components/builder/data/utils/CssGenerator/utils';
import { generateUnitValue } from '@/components/builder/data/utils/CssGenerator/helper';
import { transformLabelToTypography } from '@/components/builder/data/utils/CssGenerator/utils';
import { isValidCssValue, isValidUnitValue } from '@/utils/cssValidator';


export const generateCss = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile'): string => {
    if (!block.bpConfigs?.[device]) return '';

    const bpConfig = block.bpConfigs[device];
    const config = block.configs;
    
    const isValid = {
      gridProps:isValidCssValue(bpConfig.gridColStart) && 
                isValidCssValue(bpConfig.gridColEnd) && 
                isValidCssValue(bpConfig.gridRowStart) && 
                isValidCssValue(bpConfig.gridRowEnd),
      formBorder: isValidCssValue(bpConfig.formBorder),
      titleForm: isValidCssValue(bpConfig.titleForm),
      generalForm: isValidCssValue(bpConfig.generalForm),
      fieldSizes: isValidCssValue(bpConfig.fieldSizes),
      fieldsLabel: isValidCssValue(bpConfig.fieldsLabel),
      fieldsStyles: isValidCssValue(bpConfig.fieldsStyles),
      fieldsPlaceholder: isValidCssValue(bpConfig.fieldsPlaceholder),
      fieldsInput: isValidCssValue(bpConfig.fieldsInput),
      buttonSubmit: isValidCssValue(bpConfig.buttonSubmit)
    };
    
    if (!Array.isArray((config as any).form) || (config as any).form.length === 0) {
      return '';
    }
    
    const className = getBlockClassName('form');
    const outer = `.${className}-wrapper[data-auto-id="${blockId}"]`;
    let css = '';

    if (isValid.gridProps) {
      css += `${outer} {
          display: block;
          grid-column-start: ${bpConfig.gridColStart};
          grid-column-end: ${bpConfig.gridColEnd};
          grid-row-start: ${bpConfig.gridRowStart};
          grid-row-end: ${bpConfig.gridRowEnd};
          ${isValidUnitValue(bpConfig.mt) ? `margin-top: ${bpConfig.mt.val}${bpConfig.mt.unit || 'px'};` : ''}
          ${isValidUnitValue(bpConfig.ml) ? `margin-left: ${bpConfig.ml.val}${bpConfig.ml.unit || 'px'};` : ''}
          ${isValidCssValue(bpConfig.justifySelf) ? `justify-self: ${bpConfig.justifySelf};` : ''}
          ${isValidCssValue(bpConfig.alignSelf) ? `align-self: ${bpConfig.alignSelf};` : ''}
      }\n`;
    }

    if (isValid.formBorder) {
      const paddingValues = isValid.generalForm && bpConfig.generalForm?.space?.padding ? 
        ['top', 'right', 'bottom', 'left'].map(dir => {
          return isValidUnitValue(bpConfig.generalForm.space.padding[dir]) ? 
            generateUnitValue(bpConfig.generalForm.space.padding[dir]) : 
            '0';
        }).join(' ') : 
        '0';
      
      css += `${outer} .atk-form {
         height: 100%;
         ${generateBorder(bpConfig.formBorder)}
         padding: ${paddingValues}; 
      }\n`;
    }

    if (isValid.titleForm) {
      const paddingValues = bpConfig.titleForm?.titleSpacing?.padding ? 
        ['top', 'right', 'bottom', 'left'].map(dir => {
          return isValidUnitValue(bpConfig.titleForm.titleSpacing.padding[dir]) ? 
            generateUnitValue(bpConfig.titleForm.titleSpacing.padding[dir]) : 
            '0';
        }).join(' ') : 
        '0';
        
      const marginValues = bpConfig.titleForm?.titleSpacing?.margin ? 
        ['top', 'right', 'bottom', 'left'].map(dir => {
          return isValidUnitValue(bpConfig.titleForm.titleSpacing.margin[dir]) ? 
            generateUnitValue(bpConfig.titleForm.titleSpacing.margin[dir]) : 
            '0';
        }).join(' ') : 
        '0';
      
      css += `${outer} .atk-form-title {
          ${generateTypography(bpConfig.titleForm, '--atk-form-title-letter-spacing', '--atk-form-title-line-height')}
          padding: ${paddingValues};
          margin: ${marginValues};
      }\n`;
    }
    
    if (isValid.generalForm) {
      css += `${outer} .atk-form-field-wrapper {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          ${isValidCssValue(bpConfig.generalForm.align) ? `justify-content: ${bpConfig.generalForm.align};` : ''}
          ${(isValidUnitValue(bpConfig.generalForm.vertical) && isValidUnitValue(bpConfig.generalForm.horizontal)) ? 
            `margin: calc(${generateUnitValue(bpConfig.generalForm.vertical)}/2) calc(${generateUnitValue(bpConfig.generalForm.horizontal)}/2*(-1));` : ''}
      }\n`;
    }
    
    (config as any).form.forEach((field: any) => {
      if (!field || !field.id || !field.key || !isValid.fieldSizes || !bpConfig.fieldSizes[field.key]) {
        return;
      }
      
      const fieldSizes = bpConfig.fieldSizes[field.key];
      const hasValidFieldWidth = isValidUnitValue(fieldSizes?.fieldWidth);
      const hasValidFieldHeight = isValidUnitValue(fieldSizes?.fieldHeight);
      const hasValidFieldSpacing = isValidUnitValue(bpConfig.generalForm?.spaceTitleField);
      css += `${outer} .atk-form-field-separator-line[data-auto-id="${field.id}"] {
        display: ${field.separateLine ? 'block' : 'none'};
        width: ${field.separateLine ? '100%' : '0%'};
      }\n`;
      
      css += `${outer} .atk-form-field[data-auto-id="${field.id}"] {
          display: flex;
          flex-direction: column;
          ${hasValidFieldSpacing ? `row-gap: ${generateUnitValue(bpConfig.generalForm.spaceTitleField)};` : ''}
          width: 100%;
          ${hasValidFieldWidth ? `max-width: ${fieldSizes.fieldWidth.val}${fieldSizes.fieldWidth.unit || 'px'};` : ''}
          ${(isValidUnitValue(bpConfig.generalForm?.vertical) && isValidUnitValue(bpConfig.generalForm?.horizontal)) ? 
            `padding: calc(${generateUnitValue(bpConfig.generalForm.vertical)}/2) calc(${generateUnitValue(bpConfig.generalForm.horizontal)}/2);` : ''}
      }\n`;
      
      if (field.type !== 'checkbox' && field.type !== 'radio' && hasValidFieldHeight) {
        css += `${outer} .atk-form-field[data-auto-id="${field.id}"] input, 
                ${outer} .atk-form-field[data-auto-id="${field.id}"] textarea {
            min-height: ${fieldSizes.fieldHeight.val}${fieldSizes.fieldHeight.unit || 'px'};
        }\n`;
      }

      if (field.type === 'checkbox' && isValidUnitValue(fieldSizes?.checkboxSize)) {
        css += `${outer} .atk-form-field[data-auto-id="${field.id}"] .atk-form-checkbox[type="checkbox"] {
            width: ${fieldSizes.checkboxSize.val}${fieldSizes.checkboxSize.unit || 'px'};
            height: ${fieldSizes.checkboxSize.val}${fieldSizes.checkboxSize.unit || 'px'};
        }\n`;

        css += `${outer} .atk-form-checkbox-wrapper {
            display: flex;
            flex-wrap: wrap;
            row-gap: 6px;
            align-items: start;
            flex-direction: ${field.direction || 'column'};
            column-gap: 12px;
        }
        
        ${outer} .atk-form-checkbox-element {
            display: flex;
            row-gap: 6px;
            flex-direction: column;
        }
        
        ${outer} .atk-form-checkbox-element-wrapper {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        ${outer} .atk-form-checkbox-element-inner {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        ${outer} .atk-form-checkbox[type="checkbox"]:checked {
            border-color: transparent;
            background-color: #059669;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
        }\n`;
      }
      
      // Radio styles
      if (field.type === 'radio' && isValidUnitValue(fieldSizes?.radioSize)) {
        css += `${outer} .atk-form-field[data-auto-id="${field.id}"] .atk-form-radio[type="radio"] {
            width: ${fieldSizes.radioSize.val}${fieldSizes.radioSize.unit || 'px'};
            height: ${fieldSizes.radioSize.val}${fieldSizes.radioSize.unit || 'px'};
        }\n`;

        // Static radio styles
        css += `${outer} .atk-form-radio-wrapper {
            display: flex;
            flex-wrap: wrap;
            row-gap: 6px;
            align-items: start;
            flex-direction: ${field.direction || 'column'};
            column-gap: 12px;
        }
        
        ${outer} .atk-form-radio-element {
            display: flex;
            row-gap: 6px;
            flex-direction: column;
        }
        
        ${outer} .atk-form-radio-element-wrapper {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        ${outer} .atk-form-radio-element-inner {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        ${outer} .atk-form-radio[type="radio"]:checked::after {
            position: absolute;
            content: "";
            width: 6px;
            height: 6px;
            background-color: white;
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }\n`;
      }

      // Dropdown styles
      if (field.type === 'dropdown' && hasValidFieldHeight) {
        css += `${outer} .atk-form-field[data-auto-id="${field.id}"] select {
            min-height: ${fieldSizes.fieldHeight.val}${fieldSizes.fieldHeight.unit || 'px'};
            appearance: none !important;
            background-image: url(https://cdn-master.shopone.io/master-icons/svgFree/chevron-up-1.svg) !important;
            background-size: 16px !important;
            background-repeat: no-repeat !important;
            background-position: calc(100% - 16px) center !important;
        }\n`;
      }
    });

    // Label styles
    if (isValid.fieldsLabel) {
      css += `${outer} .atk-form-field label {
          ${generateTypography(transformLabelToTypography(bpConfig.fieldsLabel))}
      }\n`;
    }
    
    // Input styles
    if (isValid.fieldsStyles) {
      let inputCss = '';
      
      if (bpConfig.fieldsStyles.border) {
        inputCss += generateBorder(bpConfig.fieldsStyles.border);
      }
      
      if (bpConfig.fieldsStyles.background) {
        inputCss += generateBackground(bpConfig.fieldsStyles.background);
      }
      
      if (bpConfig.fieldsSpacing?.padding) {
        const paddingDirs = ['top', 'right', 'bottom', 'left'];
        const validPaddings = paddingDirs.every(dir => 
          isValidUnitValue(bpConfig.fieldsSpacing.padding[dir])
        );
        
        if (validPaddings) {
          const paddingValues = paddingDirs.map(dir => 
            generateUnitValue(bpConfig.fieldsSpacing.padding[dir])
          ).join(' ');
          
          inputCss += `padding: ${paddingValues};`;
        }
      }
      
      if (inputCss) {
        css += `${outer} .atk-form-field input,
        ${outer} .atk-form-field textarea,
        ${outer} .atk-form-field select {
          ${inputCss}
        }\n`;
      }
    }

    // Placeholder styles
    if (isValid.fieldsPlaceholder) {
      css += `
      ${outer} .atk-form-field input::placeholder,
      ${outer} .atk-form-field textarea::placeholder {
        ${generateTypography(transformPlaceholderToTypography(bpConfig.fieldsPlaceholder))}
      }\n`;
    }

    // Input text styles
    if (isValid.fieldsInput) {
      css += `${outer} .atk-form-field input,
      ${outer} .atk-form-field textarea,
      ${outer} .atk-form-field select,
      ${outer} .atk-form-field .atk-form-checkbox-element-label,
      ${outer} .atk-form-field .atk-form-radio-element-label {
        ${generateTypography(transformInputToTypography(bpConfig.fieldsInput))}
      }\n`;
    }

    // Submit button wrapper
    css += `${outer} .atk-form-submit-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        ${isValidUnitValue(bpConfig.generalForm.spaceTitleField) ? `padding-top: ${generateUnitValue(bpConfig.generalForm.spaceTitleField)};` : ''}
    }\n`;

    // Submit button styles
    if (isValid.buttonSubmit) {
      css += `${outer} .atk-form-submit-wrapper .atk-button-submit {
        cursor: pointer;
        ${generateSubmitButtonStyles(bpConfig.buttonSubmit)}
      }\n`;
    }
  
    // Media queries
    if ( config.displayOnDesktop === false) {
      css += `@media only screen and (max-width: 767px) {
        ${outer} {
          display: none !important;
        }
      }\n`;
    }

    if (config.displayOnMobile === false) {
      css += `@media only screen and (min-width: 768px) {
        ${outer} {
          display: none !important;
        }
      }\n`;
    }

    css += `
      .atk-form-success-message {
        text-align:center;
        color:#456C54;
        margin-top:10px;
        margin-bottom:10px
      }
    `;

    css += `  
      .atk-form-error-message {
        text-align:center;
        color: red;
        margin-top:10px;
        margin-bottom:10px
      }
    `;
    css += `
      .atk-form-wrapper .animation {
        animation-name: ${(config as any).animation.type};
        animation-duration: ${(config as any).animation.duration.val}${(config as any).animation.duration.unit};
        animation-iteration-count: ${(config as any).animation.loop};
        animation-delay: ${(config as any).animation.delay.val}${(config as any).animation.delay.unit};
        -webkit-animation-name: ${(config as any).animation.type};
        -webkit-animation-duration: ${(config as any).animation.duration.val}${(config as any).animation.duration.unit};
        -webkit-animation-iteration-count: ${(config as any).animation.loop};
        -webkit-animation-delay: ${(config as any).animation.delay.val}${(config as any).animation.delay.unit};

      }
    `;

    return css;
};