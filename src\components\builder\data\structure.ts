import { Auto_BlockStructure, genRandomBlockId } from '@giaminhautoketing/auto-builder';
const SECTION_ID = (() => `SECTION-${genRandomBlockId()}`)();
export const blockStructure: Auto_BlockStructure = {
    blocks: {
        page: {
            type: 'page',
            label: 'Page',
            cname: 'page',
            configs: {
                displayOnDesktop: true,
                displayOnMobile: true,
            },
            bpConfigs: {
                desktop: {
                    background: '#e3e3e3',
                    color: 'blue',
                    width: { val: '100', unit: '%' },
                    maxWidthPage: { val: '1140', unit: 'px' },
                    height: { val: '100', unit: '%' },
                    zIndex: 2,
                    position: 'relative',
                    pt: { val: '0', unit: 'px' },
                    pb: { val: '0', unit: 'px' },
                    pl: { val: '0', unit: 'px' },
                    pr: { val: '0', unit: 'px' },
                },
                mobile: {
                    background: '#e3e3e3',
                    color: 'blue',
                    width: { val: '100', unit: '%' },
                    maxWidthPage: { val: '480', unit: 'px' },
                    height: { val: '100', unit: '%' },
                    zIndex: 2,
                    position: 'relative',
                    pt: { val: '0', unit: 'px' },
                    pb: { val: '0', unit: 'px' },
                    pl: { val: '0', unit: 'px' },
                    pr: { val: '0', unit: 'px' },
                },
            },
        },
        [SECTION_ID]: {
            type: 'section',
            label: 'Section',
            cname: 'section',
            configs: {
                displayOnDesktop: true,
                displayOnMobile: true,
            },
            bpConfigs: {
                desktop: {
                    background: '#fff',
                    maxWidth: { val: '1140', unit: 'px' },
                    innerDisplay: 'grid',
                    gridTemplateRows: [{ val: '400', unit: 'px' }],
                    minHeight: { val: '400', unit: 'px' },
                    gridTemplateColumns: [{ val: 'minmax(0,1fr)', unit: '' }],
                    position: 'relative',
                    pt: { val: '0', unit: 'px' },
                    pb: { val: '0', unit: 'px' },
                    pl: { val: '0', unit: 'px' },
                    pr: { val: '0', unit: 'px' },
                },
                mobile: {
                    background: '#ffffff',
                    maxWidth: { val: '480', unit: 'px' },
                    innerDisplay: 'grid',
                    gridTemplateRows: [{ val: '400', unit: 'px' }],
                    minHeight: { val: '400', unit: 'px' },
                    gridTemplateColumns: [{ val: 'minmax(0,1fr)', unit: '' }],
                    position: 'relative',
                    pt: { val: '0', unit: 'px' },
                    pb: { val: '0', unit: 'px' },
                    pl: { val: '0', unit: 'px' },
                    pr: { val: '0', unit: 'px' },
                },
            },
        },
    },
    hierarchy: {
        page: [SECTION_ID],
        [SECTION_ID]: [],
    },
};
