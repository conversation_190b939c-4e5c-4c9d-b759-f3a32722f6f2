import { FC } from 'react';
import { TextProps } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseChooseIcon } from '@/components/builder/base/BaseChooseIcon';
import { OptionProps } from '@/components/builder/base/BaseChooseIcon';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseChooseIconProps = Parameters<typeof BaseChooseIcon>[0];

interface ChooseIconProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
    options: OptionProps[];
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    chooseIconProps?: Omit<BaseChooseIconProps, 'options' | 'valueData' | 'onChange'>;
}

export const ChooseIcon: FC<ChooseIconProps> = ({
    path,
    blockId,
    options,
    isUpdateConfigs,
    textProps,
    label,
    chooseIconProps,
    ...otherProps
}) => {
    const value = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChange = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, value);
        } else {
            updateBlockProperty(blockId, currentDevice, path, value);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseChooseIcon options={options} valueData={value} onChange={handleChange} {...chooseIconProps} />
        </BaseItemLayout>
    );
};
