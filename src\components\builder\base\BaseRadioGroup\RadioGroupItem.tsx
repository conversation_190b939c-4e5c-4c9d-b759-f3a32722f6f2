import { FC } from 'react';
import { RadioButton, Text } from '@shopify/polaris';
import { RadioDataProps } from './types';

interface RadioGroupItemProps {
    data: RadioDataProps;
    isActive: boolean;
    onChangeRadio(value: string): void;
}

export const RadioGroupItem: FC<RadioGroupItemProps> = ({ isActive, data, onChangeRadio }) => {
    const onActiveRadio = () => {
        onChangeRadio(data.value);
    };

    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                transition: 'all 0.25s',
                paddingBlock: '3px',
            }}
            onClick={onActiveRadio}
        >
            <RadioButton checked={isActive} label="" onChange={onActiveRadio} />
            <Text as="span" variant="bodyMd">
                {data.title}
            </Text>
        </div>
    );
};
