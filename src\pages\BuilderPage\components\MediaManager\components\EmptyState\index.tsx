import { DropZone, Text } from '@shopify/polaris';
import { ReactComponent as EmptyStateIcon } from '@/assets/svgs/Illustration.svg';

interface EmptyStateProps {
    onDrop: (files: File[]) => void;
    accept: string;
}

export const EmptyState = ({ onDrop, accept }: EmptyStateProps) => {
    return (
        <DropZone onDrop={(_, acceptedFiles) => onDrop(acceptedFiles)} accept={accept}>
            <div
                css={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    rowGap: '30px',
                    width: '100%',
                    height: '100%',
                }}
            >
                <EmptyStateIcon />
                <div
                    css={{
                        display: 'flex',
                        alignItems: 'center',
                        flexDirection: 'column',
                        rowGap: '10px',
                    }}
                >
                    <Text variant="bodyMd" as="p" fontWeight="medium">
                        Add your files
                    </Text>
                    <Text variant="bodySm" as="p">
                        Drag and drop files or upload from your computer
                    </Text>
                </div>
            </div>
        </DropZone>
    );
};
