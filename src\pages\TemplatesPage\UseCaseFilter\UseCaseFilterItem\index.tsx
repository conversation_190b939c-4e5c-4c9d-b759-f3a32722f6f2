import { FC } from 'react';
import { Button } from '@shopify/polaris';

interface UseCaseFilterItemProps {
    label: string;
    isActive: boolean;
    onClick: () => void;
}

export const UseCaseFilterItem: FC<UseCaseFilterItemProps> = ({
    label,
    isActive,
    onClick,
}) => {
    return (
        <div
            css={
                !isActive && {
                    '& .Polaris-Button:hover': {
                        boxShadow:
                            '0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0rem #FFF inset',
                        backgroundColor: '#efefef',
                    },
                }
            }
        >
            <Button
                size="large"
                variant={isActive ? 'primary' : 'secondary'}
                onClick={onClick}
                pressed={isActive}
            >
                {label}
            </Button>
        </div>
    );
};
