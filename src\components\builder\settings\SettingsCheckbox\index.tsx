import { FC } from 'react';
import { BaseCheckbox, BaseItemLayout } from '@/components/builder/base';
import { TextProps } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseCheckboxProps = Parameters<typeof BaseCheckbox>[0];

interface SettingsCheckboxProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName' | 'hideTitle'> {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Partial<TextProps>;
    checkboxProps?: Omit<BaseCheckboxProps, 'value' | 'onChange'>;
}

export const SettingsCheckbox: FC<SettingsCheckboxProps> = ({
    label,
    path,
    blockId,
    isUpdateConfigs,
    textProps,
    checkboxProps,
    ...otherProps
}) => {
    const value = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChange = (newChecked: boolean) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, newChecked);
        } else {
            updateBlockProperty(blockId, currentDevice, path, newChecked);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd' };
    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseCheckbox label={label as string} checked={value} onChange={handleChange} {...checkboxProps} />
        </BaseItemLayout>
    );
};
