import { FC, ReactNode, useState, useEffect } from 'react';
import { Box, Button, ButtonProps, InlineStack, Text, Tooltip, Icon, Divider } from '@shopify/polaris';
import {
    useFloating,
    useInteractions,
    useClick,
    useDismiss,
    useRole,
    useId,
    FloatingOverlay,
    FloatingFocusManager,
    FloatingPortal,
} from '@floating-ui/react';
import { QuestionCircleIcon, XIcon } from '@shopify/polaris-icons';
import { useUncontrolled } from '@/hooks';
import './styles.scss';

interface BaseModalProps {
    textTooltip?: string;
    isOpen?: boolean;
    onOpenChange?(isOpen: boolean): void;
    okTitle?: string;
    onOk?(): void;
    hiddenOkButton?: boolean;
    hiddenCancelButton?: boolean;
    hiddenFooter?: boolean;
    onCancel?(): void;
    cancelTitle?: string;
    okElementProps?: ButtonProps;
    cancelElementProps?: ButtonProps;
    elementTriggerProps?: { children: ReactNode } & Partial<typeof InlineStack>;
    elementContentProps?: Record<string, unknown>;
    elementModalContentProps: {
        elementProps?: Record<string, unknown>;
        title?: string;
        des?: string;
        children: ReactNode;
    };
    elementOverlay?: { bg?: string };
    isDisabled?: boolean;
}

export const BaseModal: FC<BaseModalProps> = ({
    textTooltip,
    isOpen,
    onOpenChange,
    okTitle = 'Comfirm',
    onOk,
    hiddenOkButton = false,
    hiddenCancelButton = false,
    hiddenFooter = false,
    onCancel,
    cancelTitle = 'Cancel',
    okElementProps,
    cancelElementProps,
    elementContentProps,
    elementTriggerProps,
    elementModalContentProps,
    elementOverlay,
    isDisabled,
}) => {
    const [tooltipReady, setTooltipReady] = useState(false);
    const [open, setOpen] = useUncontrolled({
        value: isOpen,
        onChange: onOpenChange,
        defaultValue: false,
        finalValue: false,
    });

    useEffect(() => {
        if (open) {
            const timer = setTimeout(() => {
                setTooltipReady(true);
            }, 300);
            return () => clearTimeout(timer);
        } else {
            setTooltipReady(false);
        }
    }, [open]);

    const { refs, context } = useFloating({
        open,
        onOpenChange: setOpen,
    });

    const click = useClick(context);
    const dismiss = useDismiss(context);
    const role = useRole(context);

    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss, role]);

    const headingId = useId();
    const descriptionId = useId();

    const onOkClick = () => {
        onOk?.();
        setOpen(false);
    };

    const onCancelClick = () => {
        onCancel?.();
        setOpen(false);
    };

    return (
        <Box>
            <div ref={refs.setReference} {...getReferenceProps()}>
                <InlineStack {...elementTriggerProps}>{elementTriggerProps?.children}</InlineStack>
            </div>
            {open && (
                <FloatingPortal>
                    <FloatingOverlay
                        lockScroll
                        style={{
                            background: elementOverlay?.bg || '#00000099',
                            zIndex: 99,
                        }}
                    >
                        <FloatingFocusManager context={context}>
                            <div
                                ref={refs.setFloating}
                                {...getFloatingProps()}
                                aria-labelledby={headingId}
                                aria-describedby={descriptionId}
                                className="shared-floating shared-floating-base-modal"
                            >
                                <div style={{ ...elementContentProps }} className="shared-floating-base-modal__content">
                                    <div className="shared-floating-base-modal__content__close">
                                        {textTooltip &&
                                            (tooltipReady ? (
                                                <Tooltip content={textTooltip}>
                                                    <div style={{ color: 'var(--p-text-subdued)' }}>
                                                        <Icon tone="base" source={QuestionCircleIcon} />
                                                    </div>
                                                </Tooltip>
                                            ) : (
                                                <div style={{ color: 'var(--p-text-subdued)' }}>
                                                    <Icon tone="base" source={QuestionCircleIcon} />
                                                </div>
                                            ))}
                                        <Button
                                            variant="tertiary"
                                            tone="success"
                                            onClick={onCancelClick}
                                            icon={XIcon}
                                        />
                                    </div>

                                    <Box>
                                        <Box padding="400">
                                            <Text variant="headingMd" fontWeight="medium" as="span">
                                                {elementModalContentProps.title}
                                            </Text>
                                            {elementModalContentProps.des && (
                                                <Text id={descriptionId} variant="bodyMd" tone="subdued" as="p">
                                                    {elementModalContentProps.des}
                                                </Text>
                                            )}
                                        </Box>
                                        <Divider />
                                    </Box>
                                    <div
                                        className="shared-floating-base-modal__content__body"
                                        {...elementModalContentProps.elementProps}
                                    >
                                        {elementModalContentProps.children}
                                    </div>
                                    {!hiddenFooter && (
                                        <div className="shared-floating-base-modal__content__footer">
                                            <InlineStack align="end" gap="200">
                                                {!hiddenCancelButton && (
                                                    <Button onClick={onCancelClick} {...cancelElementProps}>
                                                        {cancelTitle}
                                                    </Button>
                                                )}
                                                {!hiddenOkButton && (
                                                    <Button
                                                        variant="primary"
                                                        disabled={isDisabled}
                                                        onClick={onOkClick}
                                                        {...okElementProps}
                                                    >
                                                        {okTitle}
                                                    </Button>
                                                )}
                                            </InlineStack>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </FloatingFocusManager>
                    </FloatingOverlay>
                </FloatingPortal>
            )}
        </Box>
    );
};
