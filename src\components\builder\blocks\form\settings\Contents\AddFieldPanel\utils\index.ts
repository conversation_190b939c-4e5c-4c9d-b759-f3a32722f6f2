import { useBlockStore, Auto_BlockDeviceType } from '@giaminhautoketing/auto-builder';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';
import { FormField } from '@/components/builder/blocks/form/types';

interface UseSettingsProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
    device?: Auto_BlockDeviceType;
}

const MOBILE_DEVICE = 'mobile' as const;

export function useSettingsFormField<T>({ blockId, path, isUpdateConfigs, device }: UseSettingsProps) {
    const value = isUpdateConfigs ? getBlockProperty(`configs.${path}`, blockId) : getBlockBPProperty(path, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const updateValue = (value: T) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, value as string);
        } else {
            updateBlockProperty(blockId, device as Auto_BlockDeviceType, path, value as string);
        }
    };

    return { value, updateValue };
}

export const useFieldSize = () => {
    const getDefaultFieldSize = (field: FormField, device: Auto_BlockDeviceType) => {
        const isMobile = device === MOBILE_DEVICE;
        const baseSize = {
            fieldWidth: { val: isMobile ? '100' : '50', unit: '%' },
        };

        const fieldSpecificSize = {
            radio: {
                radioSize: { val: isMobile ? '14' : '16', unit: 'px' },
            },
            checkbox: {
                checkboxSize: { val: isMobile ? '14' : '16', unit: 'px' },
            },
            default: {
                fieldHeight: { val: isMobile ? '32' : '36', unit: 'px' },
            },
        };

        const size =
            field.type === 'radio'
                ? fieldSpecificSize.radio
                : field.type === 'checkbox'
                ? fieldSpecificSize.checkbox
                : fieldSpecificSize.default;

        return {
            [field.key]: {
                ...baseSize,
                ...size,
            },
        };
    };

    return { getDefaultFieldSize };
};
