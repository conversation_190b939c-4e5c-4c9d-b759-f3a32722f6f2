/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { FC, useCallback, useEffect, useState } from 'react';
import { useKeenSlider } from 'keen-slider/react';
import { Box, Button, ButtonGroup, Card, Icon, Pagination, Text, Tooltip } from '@shopify/polaris';
import { StarFilledIcon, XSmallIcon } from '@shopify/polaris-icons';
import LogoAutoketing from '@/assets/images/logo-autoketing.png';
import SalesPopup from '@/assets/images/sales-popup-autoketing.png';
import ProductReviews from '@/assets/images/autoketing-product-reviews.png';
import SeoImageOptimizer from '@/assets/images/autoketing-seo-image-optimizer.png';
import 'keen-slider/keen-slider.min.css';
import { DataAppProps } from './types';
import { Skeleton } from './Skeleton';
import * as sc from './styled';

export const Integrated: FC = () => {
    const [open, setOpen] = useState(false);
    const [apps, setApps] = useState<DataAppProps[]>([]);
    const [currentSlide, setCurrentSlide] = useState(0);
    const [loading, setLoading] = useState(false);
    const [sliderRef, sliderInstance] = useKeenSlider<HTMLDivElement>({
        loop: true,
        slideChanged(slider) {
            setCurrentSlide(slider.track.details.rel);
        },
    });

    const onHide = () => {
        setOpen(true);
    };

    useEffect(() => {
        setLoading(true);
        fetch('https://api-v2-default.autoketing.org/ak/get-apps-ratings-cross-sell')
            .then((res) => res.json())
            .then((data) => {
                const filteredApps = data.filter((app: DataAppProps) => ['rev', 'spu', 'seo'].includes(app.app_code));
                setApps(filteredApps);
                setTimeout(() => setLoading(false), 500);
            });
    }, []);

    useEffect(() => {
        const autoplayInterval = setInterval(() => sliderInstance.current?.next(), 5000);
        return () => {
            clearInterval(autoplayInterval);
        };
    }, [sliderInstance]);

    const handlePrevious = useCallback(() => sliderInstance.current?.prev(), []);
    const handleNext = useCallback(() => sliderInstance.current?.next(), []);
    const handleLinkApp = useCallback((link: string) => window.open(link, '_blank'), []);
    const handleAllApps = useCallback(() => window.open('https://apps.shopify.com/partners/autoketing', '_blank'), []);

    return (
        <sc.InteragratedContainer isHidden={open}>
            {loading ? (
                <Skeleton />
            ) : (
                <Card>
                    <sc.InteragratedLeft>
                        {apps.length > 0 && (
                            <>
                                <sc.InteragratedLeftHeader>
                                    <Text as="h2" variant="headingSm">
                                        Intergrated with 100+ apps
                                    </Text>
                                    <Text tone="subdued" as="span">
                                        Boost your sales, optimize your traffic & increse revenue
                                    </Text>
                                </sc.InteragratedLeftHeader>
                                <sc.InteragratedLeftContent>
                                    <img alt="logoAutoketing" src={LogoAutoketing} />
                                    <Box>
                                        <Text as="h2" variant="headingSm">
                                            {apps[currentSlide % apps.length].app_title}
                                        </Text>
                                        <sc.Rating>
                                            <Text tone="subdued" as="span">
                                                <Text tone="subdued" as="span">
                                                    {apps[currentSlide % apps.length].app_rating_number}
                                                </Text>
                                                <Icon source={StarFilledIcon} />
                                                {`(${apps[currentSlide % apps.length].app_total_review_count}) • ${
                                                    apps[currentSlide % apps.length].app_description
                                                }`}
                                            </Text>
                                        </sc.Rating>
                                    </Box>
                                </sc.InteragratedLeftContent>
                                <ButtonGroup>
                                    <Button onClick={() => handleLinkApp(apps[currentSlide % apps.length].ads_app_url)}>
                                        Install now
                                    </Button>
                                    <Button onClick={handleAllApps}>See all apps</Button>
                                </ButtonGroup>
                            </>
                        )}
                    </sc.InteragratedLeft>
                    <sc.InteragratedRight>
                        <sc.InteragratedRightSlider>
                            <Tooltip content="Close">
                                <Button variant="tertiary" onClick={onHide} icon={XSmallIcon} />
                            </Tooltip>
                            <div ref={sliderRef} className="keen-slider">
                                <div className="keen-slider__slide ">
                                    <img alt="image" src={SalesPopup} />
                                </div>
                                <div className="keen-slider__slide ">
                                    <img alt="image" src={ProductReviews} />
                                </div>
                                <div className="keen-slider__slide ">
                                    <img alt="image" src={SeoImageOptimizer} />
                                </div>
                            </div>
                        </sc.InteragratedRightSlider>
                        <Pagination hasPrevious onPrevious={handlePrevious} hasNext onNext={handleNext} />
                    </sc.InteragratedRight>
                </Card>
            )}
        </sc.InteragratedContainer>
    );
};
