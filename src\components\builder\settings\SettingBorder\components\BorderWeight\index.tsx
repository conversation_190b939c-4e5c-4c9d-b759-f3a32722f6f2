import { FC, useEffect, useState } from 'react';
import { BlockStack, Box, InlineStack } from '@shopify/polaris';
import { ReactComponent as BorderTopSide } from '@/assets/svgs/Border/border-top.svg';
import { ReactComponent as BorderLeftSide } from '@/assets/svgs/Border/border-left.svg';
import { ReactComponent as BorderRightSide } from '@/assets/svgs/Border/border-right.svg';
import { ReactComponent as BorderBottomSide } from '@/assets/svgs/Border/border-bottom.svg';
import { ReactComponent as Lock } from '@/assets/svgs/Border/lock.svg';
import { ReactComponent as Unlock } from '@/assets/svgs/Border/unlock.svg';
import { BorderInput } from '@/components/builder/settings/SettingBorder/components/shared/BorderInput';
import './style.scss';
import { BorderType } from '../shared/BorderType';
import { BorderColor } from '../shared/BorderColor';
import { getBlockBPProperty } from '@/utils/shared';
import { getBlockProperty } from '@/utils/shared';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
interface BorderWeightProps {
    isUpdateConfigs?: boolean;
    path?: string;
    blockId: string;
}

export const BorderWeight: FC<BorderWeightProps> = ({ isUpdateConfigs, path, blockId }) => {
    const [isLock, setIsLock] = useState(true);
    const [activeSide, setActiveSide] = useState<'top' | 'left' | 'right' | 'bottom' | null>(null);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleButtonClick = (side: 'top' | 'left' | 'right' | 'bottom') => {
        setActiveSide((currentSide) => {
            if (currentSide && currentSide !== side) {
                return side;
            }
            return currentSide === side ? null : side;
        });
    };

    const handleInputFocus = (side: 'top' | 'left' | 'right' | 'bottom') => {
        setActiveSide(side);
    };

    const handleInputBlur = () => {
        setActiveSide(null);
    };

    const getBorderType = () => {
        return isUpdateConfigs
            ? getBlockProperty(`configs.${path}.type`, blockId)
            : getBlockBPProperty(`${path}.type`, blockId);
    };

    const getBorderWeight = (side: 'top' | 'left' | 'right' | 'bottom') => {
        return isUpdateConfigs
            ? getBlockProperty(`configs.${path}.${side}.val`, blockId)
            : getBlockBPProperty(`${path}.${side}.val`, blockId);
    };

    const getBorderColor = () => {
        return isUpdateConfigs
            ? getBlockProperty(`configs.${path}.color`, blockId)
            : getBlockBPProperty(`${path}.color`, blockId);
    };

    useEffect(() => {
        getBorderColor();
        getBorderType();
        getBorderWeight('top');
        getBorderWeight('left');
        getBorderWeight('right');
        getBorderWeight('bottom');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentDevice]);

    return (
        <Box
            paddingBlock="400"
            paddingInline="100"
            borderRadius="200"
            borderColor="border"
            borderStyle="solid"
            borderWidth="025"
        >
            <BlockStack gap="300">
                <div className="border-setting">
                    <InlineStack align="center" blockAlign="center">
                        <BorderInput
                            id="top"
                            isActive={activeSide === 'top'}
                            path={`${path}.top`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isLock}
                            onFocus={() => handleInputFocus('top')}
                            onBlur={() => handleInputBlur()}
                            type="border"
                        />
                    </InlineStack>

                    <InlineStack align="center" blockAlign="center" gap="200" wrap={false}>
                        <div className="border-setting__input--left">
                            <BorderInput
                                id="left"
                                isActive={activeSide === 'left'}
                                path={`${path}.left`}
                                blockId={blockId}
                                isUpdateConfigs={isUpdateConfigs}
                                isLock={isLock}
                                onFocus={() => handleInputFocus('left')}
                                onBlur={() => handleInputBlur()}
                                type="border"
                            />
                        </div>

                        <div className="border-setting__diagram">
                            <div className="border-setting__diagram-row" style={{ marginBottom: '-8px' }}>
                                <button
                                    key="top"
                                    className="border-setting__button"
                                    onClick={() => handleButtonClick('top')}
                                >
                                    <BorderTopSide
                                        style={activeSide === 'top' ? { color: '#005BD3' } : { color: '#8A8A8A' }}
                                    />
                                    <div
                                        className="border-setting__line border-setting__line--top"
                                        style={{
                                            borderTopColor: getBorderColor(),
                                            borderTopStyle: getBorderType() === 'default' ? 'solid' : getBorderType(),
                                            borderTopWidth:
                                                getBorderWeight('top') > 1 ? '1px' : `${getBorderWeight('top')}px`,
                                        }}
                                    ></div>
                                </button>
                            </div>
                            <div className="border-setting__diagram-row border-setting__diagram-row--space-between">
                                <div className="border-setting__diagram-row">
                                    <button
                                        key="left"
                                        className="border-setting__button"
                                        onClick={() => handleButtonClick('left')}
                                    >
                                        <BorderLeftSide
                                            style={activeSide === 'left' ? { color: '#005BD3' } : { color: '#8A8A8A' }}
                                        />
                                        <div
                                            className="border-setting__line border-setting__line--left"
                                            style={{
                                                borderLeftColor: getBorderColor(),
                                                borderLeftStyle:
                                                    getBorderType() === 'default' ? 'solid' : getBorderType(),
                                                borderLeftWidth:
                                                    getBorderWeight('left') > 1
                                                        ? '1px'
                                                        : `${getBorderWeight('left')}px`,
                                            }}
                                        ></div>
                                    </button>
                                </div>
                                <button
                                    className="border-setting__button border-setting__button--lock"
                                    onClick={() => setIsLock(!isLock)}
                                >
                                    {isLock ? <Lock /> : <Unlock />}
                                </button>

                                <div className="border-setting__diagram-row">
                                    <button
                                        key="right"
                                        className="border-setting__button"
                                        onClick={() => handleButtonClick('right')}
                                    >
                                        <BorderRightSide
                                            style={activeSide === 'right' ? { color: '#005BD3' } : { color: '#8A8A8A' }}
                                        />
                                        <div
                                            className="border-setting__line border-setting__line--right"
                                            style={{
                                                borderRightColor: getBorderColor(),
                                                borderRightStyle:
                                                    getBorderType() === 'default' ? 'solid' : getBorderType(),
                                                borderRightWidth:
                                                    getBorderWeight('right') > 1
                                                        ? '1px'
                                                        : `${getBorderWeight('right')}px`,
                                            }}
                                        ></div>
                                    </button>
                                </div>
                            </div>
                            <div className="border-setting__diagram-row" style={{ marginTop: '-8px' }}>
                                <button
                                    key="bottom"
                                    className="border-setting__button"
                                    onClick={() => handleButtonClick('bottom')}
                                >
                                    <BorderBottomSide
                                        style={activeSide === 'bottom' ? { color: '#005BD3' } : { color: '#8A8A8A' }}
                                    />
                                    <div
                                        className="border-setting__line border-setting__line--bottom"
                                        style={{
                                            borderBottomColor: getBorderColor(),
                                            borderBottomStyle:
                                                getBorderType() === 'default' ? 'solid' : getBorderType(),
                                            borderBottomWidth:
                                                getBorderWeight('bottom') > 1
                                                    ? '1px'
                                                    : `${getBorderWeight('bottom')}px`,
                                        }}
                                    ></div>
                                </button>
                            </div>
                        </div>

                        <div className="border-setting__input--right">
                            <BorderInput
                                id="right"
                                isActive={activeSide === 'right'}
                                path={`${path}.right`}
                                blockId={blockId}
                                isUpdateConfigs={isUpdateConfigs}
                                isLock={isLock}
                                onFocus={() => handleInputFocus('right')}
                                onBlur={() => handleInputBlur()}
                                type="border"
                            />
                        </div>
                    </InlineStack>

                    <InlineStack align="center" blockAlign="center">
                        <BorderInput
                            id="bottom"
                            isActive={activeSide === 'bottom'}
                            path={`${path}.bottom`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isLock}
                            onFocus={() => handleInputFocus('bottom')}
                            onBlur={() => handleInputBlur()}
                            type="border"
                        />
                    </InlineStack>
                </div>

                <div className="border-setting__border-type-color">
                    <BorderType blockId={blockId} isUpdateConfigs={isUpdateConfigs} path={`${path}.type`} />
                    <BorderColor path={`${path}.color`} blockId={blockId} isUpdateConfigs={isUpdateConfigs} />
                </div>
            </BlockStack>
        </Box>
    );
};
