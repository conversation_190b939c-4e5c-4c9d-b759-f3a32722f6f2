import { AppsIcon, DomainNewIcon, GlobeIcon, LayoutBuyButtonIcon, SettingsIcon } from '@shopify/polaris-icons';
import LayerIcon from '@/assets/svgs/layer.svg?raw';
import { SideBarConfigProps } from '../types/sidebar';
import { Elements } from '@/pages/BuilderPage/components/DrawerContent/Elements';
import { GlobalStyle } from '@/pages/BuilderPage/components/DrawerContent/GlobalStyle';
import { Layer } from '@/pages/BuilderPage/components/DrawerContent/Layer';
import { PageSetting } from '@/pages/BuilderPage/components/DrawerContent/PageSetting';
import { Sections } from '@/pages/BuilderPage/components/DrawerContent/Sections';
import { TemplateLibrary } from '@/pages/BuilderPage/components/DrawerContent/TemplateLibrary';

export const ButtonSideBars: SideBarConfigProps = {
    items: [
        {
            id: 'elements',
            icon: AppsIcon,
            title: 'Elements',
            drawerContent: Elements,
        },
        {
            id: 'sections',
            icon: LayoutBuyButtonIcon,
            title: 'Sections',
            drawerContent: Sections,
        },
        {
            id: 'layer',
            icon: LayerIcon,
            title: 'Layer',
            drawerContent: Layer,
        },
        {
            id: 'template-library',
            icon: DomainNewIcon,
            title: 'Template library',
            drawerContent: TemplateLibrary,
        },
        {
            id: 'global-style',
            icon: GlobeIcon,
            title: 'Global style',
            drawerContent: GlobalStyle,
        },
        {
            id: 'page-setting',
            icon: SettingsIcon,
            title: 'Page setting',
            drawerContent: PageSetting,
        },
    ],
};
