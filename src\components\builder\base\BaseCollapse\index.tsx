import { FC } from 'react';
import clsx from 'clsx';
import { Collapsible, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { useUncontrolled } from '@/hooks';
import { BaseCollapseLabel } from '@/components/builder/base/BaseCollapseLabel';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';

export interface BaseCollapseProps {
    children: React.ReactNode;
    label: string;
    defaultOpen?: boolean;
    open?: boolean;
    onChange?: (open: boolean) => void;
    containerClassName?: string;
    labelContent?: React.ReactNode | ((open: boolean, setOpen?: (open: boolean) => void) => React.ReactNode);
    isUpdateToggle?: boolean;
    isUpdateConfigs?: boolean;
    blockId?: string;
    path?: string;
}

export const BaseCollapse: FC<BaseCollapseProps> = ({
    children,
    label,
    defaultOpen = false,
    open,
    onChange,
    containerClassName,
    labelContent = (open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />,
    isUpdateToggle,
    isUpdateConfigs,
    blockId,
    path,
}) => {
    const [openState, setOpenState] = useUncontrolled({
        value: open,
        onChange,
        defaultValue: defaultOpen,
    });

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleOpen = () => {
        const newValue = !openState;
        setOpenState(newValue);
        onChange?.(newValue);
        if (isUpdateToggle && blockId && path) {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, path, newValue);
            } else {
                updateBlockProperty(blockId, currentDevice, path, newValue);
            }
        }
    };

    return (
        <div className={clsx('base-collapse', containerClassName)}>
            <BaseCollapseLabel label={label} onClick={open === undefined ? handleOpen : undefined}>
                {typeof labelContent === 'function' ? labelContent(openState, handleOpen) : labelContent}
            </BaseCollapseLabel>
            <Collapsible
                open={openState}
                id={`collapse-${label}`}
                transition={{ duration: '200ms', timingFunction: 'ease ' }}
                expandOnPrint
            >
                {children}
            </Collapsible>
        </div>
    );
};
