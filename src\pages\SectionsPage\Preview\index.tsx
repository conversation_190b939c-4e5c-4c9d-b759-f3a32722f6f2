import { FC, memo, useCallback, useState, useMemo } from 'react';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { AppProvider, Box, Button, InlineGrid, Text } from '@shopify/polaris';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@/hooks';
import { pathnames } from '@/configs';
import { apiAddress } from '@/configs/apiAddress';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { ReactComponent as DesktopIcon } from '@/assets/svgs/desktop.svg';
import { ReactComponent as MobileIcon } from '@/assets/svgs/mobile.svg';
import { screenConfigs } from './configs';
import './styles.scss';
type Screen = 'desktop' | 'mobile';

export const Preview: FC = memo(() => {
    const navigate = useNavigate();
    const [screen, setScreen] = useState<Screen>('desktop');
    const templatePreview = useAppStore((state) => state.templatePreview);

    const handleScreen = useCallback((value: Screen) => setScreen(value), []);

    const createMutation = useMutation({
        url: apiAddress.shopSections.index,
        method: 'POST',
        onSuccess: () => {
            shopify.toast.show('Section created successfully');
            shopify.modal.hide('modal-preview-section-template');
            navigate(pathnames.sections);
        },
        onError: () => {
            shopify.toast.show('Failed to create section', { isError: true });
        },
    });

    const screenConfig = useMemo(() => screenConfigs[screen], [screen]);

    const containerStyle = useMemo(
        () => ({
            overflow: screen === 'desktop' ? 'hidden' : 'auto',
            paddingTop: screen === 'desktop' ? '54px' : '34px',
            height: screenConfig.containerHeight,
            backgroundImage: screenConfig.backgroundImage,
        }),
        [screen, screenConfig],
    );

    const iframeStyle = useMemo(
        () => ({
            width: screenConfig.width,
            height: screenConfig.height,
            marginTop: screenConfig.marginTop,
            borderRadius: screen === 'desktop' ? 0 : '12px',
        }),
        [screen, screenConfig],
    );

    return (
        <Modal id="modal-preview-section-template" variant="max">
            <TitleBar title="Autoketing Builder">
                <button
                    variant="primary"
                    loading={createMutation.isLoading ? '' : undefined}
                    onClick={() =>
                        createMutation.mutate({
                            title: `Example section ${Math.ceil(Math.random() * 1000000)}`,
                            thumbnail: '',
                            htmlData: '<h1>{{ section.settings.title }}</h1>',
                            jsonData: '<h1>title</h1>',
                            status: 1,
                        })
                    }
                >
                    Use section
                </button>
            </TitleBar>
            <AppProvider i18n={{}}>
                <div className="preview-container">
                    <InlineGrid columns={3}>
                        <Text as="span">{templatePreview.name}</Text>
                        <InlineGrid gap="100" columns={3}>
                            <Button
                                variant={screen === 'desktop' ? undefined : 'tertiary'}
                                onClick={() => handleScreen('desktop')}
                                icon={<DesktopIcon />}
                            />
                            <Button
                                variant={screen === 'mobile' ? undefined : 'tertiary'}
                                onClick={() => handleScreen('mobile')}
                                icon={<MobileIcon />}
                            />
                        </InlineGrid>
                        <Box></Box>
                    </InlineGrid>
                    <div className="preview-content" style={containerStyle}>
                        <iframe style={iframeStyle} src={templatePreview.url} />
                    </div>
                </div>
            </AppProvider>
        </Modal>
    );
});
