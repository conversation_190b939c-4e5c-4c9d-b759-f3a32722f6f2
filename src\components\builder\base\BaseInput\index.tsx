import { FC } from 'react';
import { TextField, TextFieldProps } from '@shopify/polaris';
import clsx from 'clsx';
import './styles.scss';

type BaseInputProps = Partial<TextFieldProps> & {
    isShadow?: boolean;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onKeyUp?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
};

export const BaseInput: FC<BaseInputProps> = ({
    value,
    onChange,
    max,
    min,
    type = 'number',
    placeholder = '0',
    suffix,
    isShadow,
    label,
    labelHidden = true,
    autoComplete = 'off',
    onKeyDown,
    onKeyUp,
    ...props
}) => {
    return (
        <div className={clsx('base-input', isShadow && 'shadow')} onKeyDown={onKeyDown} onKeyUp={onKeyUp}>
            <TextField
                placeholder={placeholder}
                type={type}
                value={value}
                onChange={onChange}
                autoComplete={autoComplete}
                suffix={suffix}
                labelHidden={labelHidden}
                label={label}
                max={max}
                onFocus={(e) => {
                    if (e?.target instanceof HTMLInputElement) {
                        e.target.select();
                    }
                }}
                min={min}
                {...props}
            />
        </div>
    );
};
