import { FC } from 'react';
import { BlockStack, Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingBorder } from '@/components/builder/settings/SettingBorder';
import { SettingsShadow } from '@/components/builder/settings';
interface BorderProps {
    id: string;
}

export const Border: FC<BorderProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Border"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <SettingBorder isUpdateConfigs={false} path="borderBreadcrumb" blockId={id} label="Border" />
                        <SettingsShadow
                            isUpdateConfigs={false}
                            type="box-shadow"
                            path="boxShadow"
                            blockId={id}
                            label="Shadow"
                        />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
