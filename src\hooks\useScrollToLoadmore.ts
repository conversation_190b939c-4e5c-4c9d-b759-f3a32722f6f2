/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from 'react';

interface UseScrollToLoadmoreProp {
    loadmore: () => void;
    threshold?: number;
    hasMore?: boolean;
    loading?: boolean;
    resetDependencies?: unknown[];
}

export const useScrollToLoadmore = ({
    loadmore,
    threshold = 50,
    hasMore = true,
    loading = false,
    resetDependencies = [],
}: UseScrollToLoadmoreProp) => {
    const containerRef = useRef<HTMLDivElement>(null);

    const handleScroll = () => {
        if (!containerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;

        if (scrollHeight - scrollTop - clientHeight < threshold && !loading && hasMore) {
            loadmore();
        }
    };

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.scrollTop = 0;
        }
    }, [...resetDependencies]);

    useEffect(() => {
        const currentContainer = containerRef.current;
        if (currentContainer) {
            currentContainer.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (currentContainer) {
                currentContainer.removeEventListener('scroll', handleScroll);
            }
        };
    }, [loading, hasMore]);

    return {
        containerRef,
    };
};
