/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useMemo } from 'react';
import { emotionNoPrefixCache } from '@/utils/emotionNoPrefixCache';
import { CacheProvider } from '@emotion/react';
import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useButtonAttributes } from '@/components/builder/blocks/button/hooks/useButtonAttributes';
import { ButtonConfigs, ButtonBreakpointConfig } from '@/components/builder/blocks/button/types';
import { ButtonIcon } from '@/components/builder/blocks/button/components/ButtonIcon';
import { ButtonText } from '@/components/builder/blocks/button/components/ButtonText';

const ButtonContent: FC<{
    configs: ButtonConfigs;
    buttonAttributes: Record<string, any>;
    autoId: string;
}> = ({ configs, buttonAttributes, autoId }) => {
    const { type, text, icon, iconSize, iconColor } = configs.content;

    switch (type) {
        case 'text-only':
            return (
                <div {...buttonAttributes}>
                    <ButtonText text={text} autoId={autoId} />
                </div>
            );
        case 'icon-only':
            return icon ? (
                <div {...buttonAttributes}>
                    <ButtonIcon source={icon} size={iconSize} color={iconColor} autoId={autoId} />
                </div>
            ) : (
                <div {...buttonAttributes} />
            );
        case 'text-and-icon':
            if (!icon)
                return (
                    <div {...buttonAttributes}>
                        <ButtonText text={text} autoId={autoId} />
                    </div>
                );

            return (
                <div {...buttonAttributes}>
                    <ButtonIcon source={icon} size={iconSize} color={iconColor} autoId={autoId} />
                    <ButtonText text={text} autoId={autoId} />
                </div>
            );
        default:
            return <div {...buttonAttributes} />;
    }
};

export const ButtonViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const buttonAttributes = useButtonAttributes(
        configs as unknown as ButtonConfigs,
        autoId,
        currentDevice,
        bpConfigs as unknown as ButtonBreakpointConfig,
    );

    const content = useMemo(() => {
        if (!configs?.content) return null;
        return (
            <ButtonContent
                configs={configs as unknown as ButtonConfigs}
                buttonAttributes={buttonAttributes}
                autoId={autoId}
            />
        );
    }, [configs, buttonAttributes, autoId]);

    return (
        <CacheProvider value={emotionNoPrefixCache}>
            <BlockViewer
                autoId={autoId}
                cname={cname}
                label={label}
                type={type}
                bpConfigs={bpConfigs}
                attrs={{ [DATA_SET_VIEWER]: 'true' }}
                configs={configs}
                css={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
            >
                {content}
            </BlockViewer>
        </CacheProvider>
    );
};
