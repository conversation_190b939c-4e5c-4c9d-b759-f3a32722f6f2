import { ForwardRefRenderFunction, forwardRef } from 'react';
import { Alpha, AlphaProps } from '../Alpha';

export interface HueProps extends Omit<AlphaProps, 'hsva' | 'onChange'> {
    hue?: number;
    onChange?(newHue: { h: number }): void;
}

const HueFR: ForwardRefRenderFunction<HTMLDivElement, HueProps> = (
    { hue = 0, onChange, direction = 'horizontal', ...otherProps },
    ref,
) => {
    return (
        <Alpha
            ref={ref}
            direction={direction}
            background={`linear-gradient(to ${
                direction === 'horizontal' ? 'right' : 'bottom'
            }, rgb(255, 0, 0) 0%, rgb(255, 255, 0) 17%, rgb(0, 255, 0) 33%, rgb(0, 255, 255) 50%, rgb(0, 0, 255) 67%, rgb(255, 0, 255) 83%, rgb(255, 0, 0) 100%)`}
            hsva={{ h: hue, s: 100, v: 100, a: hue / 360 }}
            onChange={(_, interaction) => {
                if (onChange) {
                    onChange({ h: direction === 'horizontal' ? 360 * interaction.left : 360 * interaction.top });
                }
            }}
            {...otherProps}
        />
    );
};

export const Hue = forwardRef(HueFR);
