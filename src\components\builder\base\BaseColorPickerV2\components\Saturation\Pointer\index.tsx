import { FC, HTMLAttributes, useMemo } from 'react';

export interface PointerProps extends HTMLAttributes<HTMLDivElement> {
    top?: string;
    left: string;
    color?: string;
}

export const Pointer: FC<PointerProps> = ({ left, top }) => {
    return useMemo(
        () => (
            <div
                style={{
                    top,
                    left,
                }}
                css={{
                    display: 'flex',
                    position: 'absolute',
                    width: '16px',
                    height: '16px',
                    borderWidth: '3px',
                    borderStyle: 'solid',
                    borderColor: 'white',
                    borderRadius: '50%',
                    transform: 'translate(-0.5rem, -0.5rem)',
                }}
            />
        ),
        [top, left],
    );
};
