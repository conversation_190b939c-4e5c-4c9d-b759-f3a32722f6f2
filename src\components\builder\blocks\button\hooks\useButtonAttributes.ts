/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
import { ButtonBreakpointConfig, ButtonConfigs } from '@/components/builder/blocks/button/types';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
    DATA_SET_ATK_ANIMATION,
} from '@/components/builder/constants/constants';
import { DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';
import {
    DATA_SET_BUTTON_CUSTOM_CSS_STYLE,
    DATA_SET_BUTTON_CUSTOM_CSS_CLASS,
    DATA_SET_BUTTON_CUSTOM_CSS_ENABLE,
    DATA_SET_BUTTON_POSITION,
    DATA_SET_BUTTON_TEXT,
} from '@/components/builder/blocks/button/constants';

const getCustomCssAttributes = (configs: ButtonConfigs) => {
    if (!configs?.customCSS?.enable) return {};

    return {
        [DATA_SET_BUTTON_CUSTOM_CSS_CLASS]: configs.customCSS?.className || undefined,
        [DATA_SET_BUTTON_CUSTOM_CSS_STYLE]: configs.customCSS?.style || undefined,
    };
};

const hasPosition = (position: ButtonBreakpointConfig['position'], autoId: string) => {
    if (!position) return false;
    return position.positionType !== 'default' ? autoId : undefined;
};

const hasAnimation = (animation: ButtonConfigs['animation']) => {
    return animation.type && animation.type !== 'none';
};

const generateButtonAttributes = (
    configs: ButtonConfigs,
    autoId: string,
    currentDevice: string,
    bpConfigs: ButtonBreakpointConfig | any,
) => {
    if (!configs) return { [DATA_SET_AUTO_ID_INNER]: autoId };
    const displayAttr =
        currentDevice === 'desktop'
            ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
            : { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile };
    return {
        [DATA_SET_AUTO_ID_INNER]: autoId,
        [DATA_SET_BUTTON_CUSTOM_CSS_ENABLE]: configs?.customCSS?.enable,
        ...displayAttr,
        [DATA_SET_ATK_ANIMATION]: hasAnimation(configs.animation),
        [DATA_SET_BUTTON_POSITION]: hasPosition(bpConfigs[currentDevice].position, autoId),
        ...getCustomCssAttributes(configs),
        [DATA_SET_BUTTON_TEXT]: autoId,
        className: 'animate__animated',
    };
};

// Hook
export const useButtonAttributes = (
    configs: ButtonConfigs,
    autoId: string,
    currentDevice: string,
    bpConfigs: ButtonBreakpointConfig,
) => {
    return useMemo(
        () => generateButtonAttributes(configs, autoId, currentDevice, bpConfigs),
        [configs, autoId, currentDevice, bpConfigs],
    );
};
