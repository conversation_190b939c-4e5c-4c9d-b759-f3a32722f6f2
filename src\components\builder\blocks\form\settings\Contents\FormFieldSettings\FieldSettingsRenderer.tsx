import { FC } from 'react';
import { DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';
import { formFieldSettingsMap } from './index';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { Box, Text } from '@shopify/polaris';
import { BaseNoBorderLayout } from '@/components/builder/base/BaseNoBorderLayout';
import './styles.scss';
interface FieldSettingsRendererProps {
    formField: FormFieldType;
    selectedBlockId: string;
}

export const FieldSettingsRenderer: FC<FieldSettingsRendererProps> = ({ formField, selectedBlockId }) => {
    const selectedBlockTarget = document.querySelector(
        `[${DATA_SET_AUTO_ID_INNER}="${selectedBlockId}"]`,
    ) as HTMLElement;

    if (!formField) {
        return (
            <Box padding="400">
                <Text variant="bodyMd" as="p" tone="subdued">
                    No field selected
                </Text>
            </Box>
        );
    }

    const SettingsComponent = formFieldSettingsMap[formField.type];

    if (!SettingsComponent) {
        return (
            <Box padding="400">
                <Text variant="bodyMd" as="p" tone="critical">
                    Settings not available for field type: {formField.type}
                </Text>
            </Box>
        );
    }

    return (
        <BaseNoBorderLayout containerClassName="form-field-settings">
            <SettingsComponent formField={formField} selectedBlockTarget={selectedBlockTarget} />
        </BaseNoBorderLayout>
    );
};
