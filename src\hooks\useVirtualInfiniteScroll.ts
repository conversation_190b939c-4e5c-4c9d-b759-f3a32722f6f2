/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect, useCallback, useRef } from 'react';

interface UseVirtualInfiniteScrollOptions<T> {
    /**
     * All items to be managed
     */
    items: T[];
    /**
     * Height of each item in pixels
     */
    itemHeight: number;
    /**
     * Gap between items in pixels
     */
    gap: number;
    /**
     * Number of columns in the grid layout
     */
    columns: number;
    /**
     * Number of extra rows to render above and below the visible area
     */
    buffer?: number;
    /**
     * Callback when scrolled near the end to load more items
     */
    onLoadMore?: () => void;
    /**
     * Whether more items are available to load
     */
    hasMore?: boolean;
    /**
     * Threshold in pixels from bottom to trigger loading more items
     */
    loadMoreThreshold?: number;
    /**
     * Whether currently loading more items
     */
    loading?: boolean;
}

interface VirtualItemStyle {
    position: 'absolute';
    top: number;
    left: string;
    width: string;
    height: number;
}

interface UseVirtualInfiniteScrollResult<T> {
    containerRef: React.RefObject<HTMLDivElement | null>;
    /**
     * Items that should be rendered
     */
    visibleItems: T[];
    /**
     * Function to get positioning styles for an item
     */
    getVirtualItemStyles: (index: number) => VirtualItemStyle;
    /**
     * Total calculated height for the virtual container
     */
    totalHeight: number;
    /**
     * Get position in row and column format
     */
    getGridPosition: (index: number) => { row: number; col: number };
    /**
     * Force recalculation of visible items
     */
    updateVisibleItems: () => void;
}

/**
 * Custom hook for implementing virtual scrolling with infinite loading
 */
export function useVirtualInfiniteScroll<T>({
    items,
    itemHeight,
    gap,
    columns,
    buffer = 2,
    onLoadMore,
    hasMore = false,
    loadMoreThreshold = 100,
    loading = false,
}: UseVirtualInfiniteScrollOptions<T>): UseVirtualInfiniteScrollResult<T> {
    const containerRef = useRef<HTMLDivElement>(null);
    const [visibleItems, setVisibleItems] = useState<T[]>([]);

    /**
     * Get grid position (row, column) for an item index
     */
    const getGridPosition = useCallback(
        (index: number) => {
            const row = Math.floor(index / columns);
            const col = index % columns;
            return { row, col };
        },
        [columns],
    );

    /**
     * Calculate item styling based on its position in the grid
     */
    const getVirtualItemStyles = useCallback(
        (index: number): VirtualItemStyle => {
            const { row, col } = getGridPosition(index);

            return {
                position: 'absolute',
                top: row * (itemHeight + gap),
                left: `${col * (100 / columns)}%`,
                width: `${100 / columns - 1}%`,
                height: itemHeight,
            };
        },
        [columns, itemHeight, gap, getGridPosition],
    );

    /**
     * Calculate which items should be visible based on scroll position
     */
    const updateVisibleItems = useCallback(() => {
        const containerElement = containerRef.current;
        if (!containerElement) return;

        const { scrollTop, clientHeight } = containerElement;
        const rowHeight = itemHeight + gap;

        // Calculate visible range
        const startRow = Math.floor(scrollTop / rowHeight);
        const visibleRows = Math.ceil(clientHeight / rowHeight);

        // Add buffer
        const firstRow = Math.max(0, startRow - buffer);
        const lastRow = Math.min(Math.ceil(items.length / columns), startRow + visibleRows + buffer);

        // Calculate item indices
        const startIndex = firstRow * columns;
        const endIndex = Math.min(items.length, lastRow * columns);

        // Get visible items
        setVisibleItems(items.slice(startIndex, endIndex));
    }, [containerRef, itemHeight, gap, columns, buffer, items]);

    /**
     * Handle scroll events
     */
    const handleScroll = useCallback(() => {
        if (!containerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;

        // Load more items when user scrolls near the bottom
        if (scrollHeight - scrollTop - clientHeight < loadMoreThreshold && !loading && hasMore && onLoadMore) {
            onLoadMore();
        }

        // Update visible items
        updateVisibleItems();
    }, [containerRef, loadMoreThreshold, loading, hasMore, onLoadMore, updateVisibleItems]);

    // Calculate total height for the scrollable container
    const rowCount = Math.ceil(items.length / columns);
    const totalHeight = rowCount * (itemHeight + gap);

    // Set up scroll event listener
    useEffect(() => {
        const currentContainer = containerRef.current;
        if (currentContainer) {
            currentContainer.addEventListener('scroll', handleScroll);

            // Initial calculation
            updateVisibleItems();
        }

        return () => {
            if (currentContainer) {
                currentContainer.removeEventListener('scroll', handleScroll);
            }
        };
    }, [loading, hasMore]);

    // Update visible items when the items array changes
    useEffect(() => {
        updateVisibleItems();
    }, [items.length]);

    return {
        containerRef,
        visibleItems,
        totalHeight,
        getGridPosition,
        updateVisibleItems,
        getVirtualItemStyles,
    };
}
