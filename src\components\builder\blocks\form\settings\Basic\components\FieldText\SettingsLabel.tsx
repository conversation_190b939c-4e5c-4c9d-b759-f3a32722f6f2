import {
    textAlignOptions,
    textDecorationOptions,
    textDirectionOptions,
    textFontWeightOptions,
    textTransformOptions,
} from '@/components/builder/data/options';
import {
    SettingsColorPicker,
    SettingsFontFamily,
    SettingsInput,
    SettingsSelect,
    SettingsSwitchTab,
} from '@/components/builder/settings';
import { BlockStack, Box } from '@shopify/polaris';
import { FC } from 'react';

interface SettingsLabelProps {
    blockId: string;
    path: string;
    isUpdateConfigs: boolean;
}

export const SettingsLabel: FC<SettingsLabelProps> = ({ blockId, path, isUpdateConfigs }) => {
    return (
        <BlockStack gap="400">
            <Box paddingBlock="300" paddingInline="200" borderWidth="025" borderColor="border" borderRadius="200">
                <BlockStack gap="400">
                    <SettingsFontFamily
                        blockId={blockId}
                        path={`${path}.labelFontFamily`}
                        isUpdateConfigs={isUpdateConfigs}
                    />
                    <SettingsSelect
                        label="Text Transform"
                        blockId={blockId}
                        path={`${path}.labelTextTransform`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textTransformOptions}
                    />
                    <SettingsSelect
                        label="Font Weight"
                        blockId={blockId}
                        path={`${path}.labelFontWeight`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textFontWeightOptions}
                    />
                    <SettingsInput
                        label="Font Size"
                        blockId={blockId}
                        path={`${path}.labelFontSize`}
                        isUpdateConfigs={isUpdateConfigs}
                        inputProps={{
                            min: 4,
                            suffix: 'px',
                        }}
                    />
                    <SettingsSwitchTab
                        label="Text Align"
                        blockId={blockId}
                        path={`${path}.labelTextAlign`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textAlignOptions}
                    />
                    <SettingsSwitchTab
                        label="Text Decoration"
                        blockId={blockId}
                        path={`${path}.labelTextDecoration`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textDecorationOptions}
                    />
                    <SettingsSwitchTab
                        label="Text Direction"
                        blockId={blockId}
                        path={`${path}.labelTextDirection`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textDirectionOptions}
                    />
                    <SettingsColorPicker
                        label="Text Color"
                        blockId={blockId}
                        path={`${path}.labelColor`}
                        isUpdateConfigs={isUpdateConfigs}
                    />
                </BlockStack>
            </Box>
        </BlockStack>
    );
};
