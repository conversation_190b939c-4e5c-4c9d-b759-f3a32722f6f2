import React from 'react';
import { FC, Fragment, PropsWithChildren } from 'react';
import { BaseModalShapeProps } from '@/components/builder/base/BaseSpacing/components/BaseModalShape';
import { BaseModalTitle } from '@/components/builder/base/BaseSpacing/components/BaseModalTitle';
import { ReactComponent as Lock } from '@/assets/svgs/Border/lock.svg';
import { ReactComponent as Unlock } from '@/assets/svgs/Border/unlock.svg';

interface BaseSpacingProps {
    isLockedData?: boolean;
    onLock?: () => void;
    isMargin?: boolean;
    isPadding?: boolean;
    paddingItemRenderer?: FC<Pick<BaseModalShapeProps, 'type' | 'side'>>;
    marginItemRenderer?: FC<Pick<BaseModalShapeProps, 'type' | 'side'>>;
}
const sides: ('top' | 'right' | 'bottom' | 'left')[] = ['top', 'right', 'bottom', 'left'];

export const BaseSpacing: FC<PropsWithChildren<BaseSpacingProps>> = ({
    isLockedData,
    isMargin,
    isPadding,
    paddingItemRenderer,
    marginItemRenderer,
    onLock,
}) => {
    return (
        <div className="setting-space-container">
            {isMargin && (
                <div className="setting-space-container--margin">
                    <BaseModalTitle type="margin" title="Margin" />
                    {sides.map((side) => {
                        const props = { type: 'margin' as const, side };
                        return (
                            <Fragment key={side}>
                                {marginItemRenderer && React.createElement(marginItemRenderer, props)}
                            </Fragment>
                        );
                    })}
                </div>
            )}
            {isPadding && (
                <div className="setting-space-container--padding">
                    <BaseModalTitle type="padding" title="Padding" />
                    {sides.map((side) => {
                        const props = { type: 'padding' as const, side };
                        return (
                            <Fragment key={side}>
                                {paddingItemRenderer && React.createElement(paddingItemRenderer, props)}
                            </Fragment>
                        );
                    })}
                </div>
            )}
            <button className="setting-space-container--link" onClick={onLock}>
                {isLockedData ? <Lock /> : <Unlock />}
            </button>
        </div>
    );
};
