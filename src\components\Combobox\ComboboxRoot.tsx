import { useState, useRef, useId, useEffect } from 'react';
import { ComboboxRootProps } from './types';
import { ComboboxContext } from './context';

export const ComboboxRoot = ({ children, id }: ComboboxRootProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const optionsRef = useRef<HTMLDivElement | null>(null);
    const triggerRef = useRef<HTMLDivElement | null>(null);
    const listRef = useRef<HTMLUListElement | null>(null);
    const prevSelectedValueRef = useRef(selectedValue);
    const uniqueId = useId();
    const comboboxId = id || uniqueId;

    useEffect(() => {
        if (prevSelectedValueRef.current && !selectedValue) {
            setIsOpen(true);
        }
        prevSelectedValueRef.current = selectedValue;
    }, [selectedValue]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                isOpen &&
                optionsRef.current &&
                triggerRef.current &&
                !optionsRef.current.contains(event.target as Node) &&
                !triggerRef.current.contains(event.target as Node)
            ) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    const value = {
        isOpen,
        setIsOpen,
        selectedValue,
        setSelectedValue,
        highlightedIndex,
        setHighlightedIndex,
        optionsRef,
        triggerRef,
        listRef,
        uniqueId: comboboxId,
    };

    return (
        <ComboboxContext.Provider value={value}>
            <div data-scope="combobox" data-part="root" data-state={isOpen ? 'open' : 'closed'}>
                {children}
            </div>
        </ComboboxContext.Provider>
    );
};
