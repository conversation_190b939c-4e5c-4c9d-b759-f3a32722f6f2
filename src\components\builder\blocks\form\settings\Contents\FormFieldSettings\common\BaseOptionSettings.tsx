import { FC, Fragment } from 'react';
import { BlockStack, Button, Icon } from '@shopify/polaris';
import { InfoIcon, PlusCircleIcon } from '@shopify/polaris-icons';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseItemLayout } from '@/components/builder/base';
import { SettingsCheckbox, SettingsSliderInput, SettingsSortable, SettingsInput } from '@/components/builder/settings';
import { useFormFieldPaths } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { SortableOption } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/SortableOption';
import { type FormField } from '@/components/builder/blocks/form/types';

interface BaseOptionSettingsProps {
    formField: FormField;
    type: string;
    children?: React.ReactNode | React.ReactNode[];
    handleAddOption?: () => void;
    insertPosition?: number | number[];
    selectedBlockTarget?: HTMLElement;
}

export const BaseOptionSettings: FC<BaseOptionSettingsProps> = ({
    formField,
    type,
    children,
    handleAddOption,
    insertPosition = -1,
    selectedBlockTarget,
}) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH_FORM } = useFormFieldPaths(formField);

    const elements = [
        <SettingsInput
            key="title"
            path={`${DEFAULT_PATH_FORM}.label`}
            blockId={selectedBlockId}
            isUpdateConfigs
            inputProps={{ type: 'text', placeholder: 'Enter label' }}
            direction="column"
            label="Title"
            textProps={{ fontWeight: 'medium' }}
            tooltipContent="Add field titles to help customers identify information more easily"
            hasTooltip
            tooltipChildren={<Icon source={InfoIcon} />}
        />,
        <SettingsInput
            key="key"
            path={`${DEFAULT_PATH_FORM}.key`}
            blockId={selectedBlockId}
            isUpdateConfigs
            inputProps={{ type: 'text' }}
            direction="column"
            label="Key"
            textProps={{ fontWeight: 'medium' }}
            tooltipContent="Key is only, required and cannot be duplicated"
            hasTooltip
            tooltipChildren={<Icon source={InfoIcon} />}
        />,
        <BaseItemLayout
            key="options"
            direction="column"
            textProps={{ as: 'p', fontWeight: 'medium', variant: 'bodyMd', children: 'Options' }}
            tooltipChildren={<Icon source={InfoIcon} />}
            hasTooltip
            tooltipContent="Add options"
        >
            <BlockStack gap="200">
                <SettingsSortable
                    blockId={selectedBlockId}
                    path={`${DEFAULT_PATH_FORM}.options`}
                    isUpdateConfigs
                    handle={true}
                    useDragOverlay={true}
                    containerProps={{ className: 'option-container' }}
                    itemRenderer={(props) => <SortableOption type={type} {...props} blockId={selectedBlockId} />}
                />
                <Button
                    textAlign="left"
                    variant="plain"
                    pressed={false}
                    icon={PlusCircleIcon}
                    onClick={handleAddOption}
                >
                    Add option
                </Button>
            </BlockStack>
        </BaseItemLayout>,

        <SettingsCheckbox
            key="separateLine"
            path={`${DEFAULT_PATH_FORM}.separateLine`}
            blockId={selectedBlockId}
            isUpdateConfigs
            direction="column"
            hideTitle
            label="Separate line for this field"
        />,

        <SettingsSliderInput
            selectedBlockTarget={selectedBlockTarget}
            cssVariable={`--form-${formField.key}-width`}
            key="fieldWidth"
            path={`fieldSizes.${formField.key}.fieldWidth`}
            blockId={selectedBlockId}
            isUpdateConfigs={false}
            direction="column"
            title="Field width"
            min={10}
            sliderProps={{
                min: 10,
                max: 100,
                step: 1,
            }}
            inputProps={{
                suffix: '%',
                min: 10,
                max: 100,
                step: 1,
            }}
        />,
    ];

    const childrenArray = Array.isArray(children) ? children : [children];
    const positionsArray = Array.isArray(insertPosition) ? insertPosition : [insertPosition];

    childrenArray.forEach((child, index) => {
        const position = positionsArray[index] ?? -1;
        if (position >= 0 && position < elements.length) {
            elements.splice(position, 0, <Fragment key={`children-${index}`}>{child}</Fragment>);
        } else if (position === -1) {
            elements.push(<Fragment key={`children-${index}`}>{child}</Fragment>);
        }
    });

    return <BlockStack gap="400">{elements}</BlockStack>;
};
