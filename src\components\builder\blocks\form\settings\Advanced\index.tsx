import { FC } from 'react';
import {
    Box,
    // BlockStack,
    // InlineStack,
} from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
// import { getBlockBPProperty } from '@/utils/shared';
import { BaseCollapse, BaseToggle } from '@/components/builder/base';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import {
    SettingsAnimation,
    SettingsCustomCSS,
    SettingsDimension,
    // SettingsInput,
    // SettingsSelect,
    // SettingsSwitchTab,
} from '@/components/builder/settings';
import { SettingsDisplay } from '@/components/builder/settings/SettingsDisplay';
// import { alignHorizontalOptions, alignVerticalOptions, buttonPositionOptions } from '@/components/builder/data/options';

import './styles.scss';
import { SettingsFormSubmit } from './components';

export const Advanced: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state?.selectedBlockId) as string;
    // const positionType = getBlockBPProperty('position.positionType', selectedBlockId);
    // const stickToType = getBlockBPProperty('position.stickTo.stickToType', selectedBlockId);

    return (
        <BaseHasBorderLayout>
            {/* <BaseCollapse label="Align" containerClassName="form-align-settings">
                <InlineStack blockAlign="center" align="space-between">
                    <SettingsSwitchTab
                        options={alignHorizontalOptions}
                        blockId={selectedBlockId}
                        path="formAlignment.alignSelf"
                        direction="column"
                        isUpdateConfigs={false}
                        switchTabProps={{
                            fullWidth: true,
                        }}
                    />
                    <SettingsSwitchTab
                        options={alignVerticalOptions}
                        blockId={selectedBlockId}
                        path="formAlignment.justifySelf"
                        direction="column"
                        isUpdateConfigs={false}
                        switchTabProps={{
                            fullWidth: true,
                        }}
                    />
                </InlineStack>
            </BaseCollapse> */}

            <BaseCollapse label="Size">
                <SettingsDimension blockId={selectedBlockId} isUpdateConfigs={false} isForm={true} />
            </BaseCollapse>

            <BaseCollapse label="Form submit setting" containerClassName="form-submit-setting">
                <SettingsFormSubmit blockId={selectedBlockId} isUpdateConfigs path="submit" />
            </BaseCollapse>

            <BaseCollapse label="Animation">
                <SettingsAnimation path="animation" blockId={selectedBlockId} isUpdateConfigs />
            </BaseCollapse>

            <BaseCollapse label="Display on">
                <Box paddingBlockStart="300">
                    <SettingsDisplay blockId={selectedBlockId} />
                </Box>
            </BaseCollapse>

            {/* <BaseCollapse label="Position">
                <Box paddingBlockStart="300">
                    <BlockStack gap="300">
                        <SettingsSelect
                            blockId={selectedBlockId}
                            path="position.positionType"
                            options={buttonPositionOptions}
                            isUpdateConfigs={false}
                            label="Position"
                        />
                        {positionType === 'sticky' && (
                            <>
                                <SettingsSelect
                                    blockId={selectedBlockId}
                                    isUpdateConfigs={false}
                                    path="position.stickTo.stickToType"
                                    options={[
                                        {
                                            id: 'top',
                                            content: 'Top',
                                        },
                                        {
                                            id: 'bottom',
                                            content: 'Bottom',
                                        },
                                        {
                                            id: 'default',
                                            content: 'Default',
                                        },
                                    ]}
                                    label="Fixed position"
                                />
                                {stickToType === 'top' && (
                                    <>
                                        <SettingsInput
                                            blockId={selectedBlockId}
                                            isUpdateConfigs={false}
                                            path="position.stickTo.top"
                                            label="Top"
                                            inputProps={{
                                                type: 'number',
                                                suffix: '%',
                                            }}
                                        />
                                    </>
                                )}
                                {stickToType === 'bottom' && (
                                    <SettingsInput
                                        blockId={selectedBlockId}
                                        isUpdateConfigs={false}
                                        path="position.stickTo.bottom"
                                        label="Bottom"
                                        inputProps={{
                                            type: 'number',
                                            suffix: '%',
                                        }}
                                    />
                                )}
                            </>
                        )}
                    </BlockStack>
                </Box>
            </BaseCollapse> */}

            <BaseCollapse
                label="Custom CSS"
                isUpdateToggle
                blockId={selectedBlockId}
                path="customCSS.enable"
                isUpdateConfigs
                labelContent={(open, setOpen) => <BaseToggle valueData={open} onChange={() => setOpen?.(!open)} />}
            >
                <SettingsCustomCSS blockId={selectedBlockId} isUpdateConfigs path="customCSS" label="Custom CSS" />
            </BaseCollapse>
        </BaseHasBorderLayout>
    );
};
