{"name": "autoketing-ldp-builder", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "lint-staged": {"**/*.{jx,jsx,ts,tsx}": ["npm run lint"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@floating-ui/react": "^0.27.7", "@giaminhautoketing/auto-builder": "^1.0.10", "@pqina/pintura": "^8.92.14", "@pqina/react-pintura": "^9.0.4", "@shopify/app-bridge-react": "^4.1.7", "@shopify/polaris": "^13.9.3", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.11.9", "@tiptap/extension-subscript": "^2.11.9", "@tiptap/extension-superscript": "^2.11.9", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.9", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@uiw/color-convert": "^2.5.5", "@zag-js/color-picker": "^1.10.0", "@zag-js/react": "^1.10.0", "animate.css": "^4.1.1", "autoketing-ldp-builder": ".", "axios": "^1.8.3", "clsx": "^2.1.1", "immer": "^10.1.1", "jszip": "^3.10.1", "keen-slider": "^6.8.6", "object-path-immutable": "^4.1.2", "react": "18.3.1", "react-ace": "^14.0.1", "react-apexcharts": "^1.7.0", "react-dom": "18.3.1", "react-router-dom": "^6.27.0", "react-virtualized": "^9.22.6", "sass": "^1.86.0", "use-broadcast-ts": "^2.0.1", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "9.21.0", "@shopify/app-bridge-types": "^0.0.16", "@swc/core": "^1.12.5", "@swc/plugin-emotion": "^10.0.2", "@types/node": "^22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/react-virtualized": "^9.22.2", "@vitejs/plugin-react-swc": "3.8.0", "eslint": "9.21.0", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "15.15.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "typescript": "5.7.2", "typescript-eslint": "8.24.1", "vite": "6.2.0", "vite-plugin-svgr": "^4.3.0"}}