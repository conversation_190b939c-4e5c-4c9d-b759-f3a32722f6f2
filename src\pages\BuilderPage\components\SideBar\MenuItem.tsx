import { IconSource, Icon, Tooltip } from '@shopify/polaris';
import { FC } from 'react';

const STYLES = {
    sidebarButton: 'sidebar-button',
    active: 'active',
    iconWrapper: 'icon-wrapper',
};

export interface MenuItemProps {
    icon: IconSource | React.ReactNode;
    active?: boolean;
    onClick?: () => void;
    title?: string;
}

const MenuItem: FC<MenuItemProps> = ({ icon, active = false, onClick, title }) => {
    const buttonClasses = `${STYLES.sidebarButton} ${active ? STYLES.active : ''}`;

    return (
        <Tooltip content={title} dismissOnMouseOut>
            <button type="button" className={buttonClasses} onClick={onClick} aria-pressed={active} aria-label={title}>
                {typeof icon === 'string' && icon.trim().startsWith('<svg') && icon.trim().endsWith('</svg>') ? (
                    <span dangerouslySetInnerHTML={{ __html: icon }} />
                ) : typeof icon === 'string' || typeof icon === 'function' ? (
                    <Icon source={icon as IconSource} />
                ) : (
                    icon
                )}
            </button>
        </Tooltip>
    );
};

export default MenuItem;
