import { FC, useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import { InlineStack, Button, Tooltip, Text, Icon, Spinner } from '@shopify/polaris';
import { DeleteIcon, ExchangeIcon, UploadIcon } from '@shopify/polaris-icons';
import { useMediaManager } from '@/pages/BuilderPage/components/MediaManager/useMediaManager';
import { useSettings } from '@/pages/BuilderPage/hooks';
import { getVideoEmbedCode } from '../../blocks/video/helper';
import './styles.scss';

interface SettingsChooseVideoProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const SettingsChooseVideo: FC<SettingsChooseVideoProps> = ({ blockId, path, isUpdateConfigs }) => {
    const videoContainerRef = useRef<HTMLDivElement>(null);
    const [isLoading, setIsLoading] = useState(false);
    const { value, updateValue } = useSettings({ blockId, path, isUpdateConfigs });
    const setOpen = useMediaManager((state) => state.setOpen);

    const openMediaManager = () => {
        setOpen(
            { mode: 'manager' },
            {
                currentTab: 'upload',
                sortBy: 'video',
                allows: 'video',
                onSelect(media) {
                    if (!Array.isArray(media)) {
                        updateValue(media.url);
                    }
                },
            },
        );
    };

    const onDelete = () => {
        if (value) {
            updateValue('');
        }
    };

    const embedCode = getVideoEmbedCode(value as string, {
        showControls: true,
        autoplay: false,
        muted: false,
        loop: false,
    });

    useEffect(() => {
        setIsLoading(true);

        if (videoContainerRef.current) {
            const container = videoContainerRef.current;

            const videoElement = container.querySelector('iframe') || container.querySelector('video');

            if (videoElement) {
                const handleLoad = () => {
                    setIsLoading(false);
                };

                videoElement.addEventListener('load', handleLoad);

                if (videoElement instanceof HTMLVideoElement) {
                    videoElement.addEventListener('loadeddata', handleLoad);
                }

                return () => {
                    videoElement.removeEventListener('load', handleLoad);
                    if (videoElement instanceof HTMLVideoElement) {
                        videoElement.removeEventListener('loadeddata', handleLoad);
                    }
                };
            }
        }
    }, [embedCode]);

    return (
        <div className="settings-choose-video">
            {value ? (
                <>
                    {isLoading && (
                        <div className="loading-container">
                            <div className="spinner-container">
                                <Spinner size="large" />
                            </div>
                        </div>
                    )}
                    <div
                        ref={videoContainerRef}
                        dangerouslySetInnerHTML={{ __html: embedCode }}
                        className={clsx('video-container', { hidden: isLoading })}
                    />
                </>
            ) : (
                <div className="empty-video">
                    <div
                        className="empty-content"
                        onClick={(e) => {
                            e.stopPropagation();
                            openMediaManager();
                        }}
                    >
                        <div className="icon-container">
                            <Icon source={UploadIcon} />
                        </div>
                        <Text as="p" variant="bodySm" tone="subdued" alignment="center">
                            <Text as="span" variant="bodySm" tone="inherit">
                                Click to select video files
                            </Text>
                        </Text>
                    </div>
                </div>
            )}
            <InlineStack align="end" gap="200">
                <Tooltip content="Change" dismissOnMouseOut>
                    <Button variant="tertiary" icon={ExchangeIcon} size="micro" onClick={openMediaManager} />
                </Tooltip>
                <Tooltip content="Delete" dismissOnMouseOut>
                    <Button variant="tertiary" icon={DeleteIcon} size="micro" onClick={onDelete} />
                </Tooltip>
            </InlineStack>
        </div>
    );
};
