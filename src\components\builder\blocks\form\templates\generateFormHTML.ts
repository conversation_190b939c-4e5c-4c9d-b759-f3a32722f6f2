/* eslint-disable @typescript-eslint/no-explicit-any */

import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { FormField } from '../types';

export const generateFormHTML = (blockId: string, block: Auto_BlockData, isLiquid: boolean = false): string => {
    const { form, contentForm, submit, animation, customCSS } = block.configs as any;
    const customClass = customCSS.enable ? ` ${customCSS.class}` : '';
    const customStyle = customCSS.enable ? ` ${customCSS.style}` : '';
    const formOpenTag = isLiquid ? 
    `{% form 'contact', class: 'atk-form ${customClass}', style: '${customStyle}', data-atk-animation:"${animation.type !== 'none'}", data-auto-id-inner:"${blockId}" %}`: 
    `<form class="atk-form ${customClass}" style="${customStyle}" data-atk-animation="${animation.type !== 'none'}" data-auto-id-inner="${blockId}">`;
    const formCloseTag = isLiquid ? `{% endform %}` : '</form>';
  
    return `
        <div class="atk-form-wrapper" data-auto-id="${blockId}" data-atk-cname="forms">
            ${formOpenTag}

                <h2 class="atk-form-title" data-atk-form-title="${blockId}">${contentForm.formTitle}</h2>
                <div class="atk-form-field-wrapper" data-atk-form-field-wrap="${blockId}">
                    ${form
                        .map((field: FormField, index: number) => {
                            if (
                                field.type === 'text' ||
                                field.type === 'password' ||
                                field.type === 'number' ||
                                field.type === 'tel'
                            ) {
                            return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                    <input type="${field.type}" 
                                        id="${field.id}" 
                                        data-atk-form-input="${blockId}"
                                        data-atk-form-field-id="${field.id}"
                                        ${field.validations?.readOnly ?`readonly`:''}   
                                        ${field.pattern?.pattern ? `pattern="${field.pattern.pattern}"` : ''} 
                                        ${field.validations?.required ? 'required' : ''}
                                        ${field.characterLimit?.setCharLimit ? `maxlength="${field.characterLimit.charLimit}"` : ''}
                                        name="contact[${field.id}]" 
                                    />
                                </div>`
                            }
                            if (field.type === 'email') {
                                return `
                                    <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                    <div class="atk-form-field" data-atk-form-field="${blockId}">
                                        <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                        <input 
                                            type="email" 
                                            id="${field.id}" 
                                            data-atk-form-input="${blockId}"
                                            data-atk-form-field-id="${field.id}"
                                            ${field.validations?.readOnly ?`readonly`:''}   
                                            ${field.pattern?.pattern ? `pattern="${field.pattern.pattern}"` : ''} 
                                            ${field.characterLimit?.setCharLimit ? `maxlength="${field.characterLimit.charLimit}"` : ''}
                                            name="contact[${field.id}]" 
                                        />
                                    </div>`
                            }
                            if (field.type === 'long-text') {
                            return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                    <textarea id="${field.id}" 
                                        data-atk-form-input="${blockId}"
                                        data-atk-form-field-id="${field.id}"
                                        ${field.validations?.readOnly ?`readonly`:''}   
                                        ${field.pattern?.pattern ? `pattern="${field.pattern.pattern}"` : ''} 
                                        ${field.validations?.required ? 'required' : ''}
                                        ${field.characterLimit?.setCharLimit ? `maxlength="${field.characterLimit.charLimit}"` : ''}
                                        name="contact[${field.id}]" 
                                    ></textarea>
                                </div>`
                            }
                            if (field.type === 'checkbox') {
                            return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                    <div class="atk-form-checkbox-wrapper" data-atk-form-checkbox-multi-element-${index}="${blockId}">
                                        ${field.options
                                            .map((option: any) => `
                                                <div class="atk-form-checkbox-element" data-atk-form-checkbox-element="${blockId}">
                                                    <div class="atk-form-checkbox-element-wrapper" data-atk-form-checkbox-element-wrap="${blockId}">
                                                        <div class="atk-form-checkbox-element-inner" data-atk-form-checkbox-element-inner="${blockId}">
                                                            <input type="checkbox" 
                                                                id="${option.id}" 
                                                                data-atk-form-field-id="${field.id}" 
                                                                ${option.defaultChecked ? 'checked' : ''} 
                                                                ${field.validations?.required ? 'required' : ''} 
                                                                name="contact[${field.id}]" 
                                                            />
                                                        </div>
                                                        <div class="atk-form-checkbox-element-label">
                                                            <label for="${option.id}" data-atk-form-checkbox-label="${blockId}">${option.label}</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            `)
                                            .join('')}
                                    </div>
                                </div>`
                            }
                            if (field.type === 'radio') {
                            return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                    <div class="atk-form-radio-wrapper" data-atk-form-radio-multi-element-${index}="${blockId}">    
                                        ${field.options
                                            .map((option: any) => `
                                                <div class="atk-form-radio-element" data-atk-form-radio-element="${blockId}">
                                                    <div class="atk-form-radio-element-wrapper" data-atk-form-radio-element-wrap="${blockId}">
                                                        <div class="atk-form-radio-element-inner" data-atk-form-radio-element-inner="${blockId}">
                                                            <input type="radio" 
                                                                id="${option.id}" 
                                                                data-atk-form-field-id="${field.id}" 
                                                                ${field.initialText === option.value ? 'checked' : ''} 
                                                                ${field.validations?.required ? 'required' : ''} 
                                                                name="contact[${field.id}]" 
                                                            />
                                                        </div>
                                                        <div class="atk-form-radio-element-label">
                                                            <label for="${option.id}" data-atk-form-radio-label="${blockId}">${option.label}</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            `)
                                            .join('')}
                                    </div>
                                </div>`
                            }
                            if (field.type === 'dropdown') {
                            return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <label for="${field.id}" data-atk-form-field-label="${blockId}">${field.label}</label>
                                   <select id="${field.id}" 
                                        data-atk-form-input="${blockId}" 
                                        data-atk-form-field-id="${field.id}" 
                                        name="contact[${field.id}]"
                                        ${field.showInitialText === 'default' ? ` value="${field.initialText}"` : ''} 
                                        ${field.validations?.required ? 'required' : ''}
                                    >
                                        ${field.showInitialText === 'placeholder' ? 
                                            `<option selected disabled value="">${field.placeholder}</option>` : ''}
                                        ${field.options
                                            .map((option: any) => `<option value="${option.value}">${option.label}</option>`)
                                            .join('')}
                                    </select>
                                </div>`
                            }
                            if(field.type === 'free-text') {
                                return `
                                <span class="atk-form-field-separator-line" data-atk-form-separator-line-${index}="${blockId}"></span>
                                <div class="atk-form-field" data-atk-form-field="${blockId}">
                                    <span data-atk-form-field-id="${field.id}">${field.description}</span>
                                </div>`
                            }
                        })
                        .join('')}
                    </div>
                    <div class="atk-form-submit-wrapper" data-atk-form-submit-wrap="${blockId}">
                        <button type="submit" class="atk-button-submit" data-atk-form-submit="${blockId}">${contentForm.buttonTitle}</button>
                    </div>
                </div>
            {%- if form.posted_successfully? -%}
                ${submit.action === 'open-link' ?
                    `<script>
                        ${submit.openLink.open === 'new-window' ?
                            `window.open('${submit.openLink.url}', '_blank');` : 
                            `window.location.href = '${submit.openLink.url}';`
                        }  
                    </script>` : ''
                }
               
            <div class="atk-form-success-message" tabindex="-1" autofocus>
                ${submit.successMessage}
            </div>
            {%- endif -%}
            {%- if form.errors -%}
                <div class="atk-form-error-message" role="alert" tabindex="-1">
                Email is required! ${submit.errorMessage}
                </div>
            {%- endif -%}
            ${formCloseTag}
        </div>
    `;
};
