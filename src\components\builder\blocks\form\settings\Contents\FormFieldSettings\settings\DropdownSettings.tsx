import { FC } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { Icon } from '@shopify/polaris';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsSelect, SettingsCheckbox, SettingsSliderInput } from '@/components/builder/settings';
import { InfoIcon } from '@shopify/polaris-icons';
import { getBlockProperty } from '@/utils/shared';
import {
    useFormFieldPaths,
    useHandleAddOption,
} from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { BaseOptionSettings } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/BaseOptionSettings';

interface DropdownSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget?: HTMLElement;
}

export const DropdownSettings: FC<DropdownSettingsProps> = ({ formField, selectedBlockTarget }) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH, DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const showInitialText = getBlockProperty(`${DEFAULT_PATH}.showInitialText`, selectedBlockId);
    const opts = getBlockProperty(`${DEFAULT_PATH}.options`, selectedBlockId);
    return (
        <BaseOptionSettings
            formField={formField}
            type="dropdown"
            handleAddOption={useHandleAddOption(formField, opts)}
            insertPosition={[3, 4, 5, 6]}
            selectedBlockTarget={selectedBlockTarget}
        >
            <SettingsSelect
                path={`${DEFAULT_PATH_FORM}.showInitialText`}
                blockId={selectedBlockId}
                isUpdateConfigs
                direction="column"
                label="Show initial text"
                options={[
                    {
                        id: 'default',
                        content: 'Default',
                    },
                    {
                        id: 'placeholder',
                        content: 'Placeholder',
                    },
                ]}
                textProps={{ fontWeight: 'medium' }}
                tooltipContent="Display suggested content in fields to help customers enter correct content"
                hasTooltip
                tooltipChildren={<Icon source={InfoIcon} />}
            />

            {showInitialText === 'default' && (
                <SettingsSelect
                    path={`${DEFAULT_PATH_FORM}.initialText`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    options={opts?.map((item: { value: string; label: string }) => ({
                        id: item.value,
                        content: item.label,
                    }))}
                    direction="column"
                    label="Default"
                    textProps={{ fontWeight: 'medium' }}
                />
            )}

            {showInitialText === 'placeholder' && (
                <SettingsInput
                    path={`${DEFAULT_PATH_FORM}.placeholder`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    inputProps={{ type: 'text', placeholder: 'Enter placeholder' }}
                    direction="column"
                    label="Placeholder"
                    textProps={{ fontWeight: 'medium' }}
                />
            )}

            <SettingsCheckbox
                path={`${DEFAULT_PATH_FORM}.validations.required`}
                blockId={selectedBlockId}
                isUpdateConfigs
                direction="column"
                label="Required"
                textProps={{ fontWeight: 'medium', children: 'Validations' }}
            />

            <SettingsSliderInput
                selectedBlockTarget={selectedBlockTarget}
                cssVariable={`--form-${formField.key}-height`}
                path={`fieldSizes.${formField.key}.fieldHeight`}
                blockId={selectedBlockId}
                isUpdateConfigs={false}
                direction="column"
                title="Field height"
                min={10}
                sliderProps={{
                    min: 10,
                    max: 100,
                    step: 1,
                }}
                inputProps={{
                    suffix: 'px',
                    min: 10,
                    max: 100,
                    step: 1,
                }}
            />
        </BaseOptionSettings>
    );
};
