import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BlockStack } from '@shopify/polaris';
import { Align } from './components/Align';
import { Space } from './components/Space';
import { Border } from './components/Border';
import { Animation } from './components/Animation';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { SettingsDisplay } from '@/components/builder/settings/SettingsDisplay';
import { Position } from './components/Position';
import { BaseToggle } from '@/components/builder/base/BaseToggle';
import { SettingsCustomCSS } from '@/components/builder/settings/SettingsCustomCSS';
const DEFAULT_PATH_POSITION = 'position';

export const Advanced = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;

    return (
        <BaseHasBorderLayout>
            <BlockStack gap="400">
                <Align id={selectedBlockId} />
                <Space id={selectedBlockId} />
                <Border id={selectedBlockId} />
                <Animation id={selectedBlockId} />
                <BaseCollapse label="Display on">
                    <SettingsDisplay blockId={selectedBlockId} boxProps={{ paddingBlockStart: '300' }} />
                </BaseCollapse>
                <Position
                    blockId={selectedBlockId}
                    path={DEFAULT_PATH_POSITION}
                    label="Position"
                    isUpdateConfigs={false}
                />
                <BaseCollapse
                    label="Custom CSS"
                    isUpdateToggle
                    blockId={selectedBlockId}
                    path="customCSS.enable"
                    isUpdateConfigs
                    labelContent={(open, setOpen) => <BaseToggle valueData={open} onChange={() => setOpen?.(!open)} />}
                >
                    <SettingsCustomCSS blockId={selectedBlockId} isUpdateConfigs path="customCSS" label="Custom CSS" />
                </BaseCollapse>
            </BlockStack>
        </BaseHasBorderLayout>
    );
};
