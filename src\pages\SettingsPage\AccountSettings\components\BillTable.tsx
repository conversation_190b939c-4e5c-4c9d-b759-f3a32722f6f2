import { FC } from 'react';
import {
    IndexTable,
    useIndexResourceState,
    Text,
    Badge,
    Card,
    InlineStack,
    Button,
    BlockStack,
    Box,
    Pagination,
} from '@shopify/polaris';
import '../style.scss';

export const BillTable: FC = () => {
    const orders = [
        {
            id: '1020',
            date: 'Jul 20 at 4:34pm',
            paymentStatus: <Badge tone="success">Activated</Badge>,
            amount: '$969.44',
            disabled: false,
        },
        {
            id: '1019',
            date: 'Jul 20 at 3:46pm',
            paymentStatus: <Badge tone="warning">Canceled</Badge>,
            amount: '$701.19',
            disabled: true,
        },
        {
            id: '1018',
            date: 'Jul 20 at 3.44pm',
            paymentStatus: <Badge tone="info">Pending</Badge>,
            amount: '$798.24',
            disabled: false,
        },
    ];
    const resourceName = {
        singular: 'order',
        plural: 'orders',
    };

    const selectableOrders = orders.filter((order) => !order.disabled);

    const { selectedResources, allResourcesSelected, handleSelectionChange } = useIndexResourceState(selectableOrders);

    const rowMarkup = orders.map(({ id, date, paymentStatus, amount }, index) => (
        <IndexTable.Row id={id} key={id} selected={selectedResources.includes(id)} position={index}>
            <IndexTable.Cell>
                <Text as="span">{date}</Text>
            </IndexTable.Cell>
            <IndexTable.Cell>{paymentStatus}</IndexTable.Cell>
            <IndexTable.Cell>
                <Text as="span" alignment="end" numeric>
                    {amount}
                </Text>
            </IndexTable.Cell>
        </IndexTable.Row>
    ));

    return (
        <Card padding="0">
            <BlockStack gap="400">
                <Box padding="400" paddingBlockEnd="0" paddingInlineEnd="300">
                    <InlineStack gap="400" align="space-between" blockAlign="start">
                        <Text as="h2" variant="headingSm">
                            Billing history
                        </Text>
                        <Button>Export bills</Button>
                    </InlineStack>
                </Box>
                <div className="bill-table">
                    <IndexTable
                        resourceName={resourceName}
                        itemCount={selectableOrders.length}
                        selectedItemsCount={allResourcesSelected ? 'All' : selectedResources.length}
                        onSelectionChange={handleSelectionChange}
                        selectable={false}
                        headings={[
                            { title: 'Date' },
                            { title: 'Payment Status' },
                            { title: 'Amount', alignment: 'end' },
                        ]}
                    >
                        {rowMarkup}
                    </IndexTable>

                    <div className="bill-table__pagination">
                        <Pagination hasNext={false} hasPrevious={false} onNext={() => {}} onPrevious={() => {}} />
                    </div>
                </div>
            </BlockStack>
        </Card>
    );
};
