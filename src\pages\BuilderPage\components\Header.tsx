import { FC, useCallback, useEffect, useState } from 'react';
import { Icon, InlineStack, Box, ButtonGroup, Tooltip } from '@shopify/polaris';
import { Button, Popover, TextField, Text } from '@shopify/polaris';
import { DesktopIcon, MobileIcon, UndoIcon, RedoIcon, ViewIcon, SkeletonIcon } from '@shopify/polaris-icons';
import { Auto_BlockDeviceType, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useAppStore } from '@/stores/appStore';
import { PageManager } from '@/pages/BuilderPage/components/PageManager';
import { DEVICE_WIDTHS } from '@/stores/appStore/editor/editorSlice';
import './style.scss';
import { useAppBridge } from '@shopify/app-bridge-react';

export const Header: FC = () => {
    const [screenPopoverActive, setScreenPopoverActive] = useState(false);
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const screenWidth = useAppStore((state) => state.screenWidth);
    const page = useAppStore((state) => state.page);
    const pageId = useAppStore((state) => state.pageId);

    const setCurrentDevice = useBuilderStore((state) => state.setCurrentDevice);
    const setScreenWidth = useAppStore((state) => state.setScreenWidth);
    const resetZoom = useAppStore((state) => state.resetZoom);
    const setDeviceType = useAppStore((state) => state.setDeviceType);
    const previewPage = useAppStore((state) => state.previewPage);
    const shopify = useAppBridge();

    const MIN_WIDTH = DEVICE_WIDTHS[currentDevice];
    const STEP_WIDTH = 10;

    const activatorScreen = (
        <Tooltip content="Configure canvas size" dismissOnMouseOut>
            <Button onClick={() => setScreenPopoverActive(!screenPopoverActive)}>{screenWidth.toString()}px</Button>
        </Tooltip>
    );

    const handleSetCurrentDevice = useCallback(
        (device: Auto_BlockDeviceType) => {
            setCurrentDevice(device);
            setDeviceType(device);
            document.body.setAttribute('data-device', device);
        },
        [setCurrentDevice, setDeviceType],
    );

    const handleChangeScreenWidthToZoom = (value: string) => {
        setScreenWidth(Number(value));
    };

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (
                (e.target as HTMLElement)?.tagName === 'INPUT' ||
                (e.target as HTMLElement)?.tagName === 'TEXTAREA' ||
                (e.target as HTMLElement)?.isContentEditable
            ) {
                return;
            }
            if ((e.key === '+' || e.key === '=') && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                setScreenWidth(screenWidth - STEP_WIDTH);
            } else if ((e.key === '-' || e.key === '_') && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                setScreenWidth(screenWidth + STEP_WIDTH);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [setScreenWidth, screenWidth]);

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
                return;
            }
            if (screenWidth < MIN_WIDTH) {
                resetZoom();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [resetZoom, screenWidth, MIN_WIDTH]);

    const handlePreview = useCallback(() => {
        if (pageId) {
            previewPage(pageId).then(() => {
                window.open(`https://${shopify.config.shop}/apps/preview?id=${pageId}`, '_blank');
            });
        }
    }, [pageId, previewPage, shopify]);

    return (
        <Box
            background="bg-surface"
            borderColor="border"
            borderStyle="solid"
            borderBlockEndWidth="025"
            position="relative"
            zIndex="4"
        >
            <div className="builder-container__header">
                <Box minWidth="0">
                    <InlineStack blockAlign="center" wrap={false} gap="200" direction="row">
                        <div className="builder-container__header-title">
                            <Icon source={SkeletonIcon} tone="base" />
                            <Text tone="subdued" as="p">
                                {page?.status}
                            </Text>
                        </div>
                        <Text as="p">{page?.title}</Text>
                    </InlineStack>
                </Box>
                <Box>
                    <PageManager />
                </Box>
                <Box>
                    <InlineStack gap="200" blockAlign="center" align="center">
                        <Button
                            variant={currentDevice === 'desktop' ? undefined : 'tertiary'}
                            onClick={() => handleSetCurrentDevice('desktop')}
                            icon={DesktopIcon}
                            id="desktop-button"
                        />
                        <Button
                            variant={currentDevice === 'mobile' ? undefined : 'tertiary'}
                            onClick={() => handleSetCurrentDevice('mobile')}
                            icon={MobileIcon}
                            id="mobile-button"
                        />
                        <Popover
                            activator={activatorScreen}
                            active={screenPopoverActive}
                            onClose={() => setScreenPopoverActive(!screenPopoverActive)}
                            preferredAlignment="right"
                        >
                            <Box padding="200" id="screen-width-input">
                                <Popover.Pane fixed>
                                    <Popover.Section>
                                        <TextField
                                            label="Screen width"
                                            value={screenWidth.toString()}
                                            onChange={(value) => handleChangeScreenWidthToZoom(value)}
                                            autoComplete="off"
                                            type="number"
                                            suffix={<Text as="p">px</Text>}
                                            error={
                                                screenWidth < MIN_WIDTH
                                                    ? `Screen width must be greater than ${MIN_WIDTH}px`
                                                    : false
                                            }
                                        />
                                    </Popover.Section>
                                </Popover.Pane>
                            </Box>
                        </Popover>

                        <Box borderInlineStartWidth="025" borderColor="border" minHeight="24px"></Box>
                        <ButtonGroup>
                            <Button disabled variant="tertiary" icon={UndoIcon} />
                            <Button disabled variant="tertiary" icon={RedoIcon} />
                        </ButtonGroup>
                        <Button
                            icon={ViewIcon}
                            onClick={() => {
                                handlePreview();
                            }}
                        >
                            Preview
                        </Button>
                    </InlineStack>
                </Box>
            </div>
        </Box>
    );
};
