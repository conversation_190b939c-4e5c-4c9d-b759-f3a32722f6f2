import { CSSProperties, FC, PropsWithChildren } from 'react';
import clsx from 'clsx';
import {
    DATA_SET_AUTO_ID,
    DATA_SET_CNAME,
    DATA_SET_DRAG,
    AUTO_TOOLBAR_SET_DATA_BP_CONFIGS,
    AUTO_TOOLBAR_SET_DATA_OVERLAY_CONFIGS,
    AUTO_TOOLBAR_SET_DATA_BLOCK_DATA,
    Auto_BlockToolbar,
} from '@giaminhautoketing/auto-builder';
import './styles.scss';

interface BlockToolbarProps extends PropsWithChildren, Auto_BlockToolbar {
    style?: CSSProperties;
    className?: string;
}

export const BlockToolbar: FC<BlockToolbarProps> = ({
    children,
    id,
    cname,
    label,
    type,
    bpConfigs,
    configs,
    overlay,
    style,
    className,
}) => {
    return (
        <div
            className={clsx('auto-block-toolbar', className)}
            {...{ [DATA_SET_DRAG]: 'toolbar' }}
            {...{ [DATA_SET_AUTO_ID]: `toolbar-${id}` }}
            {...{ [DATA_SET_CNAME]: cname }}
            {...{ [AUTO_TOOLBAR_SET_DATA_OVERLAY_CONFIGS]: JSON.stringify(overlay) }}
            {...{ [AUTO_TOOLBAR_SET_DATA_BP_CONFIGS]: JSON.stringify(bpConfigs) }}
            {...{
                [AUTO_TOOLBAR_SET_DATA_BLOCK_DATA]: JSON.stringify({ cname, label, type, bpConfigs, configs }),
            }}
            style={style}
        >
            {children}
        </div>
    );
};
