import { FC } from 'react';
import { css } from '@emotion/react';
import { Button, ButtonProps } from '@shopify/polaris';
import { usePaginationContext } from '../Pagination.context';

interface PaginationControlProps extends ButtonProps {
    className?: string;
}

export const PaginationControl: FC<PaginationControlProps> = ({ disabled, children, size, className, ...others }) => {
    const ctx = usePaginationContext();
    const _size = size || ctx.size;
    const _disabled = disabled || ctx.disabled;
    return (
        <div
            css={css`
                display: flex;
                align-items: center;
                justify-content: center;
            `}
            className={className}
        >
            <Button size={_size} disabled={_disabled} {...others}>
                {children}
            </Button>
        </div>
    );
};
