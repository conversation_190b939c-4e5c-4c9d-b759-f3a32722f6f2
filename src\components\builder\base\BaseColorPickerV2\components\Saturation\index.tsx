import { FC, useMemo, ForwardRefRenderFunction, forwardRef, HTMLAttributes } from 'react';
import { HsvaColor, hsvaToHslaString } from '@uiw/color-convert';
import { Pointer, PointerProps } from './Pointer';
import { Interactive, Interaction } from '../Interactive';

export interface SaturationProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> {
    hsva?: HsvaColor;
    hue?: number;
    pointer?: FC<PointerProps>;
    onChange?: (newColor: HsvaColor) => void;
}

const SaturationFR: ForwardRefRenderFunction<HTMLDivElement, SaturationProps> = (
    { pointer, hue = 0, hsva, onChange, ...otherProps },
    ref,
) => {
    const handleChange = (interaction: Interaction) => {
        if (onChange && hsva) {
            onChange({
                h: hsva.h,
                s: interaction.left * 100,
                v: (1 - interaction.top) * 100,
                a: hsva.a,
            });
        }
    };

    const pointerElement = useMemo(() => {
        if (!hsva) return null;
        const comProps = {
            top: `${100 - hsva.v}%`,
            left: `${hsva.s}%`,
            color: hsvaToHslaString(hsva),
        };
        if (pointer && typeof pointer === 'function') {
            return pointer({ ...comProps });
        }
        return <Pointer {...comProps} />;
    }, [hsva, pointer]);

    return (
        <Interactive
            ref={ref}
            css={{
                position: 'relative',
                width: '100%',
                height: '165px',
                borderRadius: '8px',
                inset: '0',
                cursor: 'crosshair',
            }}
            style={{
                backgroundImage: `linear-gradient(0deg, #000, transparent), linear-gradient(90deg, #fff, hsl(${
                    hsva?.h ?? hue
                }, 100%, 50%))`,
            }}
            onMove={handleChange}
            onDown={handleChange}
            {...otherProps}
        >
            {pointerElement as React.ReactNode}
        </Interactive>
    );
};

export const Saturation = forwardRef(SaturationFR);
