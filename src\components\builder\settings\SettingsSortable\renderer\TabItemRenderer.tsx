import { useState, useRef, useEffect } from 'react';
import { ActionList, Text } from '@shopify/polaris';
import {
    DragHandleIcon,
    SettingsIcon,
    CheckIcon,
    DuplicateIcon,
    EditIcon,
    DeleteIcon,
    CheckCircleIcon,
} from '@shopify/polaris-icons';
import {
    useFloating,
    flip,
    offset,
    shift,
    useClick,
    useDismiss,
    useInteractions,
    FloatingPortal,
} from '@floating-ui/react';
import { UniqueIdentifier } from '@dnd-kit/core';
import { genRandomBlockId, useBlockStore } from '@giaminhautoketing/auto-builder';
import { useClickOutside, useValidatedInput } from '@/hooks';
import { SortableItemRequireProps, RendererProps } from '@/components/Sortable';
import { useAppStore } from '@/stores/appStore/useAppStore';

export interface TabItem {
    id: UniqueIdentifier;
    name: string;
}

export function TabItemRenderer<T extends SortableItemRequireProps>({
    blockId,
    index,
    clone,
    data,
    isDragging,
    onRemove,
    insertPosition,
}: RendererProps<T> & {
    blockId: string;
}) {
    const inputRef = useRef<HTMLInputElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isEditing, setIsEditing] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const isGhost = !clone && isDragging;
    const id = data.id.toString();

    const tempTab = useAppStore((state) => state.tempTab);
    const configs = useBlockStore((state) => state.blocks[blockId].configs);
    const setTempTab = useAppStore((state) => state.setTempTab);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const items = configs.items as TabItem[];
    const defaultId = configs.defaultId as string;
    const activeId = tempTab[blockId]?.activeId;

    const onDuplicate = async (id: UniqueIdentifier) => {
        const itemToDuplicate = items.find((item) => item.id === id);
        if (!itemToDuplicate) return;

        const duplicatedItem = {
            ...itemToDuplicate,
            id: genRandomBlockId(),
            name: `${itemToDuplicate.name} Copy`,
        };
        const updatedItems = [...items, duplicatedItem];
        await updateBlockConfigsProperty(blockId, 'items', updatedItems as unknown as Record<string, unknown>);
        setTempTab(blockId, duplicatedItem.id);
        setIsOpen(false);
    };

    const onSetDefault = () => {
        updateBlockConfigsProperty(blockId, 'defaultId', id);
        setIsOpen(false);
    };

    const onRename = (newName: string) => {
        updateBlockConfigsProperty(blockId, `items.${index}.name`, newName);
        setIsEditing(false);
        setIsOpen(false);
    };

    const onDelete = async () => {
        await onRemove?.();

        if (defaultId === id) {
            await updateBlockConfigsProperty(blockId, 'defaultId', items[0].id);
        }
        if (activeId === id) {
            const ids = items.map((item) => item.id);
            const nextId = ids[defaultId === id ? 0 : index === 0 ? index + 1 : index - 1];
            setTempTab(blockId, nextId.toString());
        }
        setIsOpen(false);
    };

    const { value, acceptedValue, onChange, onBlur, onKeyDown } = useValidatedInput<string>({
        initialValue: data.name,
        validator: (value, acceptedValue) => {
            return value.trim() !== '' && value !== acceptedValue;
        },
        onApply: onRename,
    });

    const isDisabled = value.trim() === '' || value === acceptedValue;
    const isActive = activeId === id;

    const { refs, floatingStyles, context } = useFloating({
        open: isOpen,
        onOpenChange: setIsOpen,
        placement: 'right-start',
        middleware: [
            flip(),
            shift(),
            offset({
                mainAxis: 16,
                crossAxis: -6,
            }),
        ],
    });
    const click = useClick(context);
    const dismiss = useDismiss(context);
    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss]);

    useClickOutside(
        inputRef,
        () => {
            onBlur();
            setIsEditing(false);
        },
        [buttonRef],
    );

    useEffect(() => {
        if (isEditing) {
            inputRef.current?.focus();
            inputRef.current?.select();
        }
    }, [isEditing]);

    return (
        <div className="img-sortable-item">
            <div
                css={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                    height: '32px',
                    backgroundColor: '#ffffff',
                    borderWidth: '1px',
                    borderColor: isActive && !isGhost ? '#005BD3' : 'rgba(138, 138, 138, 1)',
                    borderStyle: isGhost ? 'dashed' : 'solid',
                    borderRadius: '8px',
                    padding: '0 6px',
                    cursor: clone ? 'grabbing' : 'grab',
                    tabIndex: 0,
                    ':before': {
                        content: '""',
                        position: 'absolute',
                        inset: '-2px',
                        borderRadius: '8px',
                        boxShadow: '0px 0px 0px 2px #005BD3',
                        pointerEvents: 'none',
                        opacity: isEditing ? 1 : 0,
                    },
                    ':hover': {
                        '& .img-sortable-item-settings-icon': {
                            opacity: 1,
                        },
                    },
                }}
                onClick={() => {
                    setTempTab(blockId, id);
                }}
                onDoubleClick={() => {
                    setIsEditing(!isEditing);
                }}
            >
                {insertPosition && (
                    <div
                        css={{
                            position: 'absolute',
                            height: '2px',
                            left: '0',
                            right: '0',
                            [insertPosition === 'before' ? 'top' : 'bottom']: '-8px',
                            backgroundColor: '#005BD3',
                        }}
                    />
                )}
                {isGhost ? null : isEditing ? (
                    <>
                        <input
                            ref={inputRef}
                            type="text"
                            css={{
                                flexGrow: 1,
                                height: '100%',
                                paddingInlineStart: '24px',
                                marginRight: '12px',
                                outline: 'none',
                                border: 'none',
                            }}
                            value={value}
                            onChange={(e) => onChange(e.target.value)}
                            onBlur={onBlur}
                            onKeyDown={(e) => onKeyDown(e, () => setIsEditing(false))}
                        />
                        <button
                            ref={buttonRef}
                            css={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '46px',
                                height: '18px',
                                borderRadius: '4px',
                                color: '#ffffff',
                                background: 'rgba(0, 91, 211, 1)',
                                fontSize: '12px',
                                fontWeight: '450',
                                lineHeight: '16px',
                                outline: 'none',
                                border: 'none',
                                opacity: isDisabled ? 0.5 : 1,
                                cursor: 'pointer',
                            }}
                            disabled={isDisabled}
                            onClick={() => onRename(value)}
                            onDoubleClick={(e) => e.stopPropagation()}
                        >
                            Done
                        </button>
                    </>
                ) : (
                    <>
                        <div
                            css={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                userSelect: 'none',
                            }}
                        >
                            <div css={{ width: '20px', height: '20px' }}>
                                <DragHandleIcon />
                            </div>
                            <Text as="p" variant="bodyMd">
                                {value}
                            </Text>
                        </div>
                        <div css={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                            {defaultId === id && (
                                <div css={{ width: '20px', height: '20px' }}>
                                    <CheckIcon fill="rgba(0, 91, 211, 1)" />
                                </div>
                            )}
                            <div
                                ref={refs.setReference}
                                {...getReferenceProps({
                                    onClick: (e) => e.stopPropagation(),
                                    onDoubleClick: (e) => e.stopPropagation(),
                                })}
                                className="img-sortable-item-settings-icon"
                                css={{ width: '20px', height: '20px', opacity: isOpen ? 1 : 0, cursor: 'pointer' }}
                            >
                                <SettingsIcon />
                            </div>
                            {isOpen && (
                                <FloatingPortal>
                                    <div
                                        ref={refs.setFloating}
                                        style={floatingStyles}
                                        css={{
                                            width: '267px',
                                            background: '#fff',
                                            borderRadius: '12px',
                                            boxShadow:
                                                '0px 4px 6px -2px rgba(26, 26, 26, 0.2),0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset,0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset,-1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset',
                                            zIndex: 99,
                                        }}
                                        {...getFloatingProps({
                                            onPointerDown: (e) => e.stopPropagation(),
                                            onDoubleClick: (e) => e.stopPropagation(),
                                        })}
                                    >
                                        <ActionList
                                            actionRole="menuitem"
                                            items={[
                                                {
                                                    content: 'Duplicate',
                                                    icon: DuplicateIcon,
                                                    onAction: () => onDuplicate(id),
                                                },
                                                {
                                                    content: 'Set as default',
                                                    icon: CheckCircleIcon,
                                                    onAction: onSetDefault,
                                                },
                                                {
                                                    content: 'Rename',
                                                    icon: EditIcon,
                                                    onAction: () => setIsEditing(true),
                                                },
                                                {
                                                    content: 'Delete',
                                                    icon: DeleteIcon,
                                                    onAction: onDelete,
                                                    disabled: items.length === 1,
                                                },
                                            ]}
                                        />
                                    </div>
                                </FloatingPortal>
                            )}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
