import { forwardRef, HTMLAttributes } from 'react';
import { mergeProps } from '@zag-js/react';
import type { ChannelProps } from '@zag-js/color-picker';
import { useColorPickerContext } from './use-color-picker-context.ts';
import { createSplitProps } from './types';

export type ColorPickerChannelSliderProps = ChannelProps & HTMLAttributes<HTMLDivElement>;

export const ColorPickerChannelSlider = forwardRef<HTMLDivElement, ColorPickerChannelSliderProps>((props, ref) => {
    const [channelProps, localProps] = createSplitProps<ChannelProps>()(props, ['channel', 'orientation']);

    const colorPicker = useColorPickerContext();

    const mergedProps = mergeProps(colorPicker.getChannelSliderProps(channelProps), localProps);

    return (
        <div {...mergedProps} ref={ref}>
            <div {...colorPicker.getTransparencyGridProps()} />
            <div {...colorPicker.getChannelSliderTrackProps(channelProps)} />
            <div {...colorPicker.getChannelSliderThumbProps(channelProps)} />
        </div>
    );
});

ColorPickerChannelSlider.displayName = 'ColorPickerChannelSlider';
