import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
import svgr from 'vite-plugin-svgr';
import fs from 'fs';

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        react({
            jsxImportSource: '@emotion/react',
            plugins: [['@swc/plugin-emotion', {}]],
        }),
        svgr({
            svgrOptions: {
                exportType: 'named',
                ref: true,
                svgo: false,
                titleProp: true,
            },
            include: '**/*.svg',
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    server: {
        https: {
            key: fs.readFileSync('./autoketing.io-key.pem'),
            cert: fs.readFileSync('./autoketing.io.pem'),
        },
        host: 'autoketing.io',
        port: 443,
        proxy: {
            // "^/(\\?.*)?$": proxyOptions,
            '^/api': {
                target: 'https://dev.autoketing.io',
                changeOrigin: false,
                secure: true,
                ws: false,
            },
        },
        allowedHosts: true,
    },
});
