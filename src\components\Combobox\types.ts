import { Dispatch, KeyboardEvent, ReactNode, RefObject, SetStateAction } from 'react';

export interface ComboboxOption {
    value: string;
    label: string;
    disabled?: boolean;
}

export type ComboboxRootProps = {
    id?: string;
    children: ReactNode;
};

export type ComboboxTriggerProps = {
    label?: string;
    placeholder?: string;
    value: string;
    disabled?: boolean;
    error?: string;
    helpText?: ReactNode;
    loading?: boolean;
    onlyNumber?: boolean;
    showSearchIcon?: boolean;
    showArrowIcon?: boolean;
    onChange: (value: string) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    onSearch?: (value: string) => void;
    onKeyDown?: (e: KeyboardEvent<HTMLInputElement>, setIsOpen: (isOpen: boolean) => void) => void;
};

export type ComboboxContentProps = {
    loading?: boolean;
    maxHeight?: string | number;
    hideEmptyState?: boolean;
    children: ReactNode;
};

export type ComboboxOptionsProps = {
    options: ComboboxOption[];
    onSelect: (value: string) => void;
    selectedValue?: string;
    emptyState?: ReactNode;
    hideEmptyState?: boolean;
};

export interface ComboboxContextType {
    uniqueId: string;
    optionsRef: RefObject<HTMLDivElement | null>;
    triggerRef: RefObject<HTMLDivElement | null>;
    listRef: RefObject<HTMLUListElement | null>;
    isOpen: boolean;
    selectedValue: string;
    highlightedIndex: number;
    setIsOpen: (isOpen: boolean) => void;
    setSelectedValue: (value: string) => void;
    setHighlightedIndex: Dispatch<SetStateAction<number>>;
}
