/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import {
    DATA_SET_FORM_ELEMENT,
    DATA_SET_FORM_INPUT,
    DATA_SET_FORM_INPUT_PLACEHOLDER,
    DATA_SET_FORM_INPUT_REQUIRED,
    DATA_SET_FORM_INPUT_PATTERN,
    DATA_SET_FORM_INPUT_MAX_LENGTH,
    DATA_SET_FORM_INPUT_READ_ONLY,
    DATA_SET_FORM_FIELD_ID,
} from '@/components/builder/blocks/form/constants';

interface FormInputProps {
    field: any;
    index: number;
    autoId: string;
}

export const FormInput: FC<FormInputProps> = ({ field, index, autoId }) => (
    <input
        type={field.type}
        {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
        {...{ [`${DATA_SET_FORM_ELEMENT}-${index}`]: autoId }}
        {...{ [`${DATA_SET_FORM_INPUT}`]: autoId }}
        {...{ [`${DATA_SET_FORM_INPUT_PLACEHOLDER}`]: field.placeholder }}
        {...{ [`${DATA_SET_FORM_INPUT_REQUIRED}`]: field.validations?.required }}
        {...{ [`${DATA_SET_FORM_INPUT_PATTERN}`]: field.pattern?.pattern }}
        {...{ [`${DATA_SET_FORM_INPUT_MAX_LENGTH}`]: field.characterLimit?.charLimit }}
        {...{ [`${DATA_SET_FORM_INPUT_READ_ONLY}`]: field.validations?.readOnly }}
        placeholder={field.placeholder}
        value={field.initialText}
        readOnly
    />
);
