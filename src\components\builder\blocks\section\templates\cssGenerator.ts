/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { getBlockClassName } from '@/utils/cssBlockGenerator';

export const generateCss = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile'): string => {
    if (!block.bpConfigs?.[device]) return '';

    const config = block.bpConfigs[device];
    const className = getBlockClassName(blockId, 'section');

    let css = '';

    // Section container styles
    css += `.${className} {
      display: block;
      position: ${config.position};
      padding: ${config.pt?.val}${config.pt?.unit} ${config.pr?.val}${config.pr?.unit} ${config.pb?.val}${config.pb?.unit} ${config.pl?.val}${config.pl?.unit};
    }\n`;

    // Section inner styles
    css += `.${className} .atk-section-inner {
        display: ${config.innerDisplay};
        grid-template-columns: ${
            config.gridTemplateColumns?.map((col: any) => `${col.val}${col.unit}`).join(' ') || '1fr'
        };
        grid-template-rows: auto;
        min-height: ${config.minHeight?.val}${config.minHeight?.unit};
        max-width: ${config.maxWidth?.val}${config.maxWidth?.unit};
        padding: ${config.pr?.val}${config.pr?.unit} ${config.pl?.val}${config.pl?.unit} ${config.pb?.val}${config.pb?.unit } ${config.pt?.val}${config.pt?.unit}; 
        margin: 0 auto;
    }\n`;

    return css;
};
