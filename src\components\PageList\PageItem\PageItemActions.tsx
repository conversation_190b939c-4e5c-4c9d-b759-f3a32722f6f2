import { FC } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ButtonGroup, Popover, Tooltip } from '@shopify/polaris';
import {
    DeleteIcon,
    DuplicateIcon,
    EditIcon,
    ExportIcon,
    MenuHorizontalIcon,
    PageRemoveIcon,
    PageUpIcon,
    ChartVerticalFilledIcon,
    UndoIcon,
} from '@shopify/polaris-icons';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { handleApiCall } from '../../PageList/configs';

interface PageItemActionsProps {
    status: string;
    popoverActive: boolean;
    setPopoverActive: (active: boolean) => void;
    handleActionPopover: (kind: string) => void;
    isHomePage: boolean;
    id: number;
}

export const PageItemActions: FC<PageItemActionsProps> = ({
    status,
    popoverActive,
    setPopoverActive,
    handleActionPopover,
    isHomePage,
    id,
}) => {
    const currentParams = useAppStore((state) => state.currentParams);
    const updatePageStatus = useAppStore((state) => state.updatePageStatus);
    const restorePage = useAppStore((state) => state.restorePage);

    const handlePublish = async () => {
        await handleApiCall(
            () => updatePageStatus(id, 'published', currentParams),
            'Page published successfully',
            'Failed to publish page',
            () => setPopoverActive(false),
        );
    };

    const handleUnpublish = async () => {
        await handleApiCall(
            () => updatePageStatus(id, 'draft', currentParams),
            'Page unpublished successfully',
            'Failed to unpublish page',
            () => setPopoverActive(false),
        );
    };

    const handleRestore = async () => {
        await handleApiCall(
            () => restorePage(id, currentParams),
            'Page restored successfully',
            'Failed to restore page',
            () => setPopoverActive(false),
        );
    };

    return (
        <div className={`page-item__more-action${popoverActive ? ' page-item__more-action--active' : ''}`}>
            <ButtonGroup>
                <Button>Preview</Button>
                {status === 'default' ? (
                    <Tooltip content="Restore">
                        <Button icon={UndoIcon} onClick={handleRestore}></Button>
                    </Tooltip>
                ) : (
                    <ButtonGroup>
                        {!isHomePage && (
                            <Tooltip content="Analytics">
                                <Button icon={ChartVerticalFilledIcon}></Button>
                            </Tooltip>
                        )}
                        <Popover
                            active={popoverActive}
                            activator={
                                <div
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                >
                                    <Button
                                        icon={MenuHorizontalIcon}
                                        onClick={() => {
                                            setPopoverActive(!popoverActive);
                                        }}
                                    />
                                </div>
                            }
                            onClose={() => setPopoverActive(false)}
                            preferredAlignment="right"
                            captureOverscroll={false}
                        >
                            <div className="page-item__more-action-panel">
                                <Popover.Pane>
                                    <ActionList
                                        actionRole="menuitem"
                                        items={[
                                            ...(currentParams.status !== 3
                                                ? [
                                                      {
                                                          content: 'Rename',
                                                          icon: EditIcon,
                                                          onAction: () => handleActionPopover('rename'),
                                                      },
                                                      {
                                                          content: 'Duplicate',
                                                          icon: DuplicateIcon,
                                                          onAction: () => handleActionPopover('duplicate'),
                                                      },
                                                      {
                                                          content: 'Publish',
                                                          icon: PageUpIcon,
                                                          disabled: status === 'published',
                                                          onAction: handlePublish,
                                                      },
                                                      {
                                                          content: 'Unpublish',
                                                          icon: PageRemoveIcon,
                                                          disabled: status !== 'published',
                                                          onAction: handleUnpublish,
                                                      },
                                                      {
                                                          content: 'Export',
                                                          icon: ExportIcon,
                                                          onAction: () => handleActionPopover('export'),
                                                      },
                                                  ]
                                                : []),
                                            {
                                                content: 'Delete',
                                                icon: DeleteIcon,
                                                destructive: true,
                                                onAction: () => handleActionPopover('delete'),
                                            },
                                        ]}
                                    />
                                </Popover.Pane>
                            </div>
                        </Popover>
                    </ButtonGroup>
                )}
            </ButtonGroup>
        </div>
    );
};
