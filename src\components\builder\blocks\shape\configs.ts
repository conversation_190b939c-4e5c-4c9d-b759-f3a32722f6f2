import { Auto_BlockToolbar, Auto_BlockType, genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const shapeToolbarOptions: Auto_BlockToolbar[] = [
    {
        id: genRandomBlockId(),
        cname: 'shape',
        label: 'Shape',
        type: 'shape' as Auto_BlockType,
        configs: {
            content: {
                type: 'svg',
                svg: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="36" height="36" fill="currentColor"/>
            </svg>
            `,
            },

            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            syncOnDesktop: false,
            syncOnMobile: false,
            customCSS: {
                classNames: '',
                style: '',
            },
            events: {},
        },
        bpConfigs: {
            desktop: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'scroll',
                        fill: 'cover',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
                border: {
                    radius: {
                        'top-left': { val: '0', unit: 'px' },
                        'top-right': { val: '0', unit: 'px' },
                        'bottom-right': { val: '0', unit: 'px' },
                        'bottom-left': { val: '0', unit: 'px' },
                    },
                    color: '#000000',
                    top: { val: '0', unit: 'px' },
                    right: { val: '0', unit: 'px' },
                    bottom: { val: '0', unit: 'px' },
                    left: { val: '0', unit: 'px' },
                    type: 'default',
                },
            },
            mobile: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: '',
                        position: '',
                        attachment: '',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: 50,
                height: 50,
            },
            mobile: {
                width: 50,
                height: 50,
            },
        },
    },
    {
        id: genRandomBlockId(),
        cname: 'shape',
        label: 'Shape',
        type: 'shape' as Auto_BlockType,
        configs: {
            content: {
                type: 'svg',
                svg: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="36" height="36" rx="18" fill="#8A8A8A"/>
                    </svg>

            `,
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            syncOnDesktop: false,
            syncOnMobile: false,
            customCSS: {
                classNames: '',
                style: '',
            },
            events: {},
        },
        bpConfigs: {
            desktop: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'scroll',
                        fill: 'cover',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
                border: {
                    radius: {
                        'top-left': { val: '50', unit: 'px' },
                        'top-right': { val: '50', unit: 'px' },
                        'bottom-right': { val: '50', unit: 'px' },
                        'bottom-left': { val: '50', unit: 'px' },
                    },
                    color: '#000000',
                    top: { val: '0', unit: 'px' },
                    right: { val: '0', unit: 'px' },
                    bottom: { val: '0', unit: 'px' },
                    left: { val: '0', unit: 'px' },
                    type: 'default',
                },
            },
            mobile: {
                background: {
                    type: 'color',
                    color: '#8A8A8A',
                    image: {
                        url: '',
                        repeat: '',
                        position: '',
                        attachment: '',
                    },
                },
                width: { val: '50', unit: 'px' },
                height: { val: '50', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: 50,
                height: 50,
            },
            mobile: {
                width: 50,
                height: 50,
            },
        },
    },
];
