import { FC } from 'react';
import { Grid, Text } from '@shopify/polaris';
import { Auto_BlockType } from '@giaminhautoketing/auto-builder';
import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { shapeToolbarOptions } from '../configs';

export const ShapePreview: FC = () => {
    return (
        <Grid>
            {shapeToolbarOptions.map((option) => {
                return (
                    <Grid.Cell key={option.id} columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
                        <div
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '0.5rem',
                                alignItems: 'center',
                                flex: 1,
                            }}
                        >
                            <BlockToolbar
                                id={option.id}
                                cname={option.cname}
                                label={option.label}
                                type={option.type as Auto_BlockType}
                                bpConfigs={option.bpConfigs}
                                overlay={option.overlay}
                                configs={option.configs}
                                style={{
                                    width: '100%',
                                    height: '70px',
                                    background: '#FAFBFB',
                                    borderRadius: '8px',
                                    border: '1px solid #e5e5e5',
                                    cursor: 'move',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                }}
                            >
                                <div
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        margin: '1rem',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        background: '#8a8a8a',
                                        maskImage: `url("data:image/svg+xml,${encodeURIComponent(
                                            (option.configs?.content as { svg: string }).svg,
                                        )}")`,
                                        WebkitMaskImage: `url("data:image/svg+xml,${encodeURIComponent(
                                            (option.configs?.content as { svg: string }).svg,
                                        )}")`,
                                        maskSize: '100% 100%',
                                        WebkitMaskSize: '100% 100%',
                                    }}
                                />
                            </BlockToolbar>
                            <Text variant="bodySm" as="span">
                                {option.label}
                            </Text>
                        </div>
                    </Grid.Cell>
                );
            })}
        </Grid>
    );
};
