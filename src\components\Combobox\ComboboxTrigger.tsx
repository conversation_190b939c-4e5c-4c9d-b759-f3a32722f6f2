import React, { forwardRef, useCallback, KeyboardEvent } from 'react';
import { ComboboxTriggerProps } from './types';
import { useComboboxContext } from './context';

export const ComboboxTrigger = forwardRef<HTMLDivElement, ComboboxTriggerProps>(
    (
        {
            label,
            placeholder,
            value,
            error,
            helpText,
            disabled,
            loading,
            onlyNumber,
            showSearchIcon,
            showArrowIcon,
            onChange,
            onFocus,
            onBlur,
            onSearch,
            onKeyDown,
        },
        ref,
    ) => {
        const { isOpen, setIsOpen, highlightedIndex, setHighlightedIndex, uniqueId, triggerRef, listRef } =
            useComboboxContext();

        const handleFocus = useCallback(() => {
            if (!disabled) {
                setIsOpen(true);
                onFocus?.();
            }
        }, [disabled, onFocus, setIsOpen]);

        const handleBlur = useCallback(() => {
            onBlur?.();
        }, [onBlur]);

        const handleChange = useCallback(
            (e: React.ChangeEvent<HTMLInputElement>) => {
                let newValue = e.target.value;
                if (onlyNumber) {
                    newValue = newValue.replace(/[^0-9]/g, '');
                }
                onChange(newValue);
                onSearch?.(newValue);

                if (!isOpen) {
                    setIsOpen(true);
                }
            },
            [onChange, onSearch, isOpen, setIsOpen, onlyNumber],
        );

        const findNextSelectableIndex = (
            currentIndex: number,
            direction: 'next' | 'prev',
            listItems: NodeListOf<Element>,
        ): number => {
            const itemCount = listItems.length;
            if (itemCount === 0) return -1;

            const step = direction === 'next' ? 1 : -1;

            let nextIndex = currentIndex;

            for (let i = 0; i < itemCount; i++) {
                nextIndex = nextIndex + step;

                if (nextIndex >= itemCount) {
                    nextIndex = 0;
                } else if (nextIndex < 0) {
                    nextIndex = itemCount - 1;
                }

                const isDisabled = listItems[nextIndex].getAttribute('aria-disabled') === 'true';

                if (!isDisabled) {
                    return nextIndex;
                }
            }
            return currentIndex;
        };

        const handleKeyDown = useCallback(
            (e: KeyboardEvent<HTMLInputElement>) => {
                if ((e.key === 'Backspace' || e.key === 'Delete') && !isOpen) {
                    setIsOpen(true);
                    return;
                }

                if (!isOpen) {
                    if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setIsOpen(true);
                    }
                    return;
                }

                onKeyDown?.(e, setIsOpen);

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        setHighlightedIndex((prevIndex: number) => {
                            const listItems = listRef.current?.querySelectorAll('li[role="option"]');
                            if (!listItems || listItems.length === 0) return prevIndex;

                            const newIndex = findNextSelectableIndex(prevIndex, 'next', listItems);

                            if (newIndex !== -1 && listItems[newIndex]) {
                                (listItems[newIndex] as HTMLElement).scrollIntoView({
                                    block: 'nearest',
                                });
                            }

                            return newIndex;
                        });
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        setHighlightedIndex((prevIndex: number) => {
                            const listItems = listRef.current?.querySelectorAll('li[role="option"]');
                            if (!listItems || listItems.length === 0) return prevIndex;

                            const newIndex = findNextSelectableIndex(prevIndex, 'prev', listItems);

                            if (newIndex !== -1 && listItems[newIndex]) {
                                (listItems[newIndex] as HTMLElement).scrollIntoView({
                                    block: 'nearest',
                                });
                            }

                            return newIndex;
                        });
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (highlightedIndex !== -1) {
                            const listItems = listRef.current?.querySelectorAll('li[role="option"]');
                            if (listItems && listItems[highlightedIndex]) {
                                const isDisabled = listItems[highlightedIndex].getAttribute('aria-disabled') === 'true';
                                if (!isDisabled) {
                                    (listItems[highlightedIndex] as HTMLElement).click();
                                }
                            }
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        setIsOpen(false);
                        break;
                    case 'Tab':
                        setIsOpen(false);
                        break;
                }
            },
            [isOpen, setIsOpen, highlightedIndex, setHighlightedIndex, listRef, onKeyDown],
        );

        return (
            <div
                ref={(el) => {
                    triggerRef.current = el;
                    if (ref) {
                        if (typeof ref === 'function') {
                            ref(el);
                        } else if (ref !== null) {
                            ref.current = el;
                        }
                    }
                }}
                data-scope="combobox"
                data-part="trigger"
                data-state={isOpen ? 'open' : 'closed'}
                data-disabled={disabled || undefined}
            >
                {label && (
                    <label htmlFor={`combobox-input-${uniqueId}`} data-scope="combobox" data-part="label">
                        {label}
                    </label>
                )}

                <div data-scope="combobox" data-part="input-wrapper">
                    {showSearchIcon && (
                        <div data-scope="combobox" data-part="search-icon">
                            <svg
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                focusable="false"
                            >
                                <path
                                    d="M10.875 18.75C15.2242 18.75 18.75 15.2242 18.75 10.875C18.75 6.52576 15.2242 3 10.875 3C6.52576 3 3 6.52576 3 10.875C3 15.2242 6.52576 18.75 10.875 18.75Z"
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                ></path>
                                <path
                                    d="M16.4431 16.4438L20.9994 21.0002"
                                    stroke="currentColor"
                                    strokeLinecap="square"
                                    strokeLinejoin="round"
                                ></path>
                            </svg>
                        </div>
                    )}

                    <input
                        id={`combobox-input-${uniqueId}`}
                        type="text"
                        value={value}
                        onChange={handleChange}
                        onFocus={handleFocus}
                        onBlur={handleBlur}
                        onKeyDown={handleKeyDown}
                        placeholder={placeholder}
                        disabled={disabled}
                        data-scope="combobox"
                        data-part="input"
                        data-state={isOpen ? 'open' : 'closed'}
                        data-disabled={disabled || undefined}
                        data-has-icon-search={showSearchIcon || undefined}
                        data-has-icon-arrow={showArrowIcon || undefined}
                        data-has-error={error ? true : undefined}
                        role="combobox"
                        aria-expanded={isOpen}
                        aria-autocomplete="list"
                        aria-controls={`combobox-listbox-${uniqueId}`}
                        aria-haspopup="listbox"
                        autoComplete="off"
                    />

                    {loading && (
                        <div data-scope="combobox" data-part="loading-indicator" aria-hidden="true">
                            <div data-scope="combobox" data-part="spinner"></div>
                        </div>
                    )}

                    {showArrowIcon && !loading && (
                        <button
                            type="button"
                            data-scope="combobox"
                            data-part="arrow"
                            data-state={isOpen ? 'open' : 'closed'}
                            tabIndex={-1}
                            aria-hidden="true"
                            onClick={() => setIsOpen(!isOpen)}
                        >
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M4 6L8 10L12 6"
                                    stroke="currentColor"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </button>
                    )}
                </div>

                {error && (
                    <div data-scope="combobox" data-part="error">
                        {error}
                    </div>
                )}

                {helpText && (
                    <div data-scope="combobox" data-part="help-text">
                        {helpText}
                    </div>
                )}
            </div>
        );
    },
);

ComboboxTrigger.displayName = 'ComboboxTrigger';
