import { FC } from 'react';
import { TextProps } from '@shopify/polaris';
import { Level } from '@tiptap/extension-heading';
import { useTextEditor } from '@/pages/BuilderPage/hooks';
import { SettingsSelect } from '../SettingsSelect';
import { BaseSelect, BaseItemLayout } from '../../base';
import { textFormatOptions } from '../../data/options';

type SettingsSelectProps = Parameters<typeof SettingsSelect>[0];

type SettingsHeadingProps = Omit<SettingsSelectProps, 'options'>;

export const SettingsHeading: FC<SettingsHeadingProps> = ({
    blockId,
    path,
    isUpdateConfigs,
    selectProps,
    label,
    textProps,
    ...otherProps
}) => {
    const { editor } = useTextEditor({ blockId, path, isUpdateConfigs });

    const handleChange = (value: string) => {
        const level = parseInt(value);
        if (level > 0) {
            editor
                ?.chain()
                .focus()
                .toggleHeading({ level: level as Level })
                .run();
        } else {
            editor?.chain().focus().setParagraph().run();
        }
    };

    const isActiveHeading = (value: string) => {
        const level = parseInt(value);
        if (level === 0) {
            return editor?.isActive('paragraph') || false;
        }
        return editor?.isActive('heading', { level }) || false;
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseSelect
                options={textFormatOptions}
                onChange={handleChange}
                valueMap={{
                    'heading-1': '1',
                    'heading-2': '2',
                    'heading-3': '3',
                    'heading-4': '4',
                    'heading-5': '5',
                    'heading-6': '6',
                    paragraph: '0',
                }}
                isActiveCheck={isActiveHeading}
                {...selectProps}
            />
        </BaseItemLayout>
    );
};
