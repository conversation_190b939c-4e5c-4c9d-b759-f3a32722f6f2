import { BlockStack, Box, IconSource } from '@shopify/polaris';
import { FC } from 'react';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import MenuItem from './MenuItem';
import { useAppStore } from '@/stores';
import { FormField } from '@/components/builder/blocks/form/types';
import './SideBarMenu.scss';
interface SideBarItemProps {
    icon: IconSource | React.ReactNode;
    title: string;
    id: string;
}

interface SideBarProps {
    items: SideBarItemProps[];
    activeItem: string | null;
    onSelectItem: (id: string) => void;
}

export const SideBar: FC<SideBarProps> = ({ items, activeItem, onSelectItem }) => {
    const selectBlock = useBuilderStore((state) => state.selectBlock);
    const setSelectedFormField = useAppStore((state) => state.setSelectedFormField);
    const setIsAddNewFormField = useAppStore((state) => state.setIsAddNewFormField);
    const handleSelectItem = (id: string) => {
        selectBlock(null);
        setSelectedFormField(null as unknown as FormField);
        setIsAddNewFormField(false);
        onSelectItem(id);
    };
    return (
        <Box padding="100">
            <BlockStack inlineAlign="center" gap="100">
                {items.map((button) => (
                    <MenuItem
                        key={button.id}
                        icon={button.icon}
                        title={button.title}
                        active={activeItem === button.id}
                        onClick={() => handleSelectItem(button.id)}
                    />
                ))}
            </BlockStack>
        </Box>
    );
};
