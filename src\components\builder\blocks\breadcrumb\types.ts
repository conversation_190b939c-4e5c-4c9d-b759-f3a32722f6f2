import { Auto_BlockViewer } from '@giaminhautoketing/auto-builder';
import { UnitValue } from '../button/types';

export interface BreadcrumbItem {
    label: string;
    url: string;
    isEllipsis?: boolean;
}

export interface SeparatorIconConfig {
    icon: string;
    color: string;
    iconSize: { val: string; unit: string };
}

export interface BreadcrumbContent {
    items?: BreadcrumbItem[];
    showHome?: boolean;
    showSeparator?: boolean;
    showLastItem?: boolean;
    isCollapse?: boolean;
    separatorIcon?: SeparatorIconConfig;
    beforeEllipsis?: { val: number };
    afterEllipsis?: { val: number };
    hideWhenNotParent?: boolean;
}

export interface BreadcrumbCustomCSS {
    enable?: boolean;
    className?: string;
    style?: string;
}

export interface BreadcrumbSpacing {
    betweenTextAndIcon?: string;
    betweenItems?: string;
}

export interface BreadcrumbConfigs extends Record<string, unknown> {
    content?: BreadcrumbContent;
    customCSS?: BreadcrumbCustomCSS;
    spacing?: BreadcrumbSpacing;
    displayOnDesktop?: boolean;
    displayOnMobile?: boolean;
    animation?: {
        type: string;
        duration: UnitValue;
        loop: string;
        delay: UnitValue;
    };
    }

export interface BreadcrumbViewerProps extends Omit<Auto_BlockViewer, 'configs'> {
    configs: BreadcrumbConfigs;
    isUpdateConfigs: boolean;
}

export interface BreadcrumbViewerContentProps {
    autoId: string;
    configs: BreadcrumbConfigs;
    isUpdateConfigs: boolean;
    separatorIcon?: SeparatorIconConfig;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    bpConfigs: any;
}

export interface BreadcrumbItemProps {
    item: BreadcrumbItem;
    showSeparator?: boolean;
    isLast: boolean;
    hasHiddenItems?: boolean;
    isFirst: boolean;   
    separatorIcon?: SeparatorIconConfig;
    onEllipsisClick?: () => void;
    hiddenItemsTooltip?: string;
    autoId: string;
}