export interface Section {
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    status: string;
    // createdAt: string;
    updated_at: string;
}

export interface TemplateSection {
    id: string;
    title: string;
    thumbnail: string;
    status: string;
    categoryTitle: string;
}

export interface SectionsResponse {
    result: {
        data: {
            list: Section[];
            total: number;
        };
    };
}

export interface SectionCategoryResponse {
    result: {
        data: Array<{
            id: string;
            title: string;
            value: string;
        }>;
    };
}

export interface TemplateSectionResponse {
    result: {
        data: {
            list: TemplateSection[];
            total: number;
        };
    };
}
