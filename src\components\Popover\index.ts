export { usePopover } from './usePopover';
export { usePopoverContext } from './context';
import { Popover as PopoverRoot, PopoverOptions } from './Popover';
import { PopoverTrigger } from './PopoverTrigger';
import { PopoverContent } from './PopoverContent';

export const Popover = Object.assign(PopoverRoot, {
    Trigger: PopoverTrigger,
    Content: PopoverContent,
});

export type { PopoverOptions };
