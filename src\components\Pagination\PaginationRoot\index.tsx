import { FC } from 'react';
import { ButtonProps } from '@shopify/polaris';
import { usePagination } from '@/hooks';
import { createEventHandler } from '@/utils';
import { PaginationProvider } from '../Pagination.context';

export interface PaginationRootProps {
    total: number;
    siblings?: number;
    boundaries?: number;
    value?: number;
    defaultValue?: number;
    disabled?: boolean;
    size?: ButtonProps['size'];
    onChange?: (page: number) => void;
    onNextPage?: () => void;
    onPreviousPage?: () => void;
    onFirstPage?: () => void;
    onLastPage?: () => void;
    children?: React.ReactNode;
}

export const PaginationRoot: FC<PaginationRootProps> = ({
    total,
    siblings = 1,
    boundaries = 1,
    value,
    defaultValue = 1,
    size = 'large',
    disabled,
    onChange,
    onFirstPage,
    onLastPage,
    onNextPage,
    onPreviousPage,
    children,
}) => {
    const { active, range, setPage, next, previous, first, last, hasNext, hasPrevious } = usePagination({
        page: value,
        initialPage: defaultValue,
        onChange,
        total,
        siblings,
        boundaries,
    });

    const handleNextPage = createEventHandler(onNextPage, next);
    const handlePreviousPage = createEventHandler(onPreviousPage, previous);
    const handleFirstPage = createEventHandler(onFirstPage, first);
    const handleLastPage = createEventHandler(onLastPage, last);

    return (
        <PaginationProvider
            value={{
                total,
                range,
                active,
                disabled,
                size,
                onChange: setPage,
                onNext: handleNextPage,
                onPrevious: handlePreviousPage,
                onFirst: handleFirstPage,
                onLast: handleLastPage,
                hasNext,
                hasPrevious,
            }}
        >
            {children}
        </PaginationProvider>
    );
};
