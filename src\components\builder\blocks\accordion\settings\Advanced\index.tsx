import { FC } from 'react';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { Sync } from './components/Sync';
import { Animation } from './components/Animation';
import { CustomCSS } from './components/CustomCSS';
import { DisplayOn } from './components/DisplayOn';
import { Size } from './components/Size';
import { Events } from './components/Events';
import { Position } from './components/Position';

const DEFAULT_PATH_DISPLAY_ON = 'displayOn';
const DEFAULT_PATH_ANIMATION = 'animation';
const DEFAULT_PATH_SYNC = 'syncOn';
const DEFAULT_PATH_POSITION = 'position';
const DEFAULT_PATH_CUSTOM_CSS = 'customCSS';
const DEFAULT_PATH_EVENTS = 'events';

export const Advanced: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <>
            <BaseHasBorderLayout>
                <Size blockId={selectedBlockId} isUpdateConfigs={false} />
                <DisplayOn
                    blockId={selectedBlockId}
                    path={DEFAULT_PATH_DISPLAY_ON}
                    label="Display on"
                    isUpdateConfigs
                />
                <Events blockId={selectedBlockId} path={DEFAULT_PATH_EVENTS} label="Events" isUpdateConfigs />
                <Animation blockId={selectedBlockId} path={DEFAULT_PATH_ANIMATION} label="Animation" isUpdateConfigs />
                <Sync blockId={selectedBlockId} path={DEFAULT_PATH_SYNC} label="Sync" isUpdateConfigs />
                <Position
                    blockId={selectedBlockId}
                    path={DEFAULT_PATH_POSITION}
                    label="Position"
                    isUpdateConfigs={false}
                />
                <CustomCSS
                    blockId={selectedBlockId}
                    path={DEFAULT_PATH_CUSTOM_CSS}
                    label="Custom CSS"
                    isUpdateConfigs
                />
            </BaseHasBorderLayout>
        </>
    );
};
