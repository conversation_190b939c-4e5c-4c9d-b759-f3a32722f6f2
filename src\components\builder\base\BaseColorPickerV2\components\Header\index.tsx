import { useEyeDropper } from '@/hooks';
import { Icon, Text } from '@shopify/polaris';
import { EyeDropperIcon, XIcon } from '@shopify/polaris-icons';
import { HsvaColor, validHex, rgbStringToHsva, color } from '@uiw/color-convert';
interface HeaderProps {
    onClose?: () => void;
    className?: string;
    children?: React.ReactNode;
    setHsva?: (hsva: HsvaColor) => void;
    setH?: (h: number) => void;
}
export const Header = ({ onClose, children, className, setHsva, setH }: HeaderProps) => {
    const { supported, open } = useEyeDropper();

    const pickColor = async () => {
        try {
            const { sRGBHex } = (await open())!;
            setHsva?.(validHex(sRGBHex) ? color(sRGBHex).hsva : rgbStringToHsva(sRGBHex));
            setH?.(color(sRGBHex).hsva.h);
        } catch (e) {
            console.log(e);
        }
    };
    return (
        <>
            <div
                css={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '12px 16px 12px 12px',
                    '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: '1px',
                        backgroundColor: '#ebebeb',
                    },
                }}
                className={className}
            >
                <Text as="span" variant="headingMd" fontWeight="medium">
                    Colors
                </Text>
                <div css={{ display: 'flex', alignItems: 'center', gap: '6px', marginRight: '-4px' }}>
                    <div
                        css={{
                            display: supported ? 'block' : 'none',
                            ':hover': {
                                background: '#F1F1F1',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            },
                        }}
                        onClick={pickColor}
                    >
                        <Icon source={EyeDropperIcon} />
                    </div>
                    <div
                        css={{
                            ':hover': {
                                background: '#F1F1F1',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            },
                        }}
                        onClick={onClose}
                    >
                        <Icon source={XIcon} />
                    </div>
                </div>
            </div>
            {children && (
                <div
                    css={{
                        position: 'relative',
                        padding: '12px 16px',
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: '1px',
                            backgroundColor: '#ebebeb',
                        },
                    }}
                >
                    {children}
                </div>
            )}
        </>
    );
};
