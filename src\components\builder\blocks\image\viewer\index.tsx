import { FC } from 'react';
import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER } from '@giaminhautoketing/auto-builder';
import imagePlaceholder from '@/assets/svgs/image-placeholder.svg?url';

export const ImageViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => (
    <BlockViewer
        autoId={autoId}
        cname={cname}
        label={label}
        type={type}
        attrs={{ [DATA_SET_VIEWER]: 'true' }}
        bpConfigs={bpConfigs}
        configs={configs}
        css={{ overflow: 'hidden' }}
    >
        <img
            src={configs.url ? (configs.url as string) : imagePlaceholder}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            alt="Image"
        />
    </BlockViewer>
);
