import { Auto_BlockType, genRandomBlockId } from '@giaminhautoketing/auto-builder';
import { FormField, FormSettings } from './types';

export const FormFieldConfigs: FormField[] = [
    {
        id: genRandomBlockId(),
        type: 'text',
        key: `input_name_${genRandomBlockId()}`,
        label: 'Full Name',
        placeholder: 'Enter your name',
        showInitialText: 'default',
        defaultField: true,
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },
        characterLimit: {
            setCharLimit: true,
            charLimit: 50,
        },
        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'email',
        key: `email`,
        label: 'Email',
        placeholder: 'Enter email',
        showInitialText: 'default',
        initialText: '',
        defaultField: true,
        validations: {
            required: true,
            readOnly: false,
        },
        characterLimit: {
            setCharLimit: true,
            charLimit: 50,
        },
        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'tel',
        key: `input_phone_${genRandomBlockId()}`,
        label: 'Phone Number',
        placeholder: 'Enter phone number',
        showInitialText: 'default',
        initialText: '',
        defaultField: true,
        validations: {
            required: true,
            readOnly: false,
        },

        characterLimit: {
            setCharLimit: true,
            charLimit: 500,
        },

        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        numberFormat: 'whole-number',
        maxValue: {
            addMaxValue: false,
            val: 500,
        },
        minValue: {
            addMinValue: false,
            val: 0,
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'text',
        key: `input_text_${genRandomBlockId()}`,
        label: 'Input text',
        placeholder: 'Enter your text',
        showInitialText: 'default',
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },
        characterLimit: {
            setCharLimit: true,
            charLimit: 500,
        },
        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        separateLine: true,
    },

    {
        id: genRandomBlockId(),
        type: 'tel',
        key: `input_tel_${genRandomBlockId()}`,
        label: 'Input Phone Number',
        placeholder: 'Enter phone number',
        showInitialText: 'default',
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },

        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        numberFormat: 'whole-number',
        maxValue: {
            addMaxValue: false,
            val: 500,
        },
        minValue: {
            addMinValue: false,
            val: 0,
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'password',
        key: `input_password_${genRandomBlockId()}`,
        label: 'Input Password',
        placeholder: 'Enter password',
        showInitialText: 'default',
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },
        characterLimit: {
            setCharLimit: true,
            charLimit: 50,
        },
        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'number',
        key: `input_number_${genRandomBlockId()}`,
        label: 'Input Number',
        placeholder: 'Enter number',
        showInitialText: 'default',
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },

        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        numberFormat: 'whole-number',
        digitsAfterDecimal: {
            val: 2,
        },
        maxValue: {
            addMaxValue: false,
            val: 500,
        },
        minValue: {
            addMinValue: false,
            val: 0,
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'long-text',
        key: `input_textarea_${genRandomBlockId()}`,
        label: 'Input Long Text',
        placeholder: 'Enter long text',
        showInitialText: 'default',
        initialText: '',
        validations: {
            required: true,
            readOnly: false,
        },
        characterLimit: {
            setCharLimit: true,
            charLimit: 500,
        },
        pattern: {
            addPatternValidation: false,
            pattern: '',
        },
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'dropdown',
        key: `dropdown_${genRandomBlockId()}`,
        label: 'Dropdown',
        placeholder: 'Select option',
        showInitialText: 'default',
        initialText: 'option1',
        options: [
            { id: genRandomBlockId(), value: 'option1', label: 'Option 1' },
            { id: genRandomBlockId(), value: 'option2', label: 'Option 2' },
            { id: genRandomBlockId(), value: 'option3', label: 'Option 3' },
        ],
        validations: {
            required: true,
        },
    },
    {
        id: genRandomBlockId(),
        type: 'checkbox',
        key: `checkbox_${genRandomBlockId()}`,
        label: 'Checkbox',
        validations: {
            required: true,
        },
        options: [
            {
                id: genRandomBlockId(),
                key: `checkbox_option_${genRandomBlockId()}`,
                value: 'option1',
                label: 'Option 1',
                defaultChecked: false,
                image: '',
            },
            {
                id: genRandomBlockId(),
                key: `checkbox_option_${genRandomBlockId()}`,
                value: 'option2',
                label: 'Option 2',
                defaultChecked: false,
                image: '',
            },
            {
                id: genRandomBlockId(),
                key: `checkbox_option_${genRandomBlockId()}`,
                value: 'option3',
                label: 'Option 3',
                defaultChecked: false,
                image: '',
            },
        ],
        direction: 'row',
        separateLine: true,
    },
    {
        id: genRandomBlockId(),
        type: 'radio',
        key: `radio_${genRandomBlockId()}`,
        label: 'Radio',
        showInitialText: 'default',
        options: [
            { id: genRandomBlockId(), value: 'option1', label: 'Option 1' },
            { id: genRandomBlockId(), value: 'option2', label: 'Option 2' },
            { id: genRandomBlockId(), value: 'option3', label: 'Option 3' },
        ],
        direction: 'row',
        initialText: 'option1',
        separateLine: true,
        validations: {
            required: true,
        },
    },
    {
        id: genRandomBlockId(),
        type: 'free-text',
        key: `free_text_${genRandomBlockId()}`,
        label: 'Free Text',
        description: '<p>Add your text here</p>',
        separateLine: true,
    },
];

export const FormSettingsConfigs: FormSettings[] = [
    {
        id: genRandomBlockId(),
        cname: 'form',
        label: 'Form Outlined',
        type: 'form' as Auto_BlockType,
        bpConfigs: {
            desktop: {
                backgroundForm: {
                    type: 'color',
                    color: 'rgba(255, 255, 255, 0)',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
                color: '#000000',
                width: { val: '500', unit: 'px' },
                formHeight: { val: '100', unit: '%' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                formAlignment: {
                    alignSelf: 'start',
                    justifySelf: 'start',
                },
                fieldsStyles: {
                    background: {
                        type: 'color',
                        color: 'rgba(255, 255, 255, 0)',
                        image: {
                            url: '',
                            repeat: '',
                            position: '',
                            attachment: '',
                            fill: '',
                        },
                    },
                    border: {
                        color: '#000000',
                        type: 'default',
                        top: { val: '1', unit: 'px' },
                        right: { val: '1', unit: 'px' },
                        bottom: { val: '1', unit: 'px' },
                        left: { val: '1', unit: 'px' },
                        radius: {
                            'top-left': { val: '5', unit: 'px' },
                            'top-right': { val: '5', unit: 'px' },
                            'bottom-right': { val: '5', unit: 'px' },
                            'bottom-left': { val: '5', unit: 'px' },
                        },
                    },
                },
                fieldType: 'label',
                fieldsLabel: {
                    labelFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    labelFontWeight: '400',
                    labelFontFamily: 'Roboto, sans-serif',
                    labelColor: '#000000',
                    labelTextTransform: 'default',
                    labelTextDecoration: 'default',
                    labelTextAlign: 'left',
                    labelFontStyle: 'default',
                    labelTextDirection: 'ltr',
                },
                fieldsInput: {
                    inputFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    inputFontWeight: '400',
                    inputFontFamily: 'Roboto, sans-serif',
                    inputColor: '#000000',
                    inputTextTransform: 'default',
                    inputTextDecoration: 'default',
                    inputTextAlign: 'left',
                    inputFontStyle: 'default',
                    inputTextDirection: 'ltr',
                },
                fieldsPlaceholder: {
                    placeholderFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    placeholderFontWeight: '400',
                    placeholderFontFamily: 'Roboto, sans-serif',
                    placeholderColor: '#9e9e9e',
                    placeholderTextAlign: 'left',
                    placeholderTextDirection: 'ltr',
                },
                fieldsSpacing: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '16', unit: 'px' },
                        right: { val: '16', unit: 'px' },
                    },
                },
                titleForm: {
                    fontSize: {
                        val: '32',
                        unit: 'px',
                    },
                    fontWeight: '700',
                    fontFamily: 'Roboto, sans-serif',
                    color: '#000000',
                    textShadow: undefined,
                    lineHeight: {
                        val: '1.5',
                    },
                    letterSpacing: {
                        val: '2',
                        unit: 'px',
                    },
                    titleSpacing: {
                        margin: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                        padding: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    fontStyle: 'default',
                },
                buttonSubmit: {
                    background: {
                        type: 'color',
                        color: '#000000',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    color: '#ffffff',
                    buttonLetterSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
                    buttonLineSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
                    buttonWidth: { val: '30', unit: '%' },
                    buttonHeight: { val: '36', unit: 'px' },
                    buttonSpacing: {
                        margin: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                        padding: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                    },
                    buttonBorder: {
                        color: '#000000',
                        top: { val: '1', unit: 'px' },
                        right: { val: '1', unit: 'px' },
                        bottom: { val: '1', unit: 'px' },
                        left: { val: '1', unit: 'px' },
                        type: 'default',
                        radius: {
                            'top-left': { val: '5', unit: 'px' },
                            'top-right': { val: '5', unit: 'px' },
                            'bottom-right': { val: '5', unit: 'px' },
                            'bottom-left': { val: '5', unit: 'px' },
                        },
                    },
                    boxShadow: undefined,
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    fontStyle: 'default',
                    textShadow: undefined,
                },
                arrangement: {
                    buttonAlign: 'center',
                    spaceTitleField: { val: '10', unit: 'px' },
                },
                formBorder: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                generalForm: {
                    spaceTitleField: { val: '10', unit: 'px' },
                    space: {
                        padding: {
                            top: { val: '10', unit: 'px' },
                            bottom: { val: '10', unit: 'px' },
                            left: { val: '10', unit: 'px' },
                            right: { val: '10', unit: 'px' },
                        },
                    },
                    vertical: { val: '12', unit: 'px' },
                    horizontal: { val: '12', unit: 'px' },
                    align: 'left',
                },
                fieldSizes: {
                    input_name_1: {
                        fieldWidth: { val: '50', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                    email: {
                        fieldWidth: { val: '50', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                    input_phone_1: {
                        fieldWidth: { val: '50', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                },
            },
            mobile: {
                backgroundForm: {
                    type: 'color',
                    color: 'rgba(255, 255, 255, 0)',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
                color: '#000000',
                width: { val: '400', unit: 'px' },
                formHeight: { val: '100', unit: '%' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                formAlignment: {
                    alignSelf: 'start',
                    justifySelf: 'start',
                },
                fieldsStyles: {
                    background: {
                        type: 'color',
                        color: 'rgba(255, 255, 255, 0)',
                        image: {
                            url: '',
                            repeat: '',
                            position: '',
                            attachment: '',
                            fill: '',
                        },
                    },
                    border: {
                        color: '#000000',
                        type: 'default',
                        top: { val: '1', unit: 'px' },
                        right: { val: '1', unit: 'px' },
                        bottom: { val: '1', unit: 'px' },
                        left: { val: '1', unit: 'px' },
                        radius: {
                            'top-left': { val: '5', unit: 'px' },
                            'top-right': { val: '5', unit: 'px' },
                            'bottom-right': { val: '5', unit: 'px' },
                            'bottom-left': { val: '5', unit: 'px' },
                        },
                    },
                },
                fieldType: 'label',
                fieldsLabel: {
                    labelFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    labelFontWeight: '400',
                    labelFontFamily: 'Roboto, sans-serif',
                    labelColor: '#000000',
                    labelTextTransform: 'default',
                    labelTextDecoration: 'default',
                    labelTextAlign: 'left',
                    labelFontStyle: 'default',
                    labelTextDirection: 'ltr',
                },
                fieldsInput: {
                    inputFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    inputFontWeight: '400',
                    inputFontFamily: 'Roboto, sans-serif',
                    inputColor: '#000000',
                    inputTextTransform: 'default',
                    inputTextDecoration: 'default',
                    inputTextAlign: 'left',
                    inputFontStyle: 'default',
                    inputTextDirection: 'ltr',
                },
                fieldsPlaceholder: {
                    placeholderFontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    placeholderFontWeight: '400',
                    placeholderFontFamily: 'Roboto, sans-serif',
                    placeholderColor: '#000000',
                    placeholderTextAlign: 'left',
                    placeholderTextDirection: 'ltr',
                },
                fieldsSpacing: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '16', unit: 'px' },
                        right: { val: '16', unit: 'px' },
                    },
                },
                titleForm: {
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    color: '#000000',
                    textShadow: undefined,
                    lineHeight: {
                        val: '1.5',
                    },
                    letterSpacing: {
                        val: '2',
                        unit: 'px',
                    },
                    titleSpacing: {
                        margin: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                        padding: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'left',
                    fontStyle: 'default',
                },
                buttonSubmit: {
                    background: {
                        type: 'color',
                        color: '#000000',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    color: '#ffffff',
                    buttonLetterSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
                    buttonLineSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
                    buttonWidth: { val: '25', unit: '%' },
                    buttonHeight: { val: '36', unit: 'px' },
                    buttonSpacing: {
                        margin: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                        padding: {
                            top: { val: '0', unit: 'px' },
                            bottom: { val: '0', unit: 'px' },
                            left: { val: '0', unit: 'px' },
                            right: { val: '0', unit: 'px' },
                        },
                    },
                    buttonBorder: {
                        color: '#000000',
                        top: { val: '1', unit: 'px' },
                        right: { val: '1', unit: 'px' },
                        bottom: { val: '1', unit: 'px' },
                        left: { val: '1', unit: 'px' },
                        type: 'default',
                        radius: {
                            'top-left': { val: '5', unit: 'px' },
                            'top-right': { val: '5', unit: 'px' },
                            'bottom-right': { val: '5', unit: 'px' },
                            'bottom-left': { val: '5', unit: 'px' },
                        },
                    },
                    boxShadow: undefined,
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    fontStyle: 'default',
                    textShadow: undefined,
                },
                arrangement: {
                    buttonAlign: 'center',
                    spaceTitleField: { val: '10', unit: 'px' },
                },
                formBorder: {
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                generalForm: {
                    spaceTitleField: { val: '10', unit: 'px' },
                    space: {
                        margin: {
                            top: { val: '10', unit: 'px' },
                            bottom: { val: '10', unit: 'px' },
                            left: { val: '10', unit: 'px' },
                            right: { val: '10', unit: 'px' },
                        },
                        padding: {
                            top: { val: '10', unit: 'px' },
                            bottom: { val: '10', unit: 'px' },
                            left: { val: '10', unit: 'px' },
                            right: { val: '10', unit: 'px' },
                        },
                    },
                    vertical: { val: '12', unit: 'px' },
                    horizontal: { val: '12', unit: 'px' },
                    align: 'left',
                },
                fieldSizes: {
                    input_name_1: {
                        fieldWidth: { val: '100', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                    email: {
                        fieldWidth: { val: '100', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                    input_phone_1: {
                        fieldWidth: { val: '100', unit: '%' },
                        fieldHeight: { val: '36', unit: 'px' },
                    },
                },
            },
        },
        configs: {
            form: [
                {
                    id: genRandomBlockId(),
                    type: 'text',
                    key: 'input_name_1',
                    label: 'Full Name',
                    placeholder: 'Enter your name',
                    showInitialText: 'default',
                    defaultField: true,
                    initialText: '',
                    validations: {
                        required: true,
                        readOnly: false,
                    },
                    characterLimit: {
                        setCharLimit: true,
                        charLimit: 50,
                    },
                    pattern: {
                        addPatternValidation: false,
                        pattern: '',
                    },
                    separateLine: false,
                },
                {
                    id: 'email',
                    type: 'email',
                    key: 'email',
                    label: 'Email',
                    placeholder: 'Enter email',
                    showInitialText: 'default',
                    initialText: '',
                    defaultField: true,
                    validations: {
                        required: true,
                        readOnly: false,
                    },
                    characterLimit: {
                        setCharLimit: true,
                        charLimit: 50,
                    },
                    pattern: {
                        addPatternValidation: false,
                        pattern: '',
                    },
                    separateLine: false,
                },
                {
                    id: genRandomBlockId(),
                    type: 'tel',
                    key: 'input_phone_1',
                    label: 'Phone Number',
                    placeholder: 'Enter phone number',
                    showInitialText: 'default',
                    initialText: '',
                    defaultField: true,
                    validations: {
                        required: true,
                        readOnly: false,
                    },
                    characterLimit: {
                        setCharLimit: true,
                        charLimit: 500,
                    },

                    pattern: {
                        addPatternValidation: false,
                        pattern: '',
                    },
                    numberFormat: 'whole-number',
                    maxValue: {
                        addMaxValue: false,
                        val: 500,
                    },
                    minValue: {
                        addMinValue: false,
                        val: 0,
                    },
                    separateLine: false,
                },
            ],
            contentForm: {
                formTitle: 'Get Started',
                buttonTitle: 'Send',
            },
            submit: {
                action: 'stay-on-page',
                successMessage: `Thanks for contacting us. We'll get back to you as soon as possible.`,
                errorMessage: `Can’t send email. Please try again later.`,
                openLink: {
                    url: '',
                    open: 'new-window',
                },
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'bounce',
                duration: { val: '0', unit: 's' },
                loop: '2',
                delay: { val: '0', unit: 's' },
            },
            customCSS: {
                enable: false,
                className: '',
                style: '',
            },
            events: {},
        },
        overlay: {
            desktop: {
                width: 500,
                height: 200,
            },
            mobile: {
                width: 500,
                height: 200,
            },
        },
    },
    // {
    //     id: genRandomBlockId(),
    //     cname: 'form',
    //     label: 'Form Underlined',
    //     type: 'form' as Auto_BlockType,
    //     bpConfigs: {
    //         desktop: {
    //             backgroundForm: {
    //                 type: 'color',
    //                 color: 'rgba(255, 255, 255, 0)',
    //                 image: {
    //                     url: '',
    //                     repeat: 'no',
    //                     position: 'center center',
    //                     attachment: 'fixed',
    //                     fill: 'cover',
    //                 },
    //             },
    //             color: '#000000',
    //             width: { val: '500', unit: 'px' },
    //             formHeight: { val: '100', unit: '%' },
    //             position: {
    //                 positionType: 'default',
    //                 stickTo: {
    //                     stickToType: 'top',
    //                     top: { val: '0', unit: '%' },
    //                     bottom: { val: '0', unit: '%' },
    //                 },
    //             },
    //             boxShadow: undefined,
    //             formAlignment: {
    //                 alignSelf: 'start',
    //                 justifySelf: 'start',
    //             },
    //             fieldsStyles: {
    //                 background: {
    //                     type: 'color',
    //                     color: 'rgba(255, 255, 255, 0)',
    //                     image: {
    //                         url: '',
    //                         repeat: '',
    //                         position: '',
    //                         attachment: '',
    //                         fill: '',
    //                     },
    //                 },
    //                 border: {
    //                     color: '#000000',
    //                     type: 'default',
    //                     top: { val: '1', unit: 'px' },
    //                     right: { val: '1', unit: 'px' },
    //                     bottom: { val: '1', unit: 'px' },
    //                     left: { val: '1', unit: 'px' },
    //                     radius: {
    //                         'top-left': { val: '5', unit: 'px' },
    //                         'top-right': { val: '5', unit: 'px' },
    //                         'bottom-right': { val: '5', unit: 'px' },
    //                         'bottom-left': { val: '5', unit: 'px' },
    //                     },
    //                 },
    //             },
    //             fieldType: 'label',
    //             fieldsLabel: {
    //                 labelFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 labelFontWeight: '400',
    //                 labelFontFamily: 'Roboto, sans-serif',
    //                 labelColor: '#000000',
    //                 labelTextTransform: 'default',
    //                 labelTextDecoration: 'default',
    //                 labelTextAlign: 'left',
    //                 labelFontStyle: 'default',
    //                 labelTextDirection: 'ltr',
    //             },
    //             fieldsInput: {
    //                 inputFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 inputFontWeight: '400',
    //                 inputFontFamily: 'Roboto, sans-serif',
    //                 inputColor: '#000000',
    //                 inputTextTransform: 'default',
    //                 inputTextDecoration: 'default',
    //                 inputTextAlign: 'left',
    //                 inputFontStyle: 'default',
    //                 inputTextDirection: 'ltr',
    //             },
    //             fieldsPlaceholder: {
    //                 placeholderFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 placeholderFontWeight: '400',
    //                 placeholderFontFamily: 'Roboto, sans-serif',
    //                 placeholderColor: '#9e9e9e',
    //                 placeholderTextAlign: 'left',
    //                 placeholderTextDirection: 'ltr',
    //             },
    //             fieldsSpacing: {
    //                 padding: {
    //                     top: { val: '2', unit: 'px' },
    //                     bottom: { val: '2', unit: 'px' },
    //                     left: { val: '16', unit: 'px' },
    //                     right: { val: '16', unit: 'px' },
    //                 },
    //             },
    //             titleForm: {
    //                 fontSize: {
    //                     val: '32',
    //                     unit: 'px',
    //                 },
    //                 fontWeight: '700',
    //                 fontFamily: 'Roboto, sans-serif',
    //                 color: '#000000',
    //                 textShadow: undefined,
    //                 lineHeight: {
    //                     val: '1.5',
    //                 },
    //                 letterSpacing: {
    //                     val: '2',
    //                     unit: 'px',
    //                 },
    //                 titleSpacing: {
    //                     margin: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                     padding: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                 },
    //                 textTransform: 'default',
    //                 textDecoration: 'default',
    //                 textAlign: 'center',
    //                 fontStyle: 'default',
    //             },
    //             buttonSubmit: {
    //                 background: {
    //                     type: 'color',
    //                     color: '#000000',
    //                     image: {
    //                         url: '',
    //                         repeat: 'no',
    //                         position: 'center center',
    //                         attachment: 'fixed',
    //                         fill: 'cover',
    //                     },
    //                 },
    //                 fontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 fontWeight: '400',
    //                 fontFamily: 'Roboto, sans-serif',
    //                 color: '#ffffff',
    //                 buttonLetterSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
    //                 buttonLineSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
    //                 buttonWidth: { val: '30', unit: '%' },
    //                 buttonHeight: { val: '36', unit: 'px' },
    //                 buttonSpacing: {
    //                     margin: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                     padding: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                 },
    //                 buttonBorder: {
    //                     color: '#000000',
    //                     top: { val: '1', unit: 'px' },
    //                     right: { val: '1', unit: 'px' },
    //                     bottom: { val: '1', unit: 'px' },
    //                     left: { val: '1', unit: 'px' },
    //                     type: 'default',
    //                     radius: {
    //                         'top-left': { val: '5', unit: 'px' },
    //                         'top-right': { val: '5', unit: 'px' },
    //                         'bottom-right': { val: '5', unit: 'px' },
    //                         'bottom-left': { val: '5', unit: 'px' },
    //                     },
    //                 },
    //                 boxShadow: undefined,
    //                 textTransform: 'default',
    //                 textDecoration: 'default',
    //                 textAlign: 'center',
    //                 fontStyle: 'default',
    //                 textShadow: undefined,
    //             },
    //             arrangement: {
    //                 buttonAlign: 'center',
    //                 spaceTitleField: { val: '10', unit: 'px' },
    //             },
    //             formBorder: {
    //                 color: '#000000',
    //                 top: { val: '1', unit: 'px' },
    //                 right: { val: '1', unit: 'px' },
    //                 bottom: { val: '1', unit: 'px' },
    //                 left: { val: '1', unit: 'px' },
    //                 type: 'default',
    //                 radius: {
    //                     'top-left': { val: '5', unit: 'px' },
    //                     'top-right': { val: '5', unit: 'px' },
    //                     'bottom-right': { val: '5', unit: 'px' },
    //                     'bottom-left': { val: '5', unit: 'px' },
    //                 },
    //             },
    //             generalForm: {
    //                 spaceTitleField: { val: '10', unit: 'px' },
    //                 space: {
    //                     padding: {
    //                         top: { val: '10', unit: 'px' },
    //                         bottom: { val: '10', unit: 'px' },
    //                         left: { val: '10', unit: 'px' },
    //                         right: { val: '10', unit: 'px' },
    //                     },
    //                 },
    //                 vertical: { val: '12', unit: 'px' },
    //                 horizontal: { val: '12', unit: 'px' },
    //                 align: 'left',
    //             },
    //             fieldSizes: {
    //                 input_name_1: {
    //                     fieldWidth: { val: '50', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //                 input_email_1: {
    //                     fieldWidth: { val: '50', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //                 input_phone_1: {
    //                     fieldWidth: { val: '50', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //             },
    //         },
    //         mobile: {
    //             backgroundForm: {
    //                 type: 'color',
    //                 color: 'rgba(255, 255, 255, 0)',
    //                 image: {
    //                     url: '',
    //                     repeat: 'no',
    //                     position: 'center center',
    //                     attachment: 'fixed',
    //                     fill: 'cover',
    //                 },
    //             },
    //             color: '#000000',
    //             width: { val: '400', unit: 'px' },
    //             formHeight: { val: '100', unit: '%' },
    //             position: {
    //                 positionType: 'default',
    //                 stickTo: {
    //                     stickToType: 'top',
    //                     top: { val: '0', unit: '%' },
    //                     bottom: { val: '0', unit: '%' },
    //                 },
    //             },
    //             boxShadow: undefined,
    //             formAlignment: {
    //                 alignSelf: 'start',
    //                 justifySelf: 'start',
    //             },
    //             fieldsStyles: {
    //                 background: {
    //                     type: 'color',
    //                     color: 'rgba(255, 255, 255, 0)',
    //                     image: {
    //                         url: '',
    //                         repeat: '',
    //                         position: '',
    //                         attachment: '',
    //                         fill: '',
    //                     },
    //                 },
    //                 border: {
    //                     color: '#000000',
    //                     type: 'default',
    //                     top: { val: '1', unit: 'px' },
    //                     right: { val: '1', unit: 'px' },
    //                     bottom: { val: '1', unit: 'px' },
    //                     left: { val: '1', unit: 'px' },
    //                     radius: {
    //                         'top-left': { val: '5', unit: 'px' },
    //                         'top-right': { val: '5', unit: 'px' },
    //                         'bottom-right': { val: '5', unit: 'px' },
    //                         'bottom-left': { val: '5', unit: 'px' },
    //                     },
    //                 },
    //             },
    //             fieldType: 'label',
    //             fieldsLabel: {
    //                 labelFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 labelFontWeight: '400',
    //                 labelFontFamily: 'Roboto, sans-serif',
    //                 labelColor: '#000000',
    //                 labelTextTransform: 'default',
    //                 labelTextDecoration: 'default',
    //                 labelTextAlign: 'left',
    //                 labelFontStyle: 'default',
    //                 labelTextDirection: 'ltr',
    //             },
    //             fieldsInput: {
    //                 inputFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 inputFontWeight: '400',
    //                 inputFontFamily: 'Roboto, sans-serif',
    //                 inputColor: '#000000',
    //                 inputTextTransform: 'default',
    //                 inputTextDecoration: 'default',
    //                 inputTextAlign: 'left',
    //                 inputFontStyle: 'default',
    //                 inputTextDirection: 'ltr',
    //             },
    //             fieldsPlaceholder: {
    //                 placeholderFontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 placeholderFontWeight: '400',
    //                 placeholderFontFamily: 'Roboto, sans-serif',
    //                 placeholderColor: '#000000',
    //                 placeholderTextAlign: 'left',
    //                 placeholderTextDirection: 'ltr',
    //             },
    //             fieldsSpacing: {
    //                 padding: {
    //                     top: { val: '2', unit: 'px' },
    //                     bottom: { val: '2', unit: 'px' },
    //                     left: { val: '16', unit: 'px' },
    //                     right: { val: '16', unit: 'px' },
    //                 },
    //             },
    //             titleForm: {
    //                 fontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 fontWeight: '400',
    //                 fontFamily: 'Roboto, sans-serif',
    //                 color: '#000000',
    //                 textShadow: undefined,
    //                 lineHeight: {
    //                     val: '1.5',
    //                 },
    //                 letterSpacing: {
    //                     val: '2',
    //                     unit: 'px',
    //                 },
    //                 titleSpacing: {
    //                     margin: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                     padding: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                 },
    //                 textTransform: 'default',
    //                 textDecoration: 'default',
    //                 textAlign: 'left',
    //                 fontStyle: 'default',
    //             },
    //             buttonSubmit: {
    //                 background: {
    //                     type: 'color',
    //                     color: '#000000',
    //                     image: {
    //                         url: '',
    //                         repeat: 'no',
    //                         position: 'center center',
    //                         attachment: 'fixed',
    //                         fill: 'cover',
    //                     },
    //                 },
    //                 fontSize: {
    //                     val: '16',
    //                     unit: 'px',
    //                 },
    //                 fontWeight: '400',
    //                 fontFamily: 'Roboto, sans-serif',
    //                 color: '#ffffff',
    //                 buttonLetterSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
    //                 buttonLineSpacing: { type: 'default', value: { val: '0', unit: 'px' } },
    //                 buttonWidth: { val: '25', unit: '%' },
    //                 buttonHeight: { val: '36', unit: 'px' },
    //                 buttonSpacing: {
    //                     margin: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                     padding: {
    //                         top: { val: '0', unit: 'px' },
    //                         bottom: { val: '0', unit: 'px' },
    //                         left: { val: '0', unit: 'px' },
    //                         right: { val: '0', unit: 'px' },
    //                     },
    //                 },
    //                 buttonBorder: {
    //                     color: '#000000',
    //                     top: { val: '1', unit: 'px' },
    //                     right: { val: '1', unit: 'px' },
    //                     bottom: { val: '1', unit: 'px' },
    //                     left: { val: '1', unit: 'px' },
    //                     type: 'default',
    //                     radius: {
    //                         'top-left': { val: '5', unit: 'px' },
    //                         'top-right': { val: '5', unit: 'px' },
    //                         'bottom-right': { val: '5', unit: 'px' },
    //                         'bottom-left': { val: '5', unit: 'px' },
    //                     },
    //                 },
    //                 boxShadow: undefined,
    //                 textTransform: 'default',
    //                 textDecoration: 'default',
    //                 textAlign: 'center',
    //                 fontStyle: 'default',
    //                 textShadow: undefined,
    //             },
    //             arrangement: {
    //                 buttonAlign: 'center',
    //                 spaceTitleField: { val: '10', unit: 'px' },
    //             },
    //             formBorder: {
    //                 color: '#000000',
    //                 top: { val: '1', unit: 'px' },
    //                 right: { val: '1', unit: 'px' },
    //                 bottom: { val: '1', unit: 'px' },
    //                 left: { val: '1', unit: 'px' },
    //                 type: 'default',
    //                 radius: {
    //                     'top-left': { val: '5', unit: 'px' },
    //                     'top-right': { val: '5', unit: 'px' },
    //                     'bottom-right': { val: '5', unit: 'px' },
    //                     'bottom-left': { val: '5', unit: 'px' },
    //                 },
    //             },
    //             generalForm: {
    //                 spaceTitleField: { val: '10', unit: 'px' },
    //                 space: {
    //                     margin: {
    //                         top: { val: '10', unit: 'px' },
    //                         bottom: { val: '10', unit: 'px' },
    //                         left: { val: '10', unit: 'px' },
    //                         right: { val: '10', unit: 'px' },
    //                     },
    //                     padding: {
    //                         top: { val: '10', unit: 'px' },
    //                         bottom: { val: '10', unit: 'px' },
    //                         left: { val: '10', unit: 'px' },
    //                         right: { val: '10', unit: 'px' },
    //                     },
    //                 },
    //                 vertical: { val: '12', unit: 'px' },
    //                 horizontal: { val: '12', unit: 'px' },
    //                 align: 'left',
    //             },
    //             fieldSizes: {
    //                 input_name_1: {
    //                     fieldWidth: { val: '100', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //                 input_email_1: {
    //                     fieldWidth: { val: '100', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //                 input_phone_1: {
    //                     fieldWidth: { val: '100', unit: '%' },
    //                     fieldHeight: { val: '36', unit: 'px' },
    //                 },
    //             },
    //         },
    //     },
    //     configs: {
    //         form: [
    //             {
    //                 id: genRandomBlockId(),
    //                 type: 'text',
    //                 key: 'input_name_1',
    //                 label: 'Full Name',
    //                 placeholder: 'Enter your name',
    //                 showInitialText: 'default',
    //                 defaultField: true,
    //                 initialText: '',
    //                 validations: {
    //                     required: true,
    //                     readOnly: false,
    //                 },
    //                 characterLimit: {
    //                     setCharLimit: true,
    //                     charLimit: 50,
    //                 },
    //                 pattern: {
    //                     addPatternValidation: false,
    //                     pattern: '',
    //                 },
    //                 separateLine: true,
    //             },
    //             {
    //                 id: genRandomBlockId(),
    //                 type: 'email',
    //                 key: 'input_email_1',
    //                 label: 'Email',
    //                 placeholder: 'Enter email',
    //                 showInitialText: 'default',
    //                 initialText: '',
    //                 defaultField: true,
    //                 validations: {
    //                     required: true,
    //                     readOnly: false,
    //                 },
    //                 characterLimit: {
    //                     setCharLimit: true,
    //                     charLimit: 50,
    //                 },
    //                 pattern: {
    //                     addPatternValidation: false,
    //                     pattern: '',
    //                 },
    //                 separateLine: true,
    //             },
    //             {
    //                 id: genRandomBlockId(),
    //                 type: 'tel',
    //                 key: 'input_phone_1',
    //                 label: 'Phone Number',
    //                 placeholder: 'Enter phone number',
    //                 showInitialText: 'default',
    //                 initialText: '',
    //                 defaultField: true,
    //                 validations: {
    //                     required: true,
    //                     readOnly: false,
    //                 },
    //                 characterLimit: {
    //                     setCharLimit: true,
    //                     charLimit: 500,
    //                 },

    //                 pattern: {
    //                     addPatternValidation: false,
    //                     pattern: '',
    //                 },
    //                 numberFormat: 'whole-number',
    //                 maxValue: {
    //                     addMaxValue: false,
    //                     val: 500,
    //                 },
    //                 minValue: {
    //                     addMinValue: false,
    //                     val: 0,
    //                 },
    //                 separateLine: true,
    //             },
    //         ],
    //         contentForm: {
    //             formTitle: 'Get Started',
    //             buttonTitle: 'Send',
    //         },
    //         submit: {
    //             action: 'stay-on-page',
    //             successMessage: `Thanks for contacting us. We'll get back to you as soon as possible.`,
    //             errorMessage: `Can’t send email. Please try again later.`,
    //             openLink: {
    //                 url: '',
    //                 open: 'new-window',
    //             },
    //         },
    //         displayOnDesktop: true,
    //         displayOnMobile: true,
    //         animation: {
    //             type: 'bounce',
    //             duration: { val: '0', unit: 's' },
    //             loop: '2',
    //             delay: { val: '0', unit: 's' },
    //         },
    //         customCSS: {
    //             enable: false,
    //             className: '',
    //             style: '',
    //         },
    //         events: {},
    //     },
    //     overlay: {
    //         desktop: {
    //             width: 500,
    //             height: 200,
    //         },
    //         mobile: {
    //             width: 500,
    //             height: 200,
    //         },
    //     },
    // },
];
