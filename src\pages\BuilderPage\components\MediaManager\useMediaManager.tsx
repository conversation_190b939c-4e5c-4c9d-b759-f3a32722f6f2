import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware';
import { MediaManagerStore } from './types';

export const useMediaManager = create<MediaManagerStore>()(
    devtools(
        immer((set) => ({
            open: false,
            multiple: false,
            page: 1,
            sortBy: 'all',
            selected: [],
            currentTab: 'upload',
            endCursor: '',
            allows: 'all',
            forceUpdate: 0,
            setCurrentTab: (tab) => {
                set((state) => {
                    state.currentTab = tab;
                    state.page = 1;
                    state.sortBy = 'all';
                    state.selected = [];
                    state.endCursor = '';
                });
            },
            setSelected: (selected) => set({ selected }),
            setEndCursor: (endCursor) => set({ endCursor }),
            setSortBy: (sortBy) => {
                set((state) => {
                    state.sortBy = sortBy;
                    state.page = 1;
                    state.selected = [];
                    state.endCursor = '';
                });
            },
            setPage: (page) => set({ page }),
            setOpen: (open, options) => {
                set((state) => {
                    state.open = open;
                    if (!open) {
                        state.page = 1;
                        state.sortBy = 'all';
                        state.selected = [];
                        state.endCursor = '';
                    }
                    if (options) {
                        Object.entries(options).forEach(([key, value]) => {
                            if (key === 'forceUpdate') {
                                state.forceUpdate = state.forceUpdate + 1;
                            } else if (value !== undefined) {
                                (state as Record<string, unknown>)[key] = value;
                            }
                        });
                    }
                });
            },
        })),
        {
            name: 'media-manager-store',
        },
    ),
);
