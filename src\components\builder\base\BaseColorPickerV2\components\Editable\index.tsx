import { FC, useState } from 'react';
import { hexToHsva, HsvaColor } from '@uiw/color-convert';
import { SelectFormat } from './SelectFormat';
import { Rgba } from './Rgba';
import { Hexa } from './Hexa';

interface EditableProps {
    hsva: HsvaColor;
    setHsva(hsva: HsvaColor): void;
}

export const Editable: FC<EditableProps> = ({ hsva, setHsva }) => {
    const [format, setFormat] = useState<'hex' | 'rgba'>('hex');
    return (
        <div
            css={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
            }}
        >
            <SelectFormat format={format} onChange={setFormat} />
            {format === 'hex' ? (
                <Hexa
                    hsva={hsva}
                    onChangeHex={(_, value) => {
                        setHsva(hexToHsva(value as string));
                    }}
                    onChangeAlpha={(color) => {
                        setHsva({ ...hsva, ...color.hsva });
                    }}
                />
            ) : (
                <Rgba
                    hsva={hsva}
                    onChange={(color) => {
                        setHsva({ ...hsva, ...color.hsva });
                    }}
                />
            )}
        </div>
    );
};
