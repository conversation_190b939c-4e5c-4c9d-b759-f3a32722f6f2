import { FC, useState } from 'react';
import { BaseHasBorderLayout } from '@/components/builder/base';
import { FormFieldConfigs } from '@/components/builder/blocks/form/configs';
import { InlineStack, Text, Button } from '@shopify/polaris';
import { CheckCircleIcon, PlusCircleIcon } from '@shopify/polaris-icons';
import { genRandomBlockId, useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import { FormField } from '@/components/builder/blocks/form/types';
import {
    useFieldSize,
    useSettingsFormField,
} from '@/components/builder/blocks/form/settings/Contents/AddFieldPanel/utils';
import './styles.scss';

const MOBILE_DEVICE = 'mobile' as const;
const DESKTOP_DEVICE = 'desktop' as const;

export const AddFieldPanel: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const formConfigs = getBlockProperty('configs.form', selectedBlockId) as FormField[];
    const fieldSizesMobile = useBlockStore((state) => state.blocks[selectedBlockId].bpConfigs.mobile.fieldSizes);
    const fieldSizesDesktop = useBlockStore((state) => state.blocks[selectedBlockId].bpConfigs.desktop.fieldSizes);
    const { getDefaultFieldSize } = useFieldSize();

    const [addedFields, setAddedFields] = useState<Set<string>>(new Set());

    const updaterFormField = {
        items: useSettingsFormField({
            blockId: selectedBlockId,
            path: 'form',
            isUpdateConfigs: true,
        }).updateValue,

        fieldSizes: {
            desktop: useSettingsFormField({
                blockId: selectedBlockId,
                path: 'fieldSizes',
                isUpdateConfigs: false,
                device: DESKTOP_DEVICE,
            }).updateValue,

            mobile: useSettingsFormField({
                blockId: selectedBlockId,
                path: 'fieldSizes',
                isUpdateConfigs: false,
                device: MOBILE_DEVICE,
            }).updateValue,
        },
    };

    const temporarilyHighlightField = (key: string) => {
        setAddedFields((prev) => new Set([...prev, key]));
        setTimeout(() => {
            setAddedFields((prev) => {
                const newSet = new Set(prev);
                newSet.delete(key);
                return newSet;
            });
        }, 1500);
    };

    const handleAddField = (field: FormField) => {
        if (field.defaultField) return;
        const newId = genRandomBlockId();
        const newKey = ['checkbox', 'radio', 'dropdown', 'free-text'].includes(field.type)
            ? `${field.type}_${newId}`
            : `input_${field.type}_${newId}`;

        const newField = {
            ...field,
            id: newId,
            key: newKey,
        };

        const updatedConfigs = [...(formConfigs || []), newField];
        const fieldSizeDesktop = getDefaultFieldSize(newField, DESKTOP_DEVICE);
        const fieldSizeMobile = getDefaultFieldSize(newField, MOBILE_DEVICE);

        updaterFormField.items(updatedConfigs);
        updaterFormField.fieldSizes.desktop({ ...fieldSizesDesktop, ...fieldSizeDesktop });
        updaterFormField.fieldSizes.mobile({ ...fieldSizesMobile, ...fieldSizeMobile });

        temporarilyHighlightField(newField.type);
    };

    return (
        <BaseHasBorderLayout>
            {FormFieldConfigs.map((formField) => {
                const isRecentlyAdded = addedFields.has(formField.type);
                const isAdded = formField.defaultField || isRecentlyAdded;

                return (
                    <InlineStack key={formField.key} blockAlign="center" gap="200" align="space-between">
                        <Text as="p" variant="bodyMd">
                            {formField.label}
                        </Text>
                        <Button
                            icon={isAdded ? CheckCircleIcon : PlusCircleIcon}
                            variant="plain"
                            onClick={() => handleAddField(formField)}
                            id={isAdded ? 'check-circle-icon' : 'plus-circle-icon'}
                        />
                    </InlineStack>
                );
            })}
        </BaseHasBorderLayout>
    );
};
