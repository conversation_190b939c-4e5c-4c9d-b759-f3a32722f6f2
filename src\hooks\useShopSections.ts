import { useEffect, useMemo, useState } from 'react';
import clsx from 'clsx';
import { SortButtonChoice, useSetIndexFiltersMode } from '@shopify/polaris';
import { useDebounce, usePrevious, useQuery } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
import { SectionsResponse } from '@/pages/SectionsPage/types';

type SkeletonCountState = {
    [tabIndex: number]: {
        [page: number]: number;
    };
};

export function useShopSections() {
    const { mode, setMode } = useSetIndexFiltersMode();
    const [page, setPage] = useState<number>(1);
    const [tabSelected, setTabSelected] = useState(0);
    const [sortSelected, setSortSelected] = useState<string[]>(['dateModified desc']);
    const [selectedItems, setSelectedItems] = useState<string[]>([]);
    const [queryValue, setQueryValue] = useState('');
    const searchValue = useDebounce(queryValue, 500, () => setPage(1));
    const [skeletonCount, setSkeletonCount] = useState<SkeletonCountState>({});

    const LIMIT = 5;
    const DEFAULT_SKELETON_COUNT = 5;

    const tabs = ['All', 'Published', 'Unpublished'].map((item, index) => ({
        id: `${item}-${index}`,
        index,
        content: item,
        onAction: () => {},
        isLocked: true,
        actions: [],
    }));

    const sortOptions: SortButtonChoice[] = [
        {
            label: 'Title',
            value: 'title asc',
            directionLabel: 'A-Z',
        },
        {
            label: 'Title',
            value: 'title desc',
            directionLabel: 'Z-A',
        },
        {
            label: 'Date Modified',
            value: 'dateModified asc',
            directionLabel: 'Oldest first',
        },
        {
            label: 'Date Modified',
            value: 'dateModified desc',
            directionLabel: 'Newest first',
        },
    ];

    const status = ['', '1', '0'];

    const { data, isLoading, refetch } = useQuery<SectionsResponse>({
        url: clsx(
            apiAddress.shopSections.index,
            `?currentPage=${page}`,
            `&perPage=${LIMIT}`,
            `&sortName=${sortSelected[0].split(' ')[0]}`,
            `&order=${sortSelected[0].split(' ')[1]}`,
            searchValue && `&keyword=${searchValue}`,
            status[tabSelected] && `&status=${status[tabSelected]}`,
        ).replace(/\s+/g, ''),
        method: 'GET',
        sleepTime: 300,
        onSuccess: () => {
            setSelectedItems([]);
        },
    });

    const sections = useMemo(() => data?.result?.data?.list || [], [data]);
    const total = data?.result?.data?.total || 0;

    useEffect(() => {
        if (!isLoading) {
            setSkeletonCount((prev) => ({
                ...prev,
                [tabSelected]: {
                    ...prev?.[tabSelected],
                    [page]: sections.length,
                },
            }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoading]);

    const skeletonDisplay = skeletonCount?.[tabSelected]?.[page];
    const skeletonDisplayPrevious = usePrevious(skeletonDisplay);

    return {
        // Response
        sections,
        total,

        // State
        page,
        queryValue,
        sortSelected,
        searchValue,
        mode,
        tabSelected,
        selectedItems,
        isLoading,
        skeletonCount: skeletonDisplay || skeletonDisplayPrevious || DEFAULT_SKELETON_COUNT,

        // Constants
        tabs,
        sortOptions,
        limit: LIMIT,

        // Action handlers
        setMode,
        setTabSelected,
        setPage,
        setSortSelected,
        setQueryValue,
        setSelectedItems,
        refetch,
    };
}
