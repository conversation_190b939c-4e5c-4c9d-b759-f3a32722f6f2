const scriptCache = new Map<string, string>();
export const loadScripts = async (blockTypes: string[]) => {
    const knownJsFiles = new Set(['form', 'button']);
    const typesToLoad = blockTypes.filter((type) => knownJsFiles.has(type) && !scriptCache.has(type));
    await Promise.all(
        typesToLoad.map(async (type) => {
            try {
                const res = await fetch(`/src/components/builder/blocks/${type}/scripts/${type}.js`);
                if (res.ok) {
                    const scriptText = await res.text();
                    scriptCache.set(type, scriptText.replace(/\/\/# sourceMappingURL=.*$/gm, ''));
                }
            } catch (error) {
                console.error(`Failed to load script for ${type}:`, error);
            }
        }),
    );
    const uniqueTypes = Array.from(
        new Set(blockTypes.filter((type) => knownJsFiles.has(type) && scriptCache.has(type))),
    );
    const scripts = uniqueTypes.map((type) => `/* ${type} */\n${scriptCache.get(type)}\n\n`);

    return `
    document.addEventListener('DOMContentLoaded', () => {
        const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animation');
                observer.unobserve(entry.target);
                }
            });
        });
        document.querySelectorAll('[data-atk-animation="true"]').forEach((el) => observer.observe(el));
        ${scripts.join('\n\n')}\n
    });`;
};
