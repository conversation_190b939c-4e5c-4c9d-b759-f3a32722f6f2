import { FC, useEffect, useState } from 'react';
import { Button, Grid, Page } from '@shopify/polaris';
import { CalendarIcon } from '@shopify/polaris-icons';
import { analyticsConfigs } from './configs';
import { QuantityContainer } from './styled';
import { TrafficChart } from './TrafficChart';
import { SkeletonAnalytics } from './SkeletonAnalytics';
import { AnalyticsItem } from './AnalyticsItem';

export const Analytics: FC = () => {
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        setTimeout(() => {
            setLoading(false);
        }, 1000);
    }, []);

    return (
        <>
            <QuantityContainer>
                <Page
                    backAction={{ content: 'Products', url: '#' }}
                    title="Analytics"
                    subtitle="View analytics for all name landing pages"
                    secondaryActions={<Button icon={CalendarIcon}>Last 30 days</Button>}
                >
                    {loading ? (
                        <SkeletonAnalytics />
                    ) : (
                        <>
                            <Grid>
                                {analyticsConfigs.map((item, index) => (
                                    <AnalyticsItem
                                        key={index}
                                        item={{
                                            title: item.title,
                                            number: item.number,
                                            tooltip: item.tooltip,
                                            percentage: {
                                                value: item.percentage.value,
                                                fluctuate: item.percentage.fluctuate as 'up' | 'down',
                                            },
                                        }}
                                    />
                                ))}
                            </Grid>
                            <TrafficChart
                                valueSessionData={[
                                    {
                                        dimensionValues: '20250324',
                                        metricValues: '100',
                                    },
                                    {
                                        dimensionValues: '20250325',
                                        metricValues: '200',
                                    },
                                    {
                                        dimensionValues: '20250326',
                                        metricValues: '300',
                                    },
                                    {
                                        dimensionValues: '20250327',
                                        metricValues: '400',
                                    },
                                    {
                                        dimensionValues: '20250328',
                                        metricValues: '100',
                                    },
                                    {
                                        dimensionValues: '20250329',
                                        metricValues: '170',
                                    },
                                    {
                                        dimensionValues: '20250330',
                                        metricValues: '550',
                                    },
                                ]}
                            />
                        </>
                    )}
                </Page>
            </QuantityContainer>
        </>
    );
};
