import { FC, HTMLAttributes } from 'react';

export interface PointerProps extends HTMLAttributes<HTMLDivElement> {
    left?: string;
    top?: string;
}

export const Pointer: FC<PointerProps> = ({ left, top, ...otherProps }) => {
    return (
        <div
            style={{
                top,
                left,
            }}
            css={{
                display: 'flex',
                position: 'absolute',
                width: '16px',
                height: '16px',
                borderWidth: '3px',
                borderStyle: 'solid',
                borderColor: 'white',
                borderRadius: '50%',
                transform: left ? 'translateX(-50%)' : 'translateY(-50%)',
                cursor: 'pointer',
            }}
            {...otherProps}
        />
    );
};
