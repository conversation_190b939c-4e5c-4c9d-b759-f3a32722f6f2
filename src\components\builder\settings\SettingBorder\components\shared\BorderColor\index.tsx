import { FC, useState } from 'react';
import { Button } from '@shopify/polaris';
import { XIcon } from '@shopify/polaris-icons';
import { BaseColorPickerV2 } from '@/components/builder/base';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';
import { useBuilderStore, useBlockStore } from '@giaminhautoketing/auto-builder';
import './style.scss';

interface BorderColorProps {
    blockId: string;
    isUpdateConfigs?: boolean;
    path: string;
}

export const BorderColor: FC<BorderColorProps> = ({ blockId, isUpdateConfigs, path }) => {
    const [isDelete, setIsDelete] = useState(false);

    const colorValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChangeColor = (color: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, color);
        } else {
            updateBlockProperty(blockId, currentDevice, path, color);
        }
    };

    const handleDeleteColor = () => {
        setIsDelete(true);
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, 'rgba(255, 255, 255, 0)');
        } else {
            updateBlockProperty(blockId, currentDevice, path, 'rgba(255, 255, 255, 0)');
        }
    };

    const handleOpenChange = (open: boolean) => {
        if (open && isDelete) {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, path, '#000000');
            } else {
                updateBlockProperty(blockId, currentDevice, path, '#000000');
            }
            setIsDelete(false);
        }
    };

    return (
        <div className="border-color-picker">
            <BaseColorPickerV2
                color={colorValue ?? '#000'}
                onChange={handleChangeColor}
                onOpenChange={handleOpenChange}
                triggerRender={
                    <div
                        style={{
                            width: '18px',
                            height: '18px',
                            borderRadius: '4px',
                            backgroundColor: colorValue,
                            border: isDelete ? 'none' : '1px solid #CCCCCC',
                            cursor: 'pointer',
                            backgroundImage:
                                colorValue === 'rgba(255, 255, 255, 0)'
                                    ? 'linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc), linear-gradient(45deg, #ccc 25%, transparent 25%, transparent 75%, #ccc 75%, #ccc)'
                                    : 'none',
                            backgroundSize: '8px 8px',
                            backgroundPosition: '0 0, 4px 4px',
                        }}
                    ></div>
                }
            />
            {!isDelete && <Button icon={XIcon} variant="plain" onClick={handleDeleteColor} />}
        </div>
    );
};
