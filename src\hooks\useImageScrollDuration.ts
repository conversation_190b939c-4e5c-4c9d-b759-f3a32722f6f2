import { useState, useEffect, useRef } from 'react';

export const useImageScrollDuration = () => {
    const imgRef = useRef<HTMLImageElement>(null);
    const [duration, setDuration] = useState('2s');

    useEffect(() => {
        const updateDuration = () => {
            if (!imgRef.current) return;
            const imageHeight = imgRef.current.clientHeight;
            if (imageHeight === 0) return;
            const computedDuration = (2.79 * imageHeight + 420) / 1000;
            setDuration(`${computedDuration}s`);
        };

        const imgElement = imgRef.current;
        if (imgElement) {
            if (imgElement.complete) {
                updateDuration();
            } else {
                imgElement.addEventListener('load', updateDuration);
            }
        }

        window.addEventListener('resize', updateDuration);

        return () => {
            window.removeEventListener('resize', updateDuration);
            imgElement?.removeEventListener('load', updateDuration);
        };
    }, [imgRef]);

    return { imgRef, duration };
};
