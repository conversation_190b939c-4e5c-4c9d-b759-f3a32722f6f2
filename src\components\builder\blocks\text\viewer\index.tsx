/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import { EditorContent } from '@tiptap/react';
import {
    Auto_BlockViewer,
    BlockViewer,
    DATA_SET_AUTO_ID_INNER,
    DATA_SET_VIEWER,
    useBuilderStore,
} from '@giaminhautoketing/auto-builder';
import { useTextEditor } from '@/pages/BuilderPage/hooks';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
    DATA_SET_ATK_ANIMATION,
} from '@/components/builder/constants/constants';

export const TextViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const { editor } = useTextEditor({
        blockId: autoId,
        path: 'content',
        editable: false,
        isUpdateConfigs: true,
    });
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            bpConfigs={bpConfigs}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            configs={configs}
        >
            <EditorContent
                editor={editor}
                onDoubleClick={() => {
                    editor?.setEditable(true);
                    editor?.chain().focus().selectAll().run();
                }}
                onBlur={() => editor?.setEditable(false)}
                spellCheck={false}
                {...{
                    className: 'animate__animated',
                    ...(currentDevice === 'desktop'
                        ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                        : { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile }),
                    [DATA_SET_AUTO_ID_INNER]: autoId,
                    [DATA_SET_ATK_ANIMATION]:
                        (configs as any).animation.type && (configs as any).animation.type !== 'none',
                }}
            />
        </BlockViewer>
    );
};
