import { FC, useState } from 'react';
import './style.scss';
import { BlockStack } from '@shopify/polaris';
import { SettingsColorPicker } from '@/components/builder/settings/SettingsColorPicker';
import { textFontWeightOptions } from '@/components/builder/data/options';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';

interface OverviewStatesProps {
    id: string;
}

type StateType = 'Normal' | 'Hover' | 'Active';

interface StateConfig {
    type: StateType;
    colorPath: string;
    fontWeightPath: string;
}

const states: StateConfig[] = [
    { type: 'Normal', colorPath: 'normal.color', fontWeightPath: 'normal.fontWeight' },
    { type: 'Hover', colorPath: 'hover.color', fontWeightPath: 'hover.fontWeight' },
    { type: 'Active', colorPath: 'active.color', fontWeightPath: 'active.fontWeight' },
];

const StateContent: FC<{ id: string; state: StateConfig }> = ({ id, state }) => (
    <div className="state-content">
        <BlockStack gap="400">
            <SettingsColorPicker path={state.colorPath} blockId={id} label="Text color" isUpdateConfigs={false} />
            <SettingsSelect
                path={state.fontWeightPath}
                blockId={id}
                label="Font weight"
                isUpdateConfigs={false}
                options={textFontWeightOptions}
            />
        </BlockStack>
    </div>
);

const OverviewStates: FC<OverviewStatesProps> = ({ id }) => {
    const [activeButton, setActiveButton] = useState<StateType>('Normal');
    const currentState = states.find((state) => state.type === activeButton)!;

    return (
        <>
            <div className="button-group">
                {states.map((state) => (
                    <button
                        key={state.type}
                        className={`button ${activeButton === state.type ? 'active' : ''}`}
                        onClick={() => setActiveButton(state.type)}
                    >
                        {state.type}
                    </button>
                ))}
            </div>

            <div className="content-state">
                <StateContent id={id} state={currentState} />
            </div>
        </>
    );
};

export default OverviewStates;
