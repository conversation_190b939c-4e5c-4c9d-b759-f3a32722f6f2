import { FC } from 'react';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import { borderTypeOptions } from '@/components/builder/data/options';

interface BorderTypeProps {
    blockId: string;
    isUpdateConfigs?: boolean;
    path: string;
}

export const BorderType: FC<BorderTypeProps> = ({ blockId, isUpdateConfigs, path }) => {
    return (
        <SettingsSelect options={borderTypeOptions} isUpdateConfigs={isUpdateConfigs} path={path} blockId={blockId} />
    );
};
