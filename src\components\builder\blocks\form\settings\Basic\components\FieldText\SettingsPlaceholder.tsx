import { SettingsColorPicker } from '@/components/builder/settings';
import { textAlignOptions, textDirectionOptions, textFontWeightOptions } from '@/components/builder/data/options';
import { SettingsSwitchTab, SettingsFontFamily, SettingsInput, SettingsSelect } from '@/components/builder/settings';
import { Box } from '@shopify/polaris';
import { BlockStack } from '@shopify/polaris';
import { FC } from 'react';

interface SettingsPlaceholderProps {
    blockId: string;
    path: string;
    isUpdateConfigs: boolean;
}

export const SettingsPlaceholder: FC<SettingsPlaceholderProps> = ({ blockId, path, isUpdateConfigs }) => {
    return (
        <BlockStack gap="400">
            <Box paddingBlock="300" paddingInline="200" borderWidth="025" borderColor="border" borderRadius="200">
                <BlockStack gap="400">
                    <SettingsFontFamily
                        blockId={blockId}
                        path={`${path}.placeholderFontFamily`}
                        isUpdateConfigs={isUpdateConfigs}
                    />

                    <SettingsSelect
                        label="Font Weight"
                        blockId={blockId}
                        path={`${path}.placeholderFontWeight`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textFontWeightOptions}
                    />
                    <SettingsInput
                        label="Font Size"
                        blockId={blockId}
                        path={`${path}.placeholderFontSize`}
                        isUpdateConfigs={isUpdateConfigs}
                        inputProps={{
                            min: 4,
                            suffix: 'px',
                        }}
                    />
                    <SettingsSwitchTab
                        label="Text Align"
                        blockId={blockId}
                        path={`${path}.placeholderTextAlign`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textAlignOptions}
                    />

                    <SettingsSwitchTab
                        label="Text Direction"
                        blockId={blockId}
                        path={`${path}.placeholderTextDirection`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={textDirectionOptions}
                    />
                    <SettingsColorPicker
                        label="Text Color"
                        blockId={blockId}
                        path={`${path}.placeholderColor`}
                        isUpdateConfigs={isUpdateConfigs}
                    />
                </BlockStack>
            </Box>
        </BlockStack>
    );
};
