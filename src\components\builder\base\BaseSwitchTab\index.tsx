import { ComponentType, FC, useCallback } from 'react';
import { Icon, IconSource, InlineStack, Tooltip } from '@shopify/polaris';
import './style.scss';
import clsx from 'clsx';

export interface OptionProps {
    id: string;
    icon?: ComponentType | IconSource;
    contentTooltip: string;
    isPolarisIcon?: boolean;
}

export interface BaseSwitchTabProps {
    options: OptionProps[];
    valueData: string;
    onChange: (value: string) => void;
    containerClassName?: string;
    label?: string;
    fullWidth?: boolean;
    noTooltip?: boolean;
}

export const BaseSwitchTab: FC<BaseSwitchTabProps> = ({
    options,
    valueData,
    onChange,
    containerClassName,
    label,
    fullWidth,
    noTooltip,
}) => {
    const handleChange = useCallback(
        (value: string) => {
            onChange(value);
        },
        [onChange],
    );

    return (
        <InlineStack wrap={false} blockAlign="center" gap="150">
            <div className={clsx('switch-tabs__container', containerClassName)}>
                <ul
                    className={clsx('switch-tabs__container--list', {
                        'switch-tabs__container--full-width': fullWidth,
                    })}
                    aria-label={label}
                >
                    {options.map(({ id, contentTooltip, icon: IconComponent, isPolarisIcon }) => {
                        const isActive = valueData === id;
                        return (
                            <li key={id}>
                                <Tooltip
                                    key={id}
                                    content={contentTooltip}
                                    dismissOnMouseOut
                                    activatorWrapper="div"
                                    {...(noTooltip && { active: false })}
                                >
                                    <button
                                        type="button"
                                        className={`switch-tabs__container--list--button${isActive ? ' active' : ''}`}
                                        onClick={() => handleChange(id)}
                                    >
                                        {isPolarisIcon ? (
                                            <Icon source={IconComponent as IconSource} tone="base" />
                                        ) : IconComponent ? (
                                            <IconComponent />
                                        ) : (
                                            contentTooltip
                                        )}
                                    </button>
                                </Tooltip>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </InlineStack>
    );
};
