import { useState, useEffect } from 'react';
import { AxiosRequestConfig } from 'axios';
import { httpRequest } from '@/configs';
import { sleep } from '@/utils';

interface UseQueryOptions<P, T> {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: P;
    headers?: AxiosRequestConfig['headers'];
    enabled?: boolean;
    sleepTime?: number;
    extraDeps?: unknown;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
}

interface UseQueryResult<T> {
    data: T | null;
    isLoading: boolean;
    error: Error | null;
    refetch: () => Promise<void>;
}

export function useQuery<T, P = unknown>({
    url,
    method = 'GET',
    body,
    headers,
    enabled = true,
    sleepTime = 0,
    extraDeps,
    onSuccess,
    onError,
}: UseQueryOptions<P, T>): UseQueryResult<T> {
    const [data, setData] = useState<T | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        const startTime = Date.now();
        try {
            const response = await httpRequest({
                url,
                method,
                data: body,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers,
                },
            });
            const elapsedTime = Date.now() - startTime;
            if (elapsedTime < sleepTime) {
                await sleep(sleepTime - elapsedTime);
            }
            setData(response.data);
            onSuccess?.(response.data);
        } catch (err) {
            setError(err instanceof Error ? err : new Error('An unknown error occurred'));
            onError?.(err instanceof Error ? err : new Error('An unknown error occurred'));
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (enabled) {
            fetchData();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [url, enabled, extraDeps]);

    return { data, isLoading, error, refetch: fetchData };
}
