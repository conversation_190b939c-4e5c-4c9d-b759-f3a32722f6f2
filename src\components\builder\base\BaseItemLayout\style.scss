:root {
    --atk-grid-gap: var(--p-space-150);
    --atk-grid-gap-200: var(--p-space-200);
}

.base-item-layout {
    display: grid;
    grid-template-columns: minmax(0px, auto) minmax(0px, auto);
    grid-template-rows: 1fr;
    align-items: start;
    justify-content: space-between;
    gap: var(--atk-grid-gap);
    &--column {
        grid-template-columns: 1fr;
    }
    .base-item-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        word-break: break-word;
        overflow-wrap: break-word;
        height: 100%;
    }

    .base-select-container {
        min-width: 140px;
    }
}

.settings-grid-gap-200 {
    gap: var(--atk-grid-gap-200);
}
