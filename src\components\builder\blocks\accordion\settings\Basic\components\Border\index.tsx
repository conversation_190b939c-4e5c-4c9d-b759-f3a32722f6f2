import { FC } from 'react';
import { Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { SettingBorder } from '@/components/builder/settings/SettingBorder';

interface BorderProps {
    id: string;
    label: string;
}

export const Border: FC<BorderProps> = ({ id, label }) => {
    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <SettingBorder path="border" blockId={id} label={label} />
        </BaseCollapse>
    );
};
