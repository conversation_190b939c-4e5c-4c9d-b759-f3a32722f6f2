import deepmerge from 'deepmerge';
import * as objectPath from 'object-path-immutable';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { Breakpoint } from './types';

export function getResponsiveValue<T>(blockData: Auto_BlockData, path: string, breakpoint: Breakpoint): T {
    const desktopValue = objectPath.get(blockData, `bpConfigs.desktop.${path}`);
    const mobileValue = objectPath.get(blockData, `bpConfigs.mobile.${path}`);

    if (breakpoint === 'mobile') {
        if (mobileValue === undefined) {
            return desktopValue;
        }
        if (typeof desktopValue === 'object' && desktopValue !== null) {
            return deepmerge(desktopValue, mobileValue, {
                arrayMerge: (_, sourceArray) => {
                    return sourceArray;
                },
            });
        }

        return mobileValue;
    }
    return desktopValue;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function cssObjectToString(cssObj: Record<string, any>, indent = ''): string {
    let cssString = '';

    Object.keys(cssObj).forEach((key) => {
        const value = cssObj[key];

        if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
            if (key.startsWith('@media')) {
                cssString += `${indent}${key} {\n`;
                cssString += cssObjectToString(value, indent + '  ');
                cssString += `${indent}}\n\n`;
            } else {
                cssString += `${indent}${key} {\n`;
                Object.keys(value).forEach((prop) => {
                    if (value[prop] !== undefined && value[prop] !== null) {
                        cssString += `${indent}  ${kebabCase(prop)}: ${value[prop]};\n`;
                    }
                });

                cssString += `${indent}}\n\n`;
            }
        }
    });

    return cssString;
}

function kebabCase(str: string): string {
    return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();
}

export function mapObjectKeys<T>(
    obj: Record<string, T>,
    callback: (key: string, value: T, object: Record<string, T>) => string,
): Record<string, T> {
    const result: Record<string, T> = {};

    Object.keys(obj).forEach((key) => {
        const newKey = callback(key, obj[key], obj);
        result[newKey] = obj[key];
    });

    return result;
}
