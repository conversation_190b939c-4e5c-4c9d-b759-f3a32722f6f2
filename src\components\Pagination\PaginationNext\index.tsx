import { FC } from 'react';
import { css } from '@emotion/react';
import { PaginationControl } from '../PaginationControl';
import { usePaginationContext } from '../Pagination.context';
import { ReactComponent as NextIcon } from '@/assets/svgs/pagination-next.svg';

export const PaginationNext: FC = () => {
    const ctx = usePaginationContext();
    return (
        <PaginationControl
            icon={<NextIcon />}
            disabled={!ctx.hasNext}
            css={css`
                .Polaris-Button {
                    padding-left: 12px;
                    flex-direction: row-reverse;
                }
                .Polaris-Button__Icon {
                    display: flex;
                    margin-top: 0.5px;
                    ${!ctx.hasNext &&
                    css`
                        color: rgba(204, 204, 204, 1);
                    `}
                }
            `}
            onClick={ctx.onNext}
        >
            Next
        </PaginationControl>
    );
};
