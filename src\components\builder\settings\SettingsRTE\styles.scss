.tiptap.ProseMirror {
    outline: none;
    ul,
    ol {
        padding: 0 1rem;
    }
    span {
        font-size: var(--fontSizeDesktop);
        line-height: var(--lineHeightDesktop);
        color: var(--colorDesktop);
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
        font-size: inherit;
        font-weight: inherit;
    }
}

.spotlight {
    display: inline-flex;
    align-items: center;
    background-color: #3064ce;
    color: #fff;
}

.rte {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 8px;
    overflow: hidden;
    &-controls {
        &--top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 4px;
            background: #f7f7f7;
        }
        &--bottom {
            display: flex;
            align-items: center;
            column-gap: 8px;
            padding: 0 4px 4px 4px;
            background: #f7f7f7;
        }
    }
    &-content {
        width: 100%;
        height: 200px;
        padding: 4px 12px;
        background: #fff;
        border-top: 1px solid #e1e3e5;
        overflow: hidden auto;
    }
    &-tool {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36px;
        width: 36px;
        &.active {
            background-color: #e3e3e3;
            &:hover {
                background-color: #e3e3e3;
            }
        }
    }
}
