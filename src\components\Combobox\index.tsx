import { ComboboxRoot } from './ComboboxRoot';
import { ComboboxTrigger } from './ComboboxTrigger';
import { ComboboxContent } from './ComboboxContent';
import { ComboboxOptions } from './ComboboxOptions';

export const Combobox = Object.assign(ComboboxRoot, {
    Trigger: ComboboxTrigger,
    Content: ComboboxContent,
    Options: ComboboxOptions,
});

export type { ComboboxRootProps, ComboboxTriggerProps, ComboboxContentProps, ComboboxOptionsProps } from './types';
