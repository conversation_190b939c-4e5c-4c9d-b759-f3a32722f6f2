import { FC } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingBorder } from '@/components/builder/settings/SettingBorder';

interface BorderProps {
    id: string;
}

export const Border: FC<BorderProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Border"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <SettingBorder isUpdateConfigs={false} path="borderButton" blockId={id} label="Border" />
        </BaseCollapse>
    );
};
