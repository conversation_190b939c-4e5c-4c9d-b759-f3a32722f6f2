import { BlockStack, InlineStack, Button, Box } from '@shopify/polaris';
import UploadImage from '@/assets/images/upload-img.png?url';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import {
    backgroundAttachmentOptions,
    backgroundFillOptions,
    backgroundPositionOptions,
    backgroundRepeatOptions,
} from '@/components/builder/data/options';
import { ExchangeIcon, DeleteIcon } from '@shopify/polaris-icons';
import { FC } from 'react';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useMediaManager } from '@/pages/BuilderPage/components/MediaManager/useMediaManager';
interface BackgroundImageSettingsProps {
    blockId: string;
    isUpdateConfigs?: boolean;
    path: string;
    imageUrl: string;
    setActive: (active: boolean) => void;
}

export const BackgroundImageSettings: FC<BackgroundImageSettingsProps> = ({
    blockId,
    isUpdateConfigs,
    path,
    imageUrl,
    setActive,
}) => {
    const setOpen = useMediaManager((state) => state.setOpen);
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const onAddImage = (url: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.image.url`, url);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.image.url`, url);
        }
    };

    const openMediaManager = () => {
        setActive(false);
        setOpen(
            { mode: 'manager' },
            {
                currentTab: 'unplash',
                sortBy: 'all',
                allows: 'image',
                onSelect(media) {
                    if (!Array.isArray(media)) {
                        onAddImage(media.url);
                    }
                },
            },
        );
    };

    const handleRemoveImage = () => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.image.url`, '');
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.image.url`, '');
        }
    };

    return (
        <div onClick={(e) => e.stopPropagation()}>
            <Box>
                <BlockStack gap="200">
                    <div
                        css={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '165px',
                            background: `linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), url(${
                                imageUrl || UploadImage
                            })`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat',
                            border: '1px solid #BBBBBB',
                            borderRadius: '8px',
                        }}
                    >
                        {imageUrl === '' && (
                            <button
                                onClick={openMediaManager}
                                css={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: '#1F9CFC',
                                    borderRadius: '6px',
                                    padding: '6px 12px',
                                    color: '#fff',
                                    fontSize: '12px',
                                    cursor: 'pointer',
                                    border: 'none',
                                }}
                            >
                                Choose image
                            </button>
                        )}
                    </div>
                    {imageUrl !== '' && (
                        <>
                            <InlineStack blockAlign="center" align="end" gap="200">
                                <Button variant="tertiary" icon={ExchangeIcon} onClick={openMediaManager}></Button>
                                <Button variant="tertiary" icon={DeleteIcon} onClick={handleRemoveImage}></Button>
                            </InlineStack>
                            <div className="bg-color-picker-image__settings">
                                <BlockStack gap="200">
                                    <SettingsSwitchTab
                                        path={`${path}.image.repeat`}
                                        blockId={blockId}
                                        options={backgroundRepeatOptions}
                                        isUpdateConfigs={false}
                                        label="Repeat"
                                    />
                                    <SettingsSelect
                                        path={`${path}.image.fill`}
                                        blockId={blockId}
                                        options={backgroundFillOptions}
                                        label="Fill"
                                        isUpdateConfigs={false}
                                    />
                                    <SettingsSelect
                                        path={`${path}.image.attachment`}
                                        blockId={blockId}
                                        options={backgroundAttachmentOptions}
                                        label="Attachment"
                                        isUpdateConfigs={false}
                                    />
                                    <SettingsSelect
                                        path={`${path}.image.position`}
                                        blockId={blockId}
                                        options={backgroundPositionOptions}
                                        label="Position"
                                        isUpdateConfigs={false}
                                    />
                                </BlockStack>
                            </div>
                        </>
                    )}
                </BlockStack>
            </Box>
        </div>
    );
};
