.shape-manage {
    &__socials {
        height: 100%;
        width: 100% !important;
    }

    &__socials,
    &__pattern,
    &__icons,
    &__background,
    &__arrows {
        .ReactVirtualized__Grid__innerScrollContainer {
            margin: auto;
        }
    }

    &__content {
        position: relative;
        width: 99.8%;
        margin: auto;
        height: 100%;
        padding: 1.25rem 1rem;
        &__search {
            width: 330px;
            position: absolute;
            top: 20px;
            right: 16px;
            margin-bottom: 1rem;
            .Polaris-TextField__Suffix {
                margin-right: 5px;
                .Polaris-Button {
                    background: transparent !important;
                    margin: 0;
                }
            }
        }
        .Polaris-Tabs__Outer {
            > .Polaris-Box {
                padding: 0;
                margin-bottom: 1.25rem;
                ul {
                    padding: 0;
                    column-gap: 0.5rem;
                    li {
                        .Polaris-Tabs__Tab {
                            height: 2rem;
                        }
                    }
                }
            }
        }
        svg {
            fill: #919191;
        }
    }
    &__item {
        width: 130px;
        height: 130px;
        border-radius: 8px;
        cursor: pointer;
        padding: 1.71875rem;
        position: relative;
        &__svg {
            width: 75px;
            height: 75px;
            background: #919191;
            mask-size: 100% 100%;
            mask-repeat: no-repeat;
            mask-position: center;
            mask-composite: intersect;
        }
        &__delete {
            position: absolute;
            top: 4px;
            right: 4px;
            visibility: hidden;
            background: #0000000d;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 2;
            svg {
                width: 20px;
                height: 20px;
                fill: #919191 !important;
            }
            &:hover {
                svg {
                    fill: #000 !important;
                }
            }
        }
        &:hover {
            .shape-manage__item__svg {
                background: #6086f2;
            }
            .shape-manage__item__delete {
                visibility: visible;
            }
        }
        &--active {
            svg {
                fill: #6086f2;
            }
        }
    }
}
.shape-manage {
    position: relative;
    &:hover {
        .shape-manage__item__delete {
            visibility: visible;
        }
    }
}
