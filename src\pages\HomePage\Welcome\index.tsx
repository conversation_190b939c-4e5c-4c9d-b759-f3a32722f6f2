import { FC, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Box, Card, Grid, Icon, SkeletonDisplayText, Text } from '@shopify/polaris';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { welcomeConfigs } from './configs';
import { WelcomeContainer, WelcomeContent, WelcomeHeader } from './styled';

export const Welcome: FC = () => {
    const { isAuthenticated, appSettings, getAppSettings } = useAppStore();
    useEffect(() => {
        if (isAuthenticated) {
            getAppSettings();
        }
    }, [isAuthenticated, getAppSettings]);
    return (
        <WelcomeContainer>
            <Card>
                <WelcomeHeader>
                    <Box>
                        <Text as="h2" variant="headingSm">
                            {appSettings?.shopName ? (
                                `Hi ${appSettings?.shopName} 👋`
                            ) : (
                                <SkeletonDisplayText size="small" />
                            )}
                        </Text>
                    </Box>
                    <Text tone="subdued" as="span">
                        Welcome to Autoketing Builder
                    </Text>
                </WelcomeHeader>
                <WelcomeContent>
                    <Grid>
                        {welcomeConfigs.map((item, index) => (
                            <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }}>
                                <Link to={item.link}>
                                    <Box>
                                        <Text as="h2" variant="headingSm">
                                            {item.title}
                                        </Text>
                                        <Text tone="subdued" as="span">
                                            {item.des}
                                        </Text>
                                    </Box>
                                    <Icon source={item.icon} />
                                </Link>
                            </Grid.Cell>
                        ))}
                    </Grid>
                </WelcomeContent>
            </Card>
        </WelcomeContainer>
    );
};
