import { Box } from '@shopify/polaris';
import styled from '@emotion/styled';
import isPropValid from '@emotion/is-prop-valid';

interface StyleProps {
    iconSize?: number;
    display?: 'flex' | 'block' | 'inline-block' | 'none';
    maxHeight?: number;
    fillColor?: string;
    boxShadow?: string;
    cornerRadius?: string;
    padding?: string;
    backgroundColor?: string;
}

const baseBoxStyles = ({
    boxShadow = 'inherit',
    cornerRadius = 'inherit',
    padding = 'inherit',
    backgroundColor = 'inherit',
}: StyleProps) => `
  box-shadow: ${boxShadow};
  border-radius: ${cornerRadius};
  padding: ${padding};
  background-color: ${backgroundColor};
`;

export const Wrapper = styled(Box, { shouldForwardProp: (prop) => isPropValid(prop) })<StyleProps>`
    ${baseBoxStyles}
    .Polaris-Link--removeUnderline:hover {
        text-decoration: none;
    }
`;

export const ChannelItemWrapper = styled(Box, { shouldForwardProp: (prop) => isPropValid(prop) })<StyleProps>`
    ${baseBoxStyles}
    cursor: pointer;
    &:hover {
        background-color: var(--p-color-bg-surface-hover);
        box-shadow: var(--p-shadow-button-hover);
    }
`;

export const ChannelIconContainer = styled(Box, { shouldForwardProp: (prop) => isPropValid(prop) })<StyleProps>`
    display: ${(props) => props.display};

    .Polaris-Icon {
        width: ${(props) => props.iconSize}px;
        height: ${(props) => props.iconSize}px;

        svg {
            fill: ${(props) => props.fillColor};
        }
    }
`;

export const ChannelLinkContainer = styled(Box, { shouldForwardProp: (prop) => isPropValid(prop) })<StyleProps>`
    max-height: ${(props) => props.maxHeight}px;
`;
