import { useState } from 'react';
import clsx from 'clsx';
import { useQuery, useScrollToLoadmore } from '@/hooks';
import { MediaGrid } from '../../components';
import { useMediaManager } from '../../useMediaManager';
import { MediaType, UnsplashResponse } from '../../types';

export const Unplash = () => {
    const [items, setItems] = useState<MediaType[]>([]);
    const [hasMore, setHasMore] = useState(true);
    const page = useMediaManager((state) => state.page);
    const sortBy = useMediaManager((state) => state.sortBy);
    const setPage = useMediaManager((state) => state.setPage);

    const LIMIT = 20;
    const API_URL = 'https://api.unsplash.com/search/photos';

    const { isLoading: loading } = useQuery<UnsplashResponse>({
        url: clsx(
            API_URL,
            `?page=${page}`,
            `&per_page=${LIMIT}`,
            `&query=${sortBy}`,
            `&client_id=${import.meta.env.VITE_UNPLASH_KEY}`,
        ).replace(/\s+/g, ''),
        method: 'GET',
        sleepTime: 300,
        onSuccess(data) {
            const { results, total_pages } = data;
            const newItems = results.map(({ id, alt_description, urls, width, height }) => ({
                id,
                alt: alt_description,
                url: urls.small,
                width: width.toString(),
                height: height.toString(),
                mimeType: 'image/jpeg',
            }));
            setItems(page === 1 ? newItems : (prev) => [...prev, ...newItems]);
            setHasMore(page < total_pages);
        },
    });

    const loadMoreItems = () => {
        if (loading || !hasMore) return;
        setPage(page + 1);
    };

    const { containerRef } = useScrollToLoadmore({
        hasMore,
        loading,
        loadmore: loadMoreItems,
        resetDependencies: [sortBy],
    });

    return <MediaGrid items={items} loading={loading} containerRef={containerRef} />;
};
