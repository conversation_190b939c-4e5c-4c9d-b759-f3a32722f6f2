import { FC } from 'react';
import { MenuHorizontalIcon } from '@shopify/polaris-icons';
import { usePaginationContext } from '../Pagination.context';
import { PaginationControl } from '../PaginationControl';

export const PaginationItems: FC = () => {
    const ctx = usePaginationContext();

    const items = ctx.range.map((page, index) => {
        if (page === 'dots') {
            return (
                <PaginationControl
                    key={index}
                    icon={MenuHorizontalIcon}
                    disabled
                    variant="plain"
                    css={{ width: '32px', height: '32px' }}
                />
            );
        }
        return (
            <PaginationControl key={index} disabled={ctx.active === page} onClick={() => ctx.onChange(page)}>
                {page.toString()}
            </PaginationControl>
        );
    });

    return <>{items}</>;
};
