export interface Template {
    id: string;
    title: string;
    thumbnail: string;
    handle: string;
    status: string;
    jsonData: string;
    htmlData: string;
    created_at: string;
    updated_at: string;
    pageType: string;
    templateTypeId: string;
    updatedAt: string;
    url: string;
}

export interface TemplateListResponse {
    result: {
        data: {
            list: Template[];
            total: number;
        };
    };
}

export interface PageTypeResponse {
    result: {
        data: Array<{
            id: string;
            typeName: string;
        }>;
    };
}

export interface IndustryCategoryResponse {
    result: {
        data: Array<{
            id: string;
            name: string;
        }>;
    };
}
