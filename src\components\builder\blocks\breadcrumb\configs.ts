import { genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const BreadcrumbSettingsConfigs = [
    {
        id: genRandomBlockId(),
        cname: 'breadcrumb',
        label: 'Breadcrumb',
        type: 'breadcrumb',
        bpConfigs: {
            desktop: {
                backgroundBreadcrumb: {
                    type: 'color',
                    color: '#ffffff',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
                width: { val: '350', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#000000',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '2.5',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    justifyContent: 'left',
                    textShadow: undefined,
                    fontStyle: 'default',
                },
                borderBreadcrumb: {
                    color: '#ccc',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                spaceBreadcrumb: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
                spaceBetweenTextAndIcon: {
                    val: '2',
                    unit: 'px',
                },
                breadcrumbAlignment: {
                    alignSelf: 'start',
                    justifySelf: 'start',
                },
                breadcrumbSpacing: {
                    margin: {
                        top: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                    },
                    padding: {
                        top: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '10', unit: 'px' },
                        right: { val: '10', unit: 'px' },
                    },
                },
            },
            mobile: {
                backgroundBreadcrumb: {
                    type: 'color',
                    color: '#FFFFFF',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
                width: { val: '350', unit: 'px' },
                position: {
                    positionType: 'default',
                    stickTo: {
                        stickToType: 'top',
                        top: { val: '0', unit: '%' },
                        bottom: { val: '0', unit: '%' },
                    },
                },
                boxShadow: undefined,
                typography: {
                    color: '#000000',
                    fontSize: {
                        val: '16',
                        unit: 'px',
                    },
                    fontWeight: '400',
                    fontFamily: 'Roboto, sans-serif',
                    lineHeight: {
                        val: '2.5',
                    },
                    letterSpacing: {
                        val: '0',
                        unit: 'px',
                    },
                    textTransform: 'default',
                    textDecoration: 'default',
                    textAlign: 'center',
                    justifyContent: 'left',
                    textShadow: undefined,
                    fontStyle: 'default',
                },
                borderBreadcrumb: {
                    color: '#ccc',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'default',
                    radius: {
                        'top-left': { val: '5', unit: 'px' },
                        'top-right': { val: '5', unit: 'px' },
                        'bottom-right': { val: '5', unit: 'px' },
                        'bottom-left': { val: '5', unit: 'px' },
                    },
                },
                spaceBreadcrumb: {
                    padding: {
                        top: { val: '2', unit: 'px' },
                        right: { val: '12', unit: 'px' },
                        bottom: { val: '2', unit: 'px' },
                        left: { val: '12', unit: 'px' },
                    },
                },
                spaceBetweenTextAndIcon: {
                    val: '2',
                    unit: 'px',
                },
                breadcrumbAlignment: {
                    alignSelf: 'start',
                    justifySelf: 'start',
                },
                breadcrumbSpacing: {
                    margin: {
                        top: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                    },
                    padding: {
                        top: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                    },
                },
            },
        },
        configs: {
            content: {
                type: 'text-only',
                text: 'Breadcrumb',
                heading: '<h1>Heading</h1>',
                direction: 'row',
                reverse: false,
                spacing: {
                    val: '0',
                    unit: 'px',
                },
                beforeEllipsis: 0,
                afterEllipsis: 0,
                showHome: true,
                showSeparator: true,
                showLastItem: true,
                isCollapse: false,
                hideWhenNotParent: false,
                separatorIcon: {
                    icon: `<svg fill="#000000" height="100%" width="100%" version="1.1" id="Icons" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32" xml:space="preserve" transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="0.064"></g><g id="SVGRepo_iconCarrier"> <g> <path d="M12.5,28c-1,0-1.8-0.4-2.5-1c-1.4-1.4-1.4-3.6,0-5l5.7-5.8c0.1-0.1,0.1-0.2,0-0.3L10,10.1c-1.4-1.4-1.4-3.6,0-5 c0.7-0.7,1.6-1,2.5-1c0,0,0,0,0,0c1,0,1.8,0.4,2.5,1l9.3,9.4c0.9,0.9,0.9,2.3,0,3.1l-9.3,9.4C14.4,27.6,13.5,28,12.5,28 C12.5,28,12.5,28,12.5,28z"></path> </g> </g></svg>`,
                    color: 'rgba(0, 0, 0, 0.45)',
                    iconSize: { val: '20', unit: 'px' },
                },
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            customCSS: {
                enable: false,
                className: '',
                style: '',
            },
            events: {},
        },
        overlay: {
            desktop: {
                width: 300,
                height: 40,
            },
            mobile: {
                width: 300,
                height: 40,
            },
        },
    },
];
