import { useMemo } from 'react';
import { Box, Button, InlineStack, Spinner } from '@shopify/polaris';

import { But<PERSON>Viewer } from '@/components/builder/blocks/button/viewer';
import { PageViewer } from '@/components/builder/blocks/page/viewer';
import { SectionViewer } from '@/components/builder/blocks/section/viewer';
import { TextViewer } from '@/components/builder/blocks/text/viewer';
import { ImageViewer } from '@/components/builder/blocks/image/viewer';
import { VideoViewer } from '@/components/builder/blocks/video/viewer';
import { Sha<PERSON>Viewer } from '@/components/builder/blocks/shape/viewer';
import { FormViewer } from '@/components/builder/blocks/form/viewer';
import { HtmlCodeViewer } from '@/components/builder/blocks/htmlCode/viewer';
import { TabsViewer } from '@/components/builder/blocks/tabs/viewer';
import { AccordionViewer } from '@/components/builder/blocks/accordion/viewer';
import { B<PERSON>crumbViewer } from '@/components/builder/blocks/breadcrumb/viewer';

// import { cssPropertyDefinitions } from '@/components/builder/data/css';
import { useSideBar } from '@/pages/BuilderPage/hooks/useSideBar';
import { SideBar } from '@/pages/BuilderPage/components/SideBar/SideBarMenu';
import { Drawer } from '@/pages/BuilderPage/components/SideBar/Drawer';
import { ButtonSideBars } from '@/pages/BuilderPage/configs/sidebar';
import emptyBuilder from '@/assets/svgs/empty-builder.svg?url';
import waterMark from '@/assets/images/autoketing-watermark.png';
import { usePageBuilder } from '@/pages/BuilderPage/hooks/editor/usePageBuilder';
import { Editor, useBlockStore } from '@giaminhautoketing/auto-builder';
import { blockStructure } from '@/components/builder/data/structure';
import { usePageStyles } from '@/pages/BuilderPage/hooks/editor/usePageStyles';
import { useAppStore } from '@/stores/appStore/useAppStore';

const editorViews = {
    page: PageViewer,
    section: SectionViewer,
    button: ButtonViewer,
    text: TextViewer,
    image: ImageViewer,
    shape: ShapeViewer,
    'html-code': HtmlCodeViewer,
    video: VideoViewer,
    form: FormViewer,
    accordion: AccordionViewer,
    breadcrumb: BreadcrumbViewer,
    tabs: TabsViewer,
};

export const MainContent = () => {
    const { menuActive, handleSelectMenu, isDrawerOpen, closeDrawer, setIsDrawerOpen } = useSideBar();
    const { blocks, hierarchy, mainContentWidth } = usePageBuilder();
    const setBlocks = useBlockStore((state) => state.setBlocks);
    const setHierarchy = useBlockStore((state) => state.setHierarchy);
    usePageStyles();
    const requestsStatus = useAppStore((state) => state.requestsStatus);

    const editorContent = useMemo(() => {
        const isLoading = requestsStatus['getDetailPage']?.status === 'loading';
        const hasContent = blocks && hierarchy && Object.keys(blocks).length > 0 && Object.keys(hierarchy).length > 0;
        if (isLoading) {
            return (
                <div className="builder-container__content__loading">
                    <Spinner size="large" />
                </div>
            );
        }
        if (hasContent) {
            return <Editor cssPropertyDefinitions={{}} views={editorViews} />;
        }
        return (
            <div className="builder-container__content__empty-builder">
                <img src={emptyBuilder} alt="Empty builder" />
                <p className="builder-container__content__empty-builder__text">
                    Start building your page by adding elements from sidebar
                </p>
                <Button
                    onClick={() => {
                        setBlocks(blockStructure.blocks);
                        setHierarchy(blockStructure.hierarchy);
                        handleSelectMenu('elements', ButtonSideBars.items);
                    }}
                    variant="primary"
                >
                    Add element
                </Button>
                <img src={waterMark} alt="Watermark" className="builder-container__content__empty-builder__watermark" />
                <img
                    src={waterMark}
                    alt="Watermark"
                    className="builder-container__content__empty-builder__watermark2"
                />
            </div>
        );
    }, [requestsStatus, blocks, hierarchy, handleSelectMenu, setBlocks, setHierarchy]);

    return (
        <>
            <div className="builder-container__content">
                <div className="builder-container__content__editor-layout">
                    <Box
                        background="bg-fill"
                        borderColor="border"
                        borderStyle="solid"
                        borderInlineEndWidth="025"
                        padding="200"
                        minWidth="48px"
                        zIndex="3"
                        id="left-sidebar"
                    >
                        <SideBar
                            items={ButtonSideBars.items}
                            activeItem={menuActive?.id ?? null}
                            onSelectItem={(id) => handleSelectMenu(id, ButtonSideBars.items)}
                        />
                    </Box>
                    <InlineStack gap="0">
                        <div
                            id="sidebar-drawer"
                            className={`builder-container__content__sidebar-drawer ${
                                isDrawerOpen ? 'builder-container__content__sidebar-drawer--active' : ''
                            }`}
                        >
                            <Drawer
                                activeItem={menuActive}
                                onClose={closeDrawer}
                                setIsDrawerOpen={setIsDrawerOpen}
                                handleSelectMenu={handleSelectMenu}
                            />
                        </div>

                        <main
                            className="builder-container__content__main-content"
                            style={{ '--main-content-width': mainContentWidth } as React.CSSProperties}
                        >
                            <div
                                style={{
                                    // transform: `scale(${zoomLevel})`,
                                    //transformOrigin: 'center center',
                                    height: '100%',
                                    overflowY: 'auto',
                                }}
                            >
                                {editorContent}
                            </div>
                        </main>
                    </InlineStack>
                </div>
            </div>
        </>
    );
};
