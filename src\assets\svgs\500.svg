<svg width="240" height="240" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_2)">
<path d="M240 120C240 186.324 186.324 240 120 240C53.676 240 0 186.324 0 120C0 53.676 53.676 0 120 0C186.324 0 240 53.838 240 120Z" fill="#F0F1F2"/>
<g filter="url(#filter0_d_1_2)">
<path d="M224 40H16C14.9391 40 13.9217 40.4214 13.1716 41.1716C12.4214 41.9217 12 42.9391 12 44V194C12 195.061 12.4214 196.078 13.1716 196.828C13.9217 197.579 14.9391 198 16 198H224C225.061 198 226.078 197.579 226.828 196.828C227.579 196.078 228 195.061 228 194V44C228 42.9391 227.579 41.9217 226.828 41.1716C226.078 40.4214 225.061 40 224 40Z" fill="white"/>
<path d="M214 72H74C73.4696 72 72.9609 72.2107 72.5858 72.5858C72.2107 72.9609 72 73.4696 72 74V184C72 184.53 72.2107 185.039 72.5858 185.414C72.9609 185.789 73.4696 186 74 186H214C214.53 186 215.039 185.789 215.414 185.414C215.789 185.039 216 184.53 216 184V74C216 73.4696 215.789 72.9609 215.414 72.5858C215.039 72.2107 214.53 72 214 72Z" fill="#F0F1F2"/>
<path d="M114 52H76C74.9391 52 73.9217 52.4214 73.1716 53.1716C72.4214 53.9217 72 54.9391 72 56C72 57.0609 72.4214 58.0783 73.1716 58.8284C73.9217 59.5786 74.9391 60 76 60H114C115.061 60 116.078 59.5786 116.828 58.8284C117.579 58.0783 118 57.0609 118 56C118 54.9391 117.579 53.9217 116.828 53.1716C116.078 52.4214 115.061 52 114 52Z" fill="#B6BABF"/>
<path d="M60 40H16C14.9391 40 13.9217 40.4214 13.1716 41.1716C12.4214 41.9217 12 42.9391 12 44V194C12 195.061 12.4214 196.078 13.1716 196.828C13.9217 197.579 14.9391 198 16 198H60V40Z" fill="#00735C"/>
<path d="M22 58.66C24.057 58.66 24.171 56 26.571 56C29.029 56 29.029 59 31.429 59C33.829 59 33.943 56.34 36.229 56.34C38.514 56.34 38.457 59 40.8 59C43.143 59 43.314 56.34 45.543 56.34C47.77 56.34 48.57 59 50 59" stroke="#00996E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<path d="M110.58 146.477C108.17 146.477 106.023 146.034 104.136 145.148C102.261 144.261 100.773 143.04 99.6705 141.483C98.5682 139.926 97.9943 138.142 97.9489 136.131H105.108C105.188 137.483 105.756 138.58 106.812 139.42C107.869 140.261 109.125 140.682 110.58 140.682C111.739 140.682 112.761 140.426 113.648 139.915C114.545 139.392 115.244 138.67 115.744 137.75C116.256 136.818 116.511 135.75 116.511 134.545C116.511 133.318 116.25 132.239 115.727 131.307C115.216 130.375 114.506 129.648 113.597 129.125C112.688 128.602 111.648 128.335 110.477 128.324C109.455 128.324 108.46 128.534 107.494 128.955C106.54 129.375 105.795 129.949 105.261 130.676L98.6989 129.5L100.352 111.091H121.693V117.125H106.438L105.534 125.869H105.739C106.352 125.006 107.278 124.29 108.517 123.722C109.756 123.153 111.142 122.869 112.676 122.869C114.778 122.869 116.653 123.364 118.301 124.352C119.949 125.341 121.25 126.699 122.205 128.426C123.159 130.142 123.631 132.119 123.619 134.358C123.631 136.71 123.085 138.801 121.983 140.631C120.892 142.449 119.364 143.881 117.398 144.926C115.443 145.96 113.17 146.477 110.58 146.477ZM142.455 146.767C139.523 146.756 137 146.034 134.886 144.602C132.784 143.17 131.165 141.097 130.028 138.381C128.903 135.665 128.347 132.398 128.358 128.58C128.358 124.773 128.92 121.528 130.045 118.847C131.182 116.165 132.801 114.125 134.903 112.727C137.017 111.318 139.534 110.614 142.455 110.614C145.375 110.614 147.886 111.318 149.989 112.727C152.102 114.136 153.727 116.182 154.864 118.864C156 121.534 156.563 124.773 156.551 128.58C156.551 132.409 155.983 135.682 154.847 138.398C153.722 141.114 152.108 143.187 150.006 144.619C147.903 146.051 145.386 146.767 142.455 146.767ZM142.455 140.648C144.455 140.648 146.051 139.642 147.244 137.631C148.438 135.619 149.028 132.602 149.017 128.58C149.017 125.932 148.744 123.727 148.199 121.966C147.665 120.205 146.903 118.881 145.915 117.994C144.938 117.108 143.784 116.665 142.455 116.665C140.466 116.665 138.875 117.659 137.682 119.648C136.489 121.636 135.886 124.614 135.875 128.58C135.875 131.261 136.142 133.5 136.676 135.295C137.222 137.08 137.989 138.42 138.977 139.318C139.966 140.205 141.125 140.648 142.455 140.648ZM175.501 146.767C172.57 146.756 170.047 146.034 167.933 144.602C165.831 143.17 164.212 141.097 163.075 138.381C161.95 135.665 161.393 132.398 161.405 128.58C161.405 124.773 161.967 121.528 163.092 118.847C164.229 116.165 165.848 114.125 167.95 112.727C170.064 111.318 172.581 110.614 175.501 110.614C178.422 110.614 180.933 111.318 183.036 112.727C185.149 114.136 186.774 116.182 187.911 118.864C189.047 121.534 189.609 124.773 189.598 128.58C189.598 132.409 189.03 135.682 187.893 138.398C186.768 141.114 185.155 143.187 183.053 144.619C180.95 146.051 178.433 146.767 175.501 146.767ZM175.501 140.648C177.501 140.648 179.098 139.642 180.291 137.631C181.484 135.619 182.075 132.602 182.064 128.58C182.064 125.932 181.791 123.727 181.246 121.966C180.712 120.205 179.95 118.881 178.962 117.994C177.984 117.108 176.831 116.665 175.501 116.665C173.513 116.665 171.922 117.659 170.729 119.648C169.536 121.636 168.933 124.614 168.922 128.58C168.922 131.261 169.189 133.5 169.723 135.295C170.268 137.08 171.036 138.42 172.024 139.318C173.013 140.205 174.172 140.648 175.501 140.648Z" fill="#9FA5AB"/>
</g>
<defs>
<filter id="filter0_d_1_2" x="2" y="32" width="236" height="178" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape"/>
</filter>
<clipPath id="clip0_1_2">
<rect width="240" height="240" fill="white"/>
</clipPath>
</defs>
</svg>
