import { ListNumberedIcon } from '@shopify/polaris-icons';
import { BaseTool } from './BaseTool';
import { useRTE } from '../context';

export const OrderList = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('orderedList');
    const toggleOrderList = () => {
        editor?.chain().focus().toggleOrderedList().run();
    };

    return <BaseTool icon={ListNumberedIcon} isActive={isActive} onClick={toggleOrderList} tooltip="Order List" />;
};
