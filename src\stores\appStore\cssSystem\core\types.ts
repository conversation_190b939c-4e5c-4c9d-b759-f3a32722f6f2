import { CSSProperties } from 'react';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';

export type Breakpoint = 'desktop' | 'mobile';

export type ApplyTo = 'all' | 'editor' | 'publish';

export interface Generator {
    selector: string | ((widgetId: string) => string);
    generator: (blockData: Auto_BlockData, breakpoint: Breakpoint) => CSSProperties;
    applyTo?: ApplyTo;
}

export type ValueOf<T> = T[keyof T];
