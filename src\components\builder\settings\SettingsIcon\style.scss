.settings-icon {
    .Polaris-Button__Icon {
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover svg path {
            fill: #111;
        }
    }
    .Polaris-DropZone {
        .Polaris-DropZone-FileUpload {
            padding: 0;
            svg {
                fill: rgb(138, 138, 138);
                &:hover {
                    cursor: pointer;
                    fill: #4a4a4a;
                }
            }
        }
    }
}

.shape-manage {
    &__socials {
        height: 100%;
        width: 100% !important;
    }

    &__socials,
    &__pattern,
    &__icons,
    &__background,
    &__arrows {
        .ReactVirtualized__Grid__innerScrollContainer {
            margin: auto;
        }
    }

    &__content {
        position: relative;
        width: 99.8%;
        margin: auto;
        height: 100%;
        padding: 1.25rem 1rem;
        &__search {
            width: 330px;
            position: absolute;
            top: 20px;
            right: 16px;
            margin-bottom: 1rem;
            .Polaris-TextField__Suffix {
                margin-right: 5px;
                .Polaris-Button {
                    background: transparent !important;
                    margin: 0;
                }
            }
        }
        .Polaris-Tabs__Outer {
            > .Polaris-Box {
                padding: 0;
                margin-bottom: 1.25rem;
                ul {
                    padding: 0;
                    column-gap: 0.5rem;
                    li {
                        .Polaris-Tabs__Tab {
                            height: 2rem;
                        }
                    }
                }
            }
        }
        svg {
            fill: #919191;
        }
    }
    &__item {
        width: 130px;
        height: 130px;
        border-radius: 8px;
        cursor: pointer;
        padding: 1.71875rem;
        svg {
            width: 100%;
            height: 100%;
        }
        &:hover {
            svg {
                fill: #6086f2;
            }
        }
        &--active {
            svg {
                fill: #6086f2;
            }
        }
    }
}
