export interface MediaType {
    id: string;
    alt: string;
    url: string;
    width: string;
    height: string;
    mimeType: string;
    fileSize?: string;
    image?: {
        url: string;
        alt: string;
    };
}

export type MediaManagerMode = 'manager' | 'editor';

export type MediaManagerTab = 'upload' | 'autoketing' | 'unplash';

export type OpenState =
    | {
          mode: MediaManagerMode;
          url?: string;
      }
    | false;

export interface MediaManagerState {
    open: OpenState;
    multiple: boolean;
    selected: MediaType[];
    currentTab: MediaManagerTab;
    allows: 'all' | 'image' | 'video';
    page: number;
    sortBy: string;
    endCursor: string;
    forceUpdate: number;
    onSelect?: (media: MediaType | MediaType[]) => void;
}

export interface MediaManagerActions {
    setCurrentTab: (tab: MediaManagerTab) => void;
    setSelected: (selected: MediaType[]) => void;
    setSortBy: (sortBy: string) => void;
    setPage: (page: number) => void;
    setEndCursor: (endCursor: string) => void;
    setOpen: (
        open: OpenState,
        options?: {
            page?: number;
            sortBy?: string;
            endCursor?: string;
            multiple?: boolean;
            forceUpdate?: boolean;
            allows?: 'all' | 'image' | 'video';
            currentTab?: MediaManagerTab;
            onSelect?: (media: MediaType | MediaType[]) => void;
        },
    ) => void;
}

export type MediaManagerStore = MediaManagerState & MediaManagerActions;

export interface SystemMeidaResponse {
    result: {
        data: {
            pageInfo: {
                hasNextPage: boolean;
                currentPage: number;
                totalPage: number;
                totalItem: number;
                endCursor?: string;
            };
            edges: MediaType[];
        };
    };
}

export interface UnsplashResponse {
    total: number;
    total_pages: number;
    results: {
        id: string;
        alt_description: string;
        urls: {
            raw: string;
            full: string;
            small: string;
        };
        width: number;
        height: number;
    }[];
}
