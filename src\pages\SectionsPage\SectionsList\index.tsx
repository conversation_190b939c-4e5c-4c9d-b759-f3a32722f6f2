import { FC } from 'react';
import { EmptyState, ResourceItem, ResourceList } from '@shopify/polaris';
import { DeleteIcon, ExportIcon } from '@shopify/polaris-icons';
import imgNull from '@/assets/images/sections-empty.png?url';
import { SectionItem } from './SectionItem';
import { Skeleton } from './Skeleton';
import { Section } from '../types';

interface SectionsListProps {
    sections: Section[];
    selectedItems: string[];
    isLoading?: boolean;
    onSelectionChange: (selectedItems: string[]) => void;
    onPublish?: () => void;
    onUnpublish?: () => void;
    onDelete?: () => void;
    onExport?: () => void;
    skeletonCount: number;
    refetch?: () => void;
}

export const SectionsList: FC<SectionsListProps> = ({
    sections,
    selectedItems,
    isLoading,
    onSelectionChange,
    onPublish,
    onUnpublish,
    onDelete,
    onExport,
    skeletonCount,
    refetch,
}) => {
    const promotedBulkActions = [
        {
            content: 'Publish',
            onAction: () => onPublish?.(),
        },
        {
            content: 'Unpublish',
            onAction: () => onUnpublish?.(),
        },
    ];

    const bulkActions = [
        {
            content: 'Export',
            icon: ExportIcon,
            onAction: () => onExport?.(),
        },
        {
            icon: DeleteIcon,
            destructive: true,
            content: 'Delete',
            onAction: () => onDelete?.(),
        },
    ];

    if (isLoading) return <Skeleton count={skeletonCount} active={true} />;

    return (
        <ResourceList
            headerContent="Select all"
            resourceName={{ singular: 'section', plural: 'sections' }}
            items={sections}
            selectable
            selectedItems={selectedItems}
            bulkActions={bulkActions}
            promotedBulkActions={promotedBulkActions}
            idForItem={(item) => item.id.toString()}
            onSelectionChange={onSelectionChange}
            emptyState={
                <EmptyState heading="No section yet" image={imgNull}>
                    <p>Lorem ipsum dolor sit amet consectetur. Eget sit bibendum in donec </p>
                </EmptyState>
            }
            renderItem={(data, id) => {
                return <SectionItem key={id} data={data} selectMode={true} refetch={refetch} __type__={ResourceItem} />;
            }}
        />
    );
};
