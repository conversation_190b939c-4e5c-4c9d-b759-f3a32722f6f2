import { FC } from 'react';
import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { BreadcrumbSettingsConfigs } from '../configs';

export const BreadcrumbPreview: FC = () => {
    return (
        <div>
            {BreadcrumbSettingsConfigs.map((option) => (
                <BlockToolbar
                    {...option}
                    id={option.id}
                    configs={option.configs as unknown as Record<string, unknown>}
                    type="breadcrumb"
                    key={option.id}
                    style={{
                        width: '100%',
                        height: '80px',
                        background: 'while',
                        borderRadius: '8px',
                        border: '1px solid #e5e5e5',
                        cursor: 'move',
                        display: 'flex',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        alignItems: 'center',
                    }}
                >
                    {option.label}
                </BlockToolbar> 
            ))}
        </div>
    );
};
