/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockData, DATA_SET_AUTO_ID, DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';

export const generateShapeHTML = async (blockId: string, block: Auto_BlockData) => {
    const content = block.configs.content as any;
    const svg = content?.svg;
    const type = content?.type;
    let shapeHTML = '';

    if (type === 'svg' && svg) {
        // Render SVG shape as a mask (giống viewer)
        shapeHTML = `<div class="atk-shape" ${DATA_SET_AUTO_ID_INNER}="${blockId}" style="width:100%;height:100%;display:flex;justify-content:center;align-items:center;mask-image:url('data:image/svg+xml,${encodeURIComponent(
            svg,
        )}');-webkit-mask-image:url('data:image/svg+xml,${encodeURIComponent(
            svg,
        )}');mask-size:100% 100%;-webkit-mask-size:100% 100%;"></div>`;
    } else {
        // fallback nếu không có svg
        shapeHTML = `<div class="atk-shape" ${DATA_SET_AUTO_ID_INNER}="${blockId}" style="width:100%;height:100%;display:flex;justify-content:center;align-items:center;"></div>`;
    }

    return `
        <div class="atk-shape-wrapper" ${DATA_SET_AUTO_ID}="${blockId}" >${shapeHTML}</div>
    `;
};
