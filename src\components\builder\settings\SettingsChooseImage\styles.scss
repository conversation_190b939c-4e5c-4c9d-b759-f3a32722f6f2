.settings-choose-image {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .Polaris-DropZone--sizeLarge {
        height: 180px;
    }
    .Polaris-Button.Polaris-Button svg {
        fill: var(--pc-button-icon-fill_hover);
    }

    .preview-image {
        width: 100%;
        height: 180px;
        border-radius: 6px;
        object-fit: cover;
    }

    .dropzone-content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        width: 100%;
        height: 100%;
        padding-inline: 32px;
        background-size: cover;
        background-position: center;
        border-radius: 8px;
        overflow: hidden;
    }

    .loading-overlay {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .icon-container {
        margin-bottom: 8px;
    }
}
