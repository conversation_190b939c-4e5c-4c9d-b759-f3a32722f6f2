import { FC } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import { buttonPositionOptions } from '@/components/builder/data/options';

interface PositionProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
}

export const Position: FC<PositionProps> = ({ path, blockId, isUpdateConfigs, label }) => {
    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <SettingsSelect
                    options={buttonPositionOptions}
                    path={`${path}`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Type"
                />
            </Box>
        </BaseCollapse>
    );
};
