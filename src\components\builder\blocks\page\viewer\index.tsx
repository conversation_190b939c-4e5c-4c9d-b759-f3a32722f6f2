import { FC, PropsWithChildren } from 'react';
import { Auto_BlockViewer, BlockViewer } from '@giaminhautoketing/auto-builder';

export const PageViewer: FC<PropsWithChildren<Auto_BlockViewer>> = ({
    children,
    autoId,
    cname,
    label,
    type,
    bpConfigs,
    configs,
}) => (
    <BlockViewer
        autoId={autoId}
        cname={cname}
        label={label}
        type={type}
        bpConfigs={bpConfigs}
        className="page"
        configs={configs}
    >
        {children}
    </BlockViewer>
);
