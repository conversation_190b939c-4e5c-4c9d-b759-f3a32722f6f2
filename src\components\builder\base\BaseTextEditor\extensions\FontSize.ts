import { Extension, ChainedCommands } from '@tiptap/core';
import '@tiptap/extension-text-style';
import { DeviceMode, createAllCssVars, createModeAttributes, createModeAttrValue, createResetAttrs } from '../utils';

const TEXT_EDITOR_NODE_NAME_HEADING = 'heading';
const TEXT_EDITOR_NODE_NAME_PARAGRAPH = 'paragraph';

const FONT_SIZE_VARS = createAllCssVars('fontSize');

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        fontSize: {
            setMarkFontSize: (fontSize: string, mode: DeviceMode) => ReturnType;
            unsetMarkFontSize: (mode?: DeviceMode) => ReturnType;
        };
    }
}

export const FontSize = Extension.create({
    name: 'fontSize',
    addOptions() {
        return {
            types: [TEXT_EDITOR_NODE_NAME_HEADING, TEXT_EDITOR_NODE_NAME_PARAGRAPH, 'textStyle'],
        };
    },

    addGlobalAttributes() {
        const attributes = createModeAttributes('fontSize', FONT_SIZE_VARS);
        return [
            {
                types: this.options.types,
                attributes,
            },
        ];
    },

    addCommands() {
        return {
            setMarkFontSize:
                (fontSize: string, mode: DeviceMode) =>
                ({ chain }: { chain: () => ChainedCommands }) => {
                    const fontSizeAttr = createModeAttrValue('fontSize', mode, fontSize);
                    return chain().setMark('textStyle', fontSizeAttr).run();
                },

            unsetMarkFontSize:
                (mode?: DeviceMode) =>
                ({ chain }: { chain: () => ChainedCommands }) => {
                    const fontSizeAttr = createResetAttrs('fontSize', mode);
                    return chain().setMark('textStyle', fontSizeAttr).removeEmptyTextStyle().run();
                },
        };
    },
});
