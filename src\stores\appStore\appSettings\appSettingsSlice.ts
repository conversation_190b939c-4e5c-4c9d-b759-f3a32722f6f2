import { StateCreator } from 'zustand';
import { AppActions, AppState } from '../types';
import { apiAddress } from '@/configs/apiAddress';
import { httpRequest } from '@/configs';
import { AppSettingsRequest, AppSettingsActions, AppSettingsState } from './types';
import { ActivityLogParams } from '@/pages/SettingsPage/types';
export const createAppSettingsSlice: StateCreator<
    AppActions & AppState,
    [],
    [],
    AppSettingsState & AppSettingsActions
> = (set, get) => ({
    appSettings: null,
    apiStatus: 'loading',
    activityLogs: [],
    totalLogs: 0,
    setTotalLogs: (totalLogs: number) =>
        set((state) => ({
            ...state,
            totalLogs,
        })),
    getAppSettings: async () => {
        set((state) => ({
            ...state,
            apiStatus: 'loading',
        }));
        try {
            const response = await httpRequest.get(apiAddress.appSettings.settings);
            set((state) => ({
                ...state,
                apiStatus: 'success',
            }));
            await set((state) => ({
                ...state,
                appSettings: response.data.result.data,
            }));
            return response.data;
        } catch (error) {
            set((state) => ({
                ...state,
                apiStatus: 'error',
            }));
            console.error('Failed to get app settings:', error);
            throw error;
        }
    },
    updateAppSettings: async (appSettings: AppSettingsRequest) => {
        set((state) => ({
            ...state,
            apiStatus: 'loading',
        }));
        try {
            const response = await httpRequest.put(apiAddress.appSettings.settings, appSettings);
            set((state) => ({
                ...state,
                apiStatus: 'success',
            }));
            await get().getAppSettings();
            return response.data;
        } catch (error) {
            set((state) => ({
                ...state,
                apiStatus: 'error',
            }));
            throw error;
        }
    },
    resetAppSettings: async () => {
        set((state) => ({
            ...state,
            apiStatus: 'loading',
        }));
        try {
            await httpRequest.get(apiAddress.appSettings.reset, { timeout: 360000 });
            set((state) => ({
                ...state,
                apiStatus: 'success',
            }));
        } catch (error) {
            set((state) => ({
                ...state,
                apiStatus: 'error',
            }));
            throw error;
        }
    },
    setActivityLogs: async (params: ActivityLogParams) => {
        set((state) => ({
            ...state,
            apiStatus: 'loading',
        }));
        try {
            const response = await httpRequest.get(apiAddress.appSettings.activityLogs, { params });
            set((state) => ({
                ...state,
                apiStatus: 'success',
            }));
            await set((state) => {
                state.activityLogs = response.data.result.data.list;
                state.totalLogs = response.data.result.data.total;
                return state;
            });
            return response.data;
        } catch (error) {
            set((state) => ({
                ...state,
                apiStatus: 'error',
            }));
            throw error;
        }
    },
});
