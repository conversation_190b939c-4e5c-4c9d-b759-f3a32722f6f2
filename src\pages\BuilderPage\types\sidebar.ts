import { IconSource } from '@shopify/polaris';
import { ReactElement } from 'react';
import { TabItemProps } from './tabs';

export interface SideBarItemProps {
    id: string;
    title: string;
    icon: IconSource | ReactElement;
    drawerContent?: React.ComponentType<{
        onItemClick?: (item: TabItemProps) => void;
        selectedItem?: TabItemProps;
    }>;
}

export interface SideBarConfigProps {
    items: SideBarItemProps[];
}
