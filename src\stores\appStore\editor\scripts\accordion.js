document.addEventListener('DOMContentLoaded', () => {
    // active first + open 1
    document.querySelectorAll('[data-so-accordion="true"]').forEach((group) => {
        const groupId = group.dataset.soId;
        const accordion = group.querySelectorAll(`[data-so-item="true"][data-so-id-item=${groupId}]`);
        const panels = group.querySelectorAll(
            `[data-so-accordion-panel="true"][data-so-id-accordion-panels=${groupId}]`,
        );

        accordion.forEach((ac) => {
            ac.classList.remove('active');
        });
        panels.forEach((panel) => {
            panel.classList.remove('active');
        });
        if (accordion.length > 0 && panels.length > 0) {
            accordion[0].classList.add('active');
            panels[0].classList.add('active');
        }

        accordion.forEach((ac, index) => {
            const soTabId = ac.dataset.soItemId;
            const panelMatchId = group.querySelector(
                `[data-so-accordion-panel="true"][data-so-id-accordion-panel="${soTabId}"]`,
            );

            ac.addEventListener('click', () => {
                const isActive = ac.classList.contains('active');

                accordion.forEach((btn) => btn.classList.remove('active'));
                panels.forEach((panel) => panel.classList.remove('active'));

                if (!isActive) {
                    ac.classList.add('active');
                    panelMatchId.classList.add('active');
                }
            });
        });
    });
    // active none + open 1
    document.querySelectorAll('[data-so-accordion-no="true"]').forEach((group) => {
        const groupId = group.dataset.soId;
        const accordion = group.querySelectorAll(`[data-so-item="true"][data-so-id-item=${groupId}]`);
        const panels = group.querySelectorAll(
            `[data-so-accordion-panel="true"][data-so-id-accordion-panels=${groupId}]`,
        );
        accordion.forEach((ac) => {
            ac.classList.remove('active');
        });
        panels.forEach((panel) => {
            panel.classList.remove('active');
        });
        accordion.forEach((ac) => {
            const soTabId = ac.dataset.soItemId;
            const panelMatchId = group.querySelector(
                `[data-so-accordion-panel="true"][data-so-id-accordion-panel="${soTabId}"]`,
            );
            ac.addEventListener('click', () => {
                const isActive = ac.classList.contains('active');

                accordion.forEach((btn) => btn.classList.remove('active'));
                panels.forEach((panel) => panel.classList.remove('active'));

                if (!isActive) {
                    ac.classList.add('active');
                    panelMatchId.classList.add('active');
                }
            });
        });
    });
});

// active 1 + open all
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('[data-so-accordion-all="true"]').forEach((group) => {
        const groupId = group.dataset.soId;
        const accordion = group.querySelectorAll(`[data-so-item="true"][data-so-id-item=${groupId}]`);
        const panels = group.querySelectorAll(
            `[data-so-accordion-panel="true"][data-so-id-accordion-panels=${groupId}]`,
        );

        accordion.forEach((ac) => {
            ac.classList.remove('active');
        });
        panels.forEach((panel) => {
            panel.classList.remove('active');
        });
        if (accordion.length > 0 && panels.length > 0) {
            accordion[0].classList.add('active');
            panels[0].classList.add('active');
        }

        accordion.forEach((ac, index) => {
            const soTabId = ac.dataset.soItemId;
            const panelMatchId = group.querySelector(
                `[data-so-accordion-panel="true"][data-so-id-accordion-panel="${soTabId}"]`,
            );

            ac.addEventListener('click', () => {
                const isActive = ac.classList.contains('active');

                if (isActive) {
                    ac.classList.remove('active');
                    panelMatchId.classList.remove('active');
                } else {
                    ac.classList.add('active');
                    panelMatchId.classList.add('active');
                }
            });
        });
    });
});

// active all + open all
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('[data-so-accordion-all-active="true"]').forEach((group) => {
        const groupId = group.dataset.soId;
        const accordion = group.querySelectorAll(`[data-so-item="true"][data-so-id-item=${groupId}]`);
        const panels = group.querySelectorAll(
            `[data-so-accordion-panel="true"][data-so-id-accordion-panels=${groupId}]`,
        );

        accordion.forEach((ac) => {
            const soTabId = ac.dataset.soItemId;
            const panelMatchId = group.querySelector(
                `[data-so-accordion-panel="true"][data-so-id-accordion-panel="${soTabId}"]`,
            );

            ac.classList.add('active');
            panelMatchId.classList.add('active');

            ac.addEventListener('click', () => {
                const isActive = ac.classList.contains('active');

                if (isActive) {
                    ac.classList.remove('active');
                    panelMatchId.classList.remove('active');
                } else {
                    ac.classList.add('active');
                    panelMatchId.classList.add('active');
                }
            });
        });
    });
});

// active none + open all
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('[data-so-accordion-all-no="true"]').forEach((group) => {
        const groupId = group.dataset.soId;
        const accordion = group.querySelectorAll(`[data-so-item="true"][data-so-id-item=${groupId}]`);
        const panels = group.querySelectorAll(
            `[data-so-accordion-panel="true"][data-so-id-accordion-panels=${groupId}]`,
        );

        accordion.forEach((ac) => {
            ac.classList.remove('active');
        });

        panels.forEach((panel) => {
            panel.classList.remove('active');
        });

        accordion.forEach((ac) => {
            const soTabId = ac.dataset.soItemId;
            const panelMatchId = group.querySelector(
                `[data-so-accordion-panel="true"][data-so-id-accordion-panel="${soTabId}"]`,
            );

            ac.addEventListener('click', () => {
                const isActive = ac.classList.contains('active');

                if (isActive) {
                    ac.classList.remove('active');
                    panelMatchId.classList.remove('active');
                } else {
                    ac.classList.add('active');
                    panelMatchId.classList.add('active');
                }
            });
        });
    });
});
