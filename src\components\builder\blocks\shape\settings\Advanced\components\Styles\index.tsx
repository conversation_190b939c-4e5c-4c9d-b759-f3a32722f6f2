import { FC } from 'react';
import { Icon, BlockStack, Box } from '@shopify/polaris';
import { ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { SettingsBackgroundColor } from '@/components/builder/settings/SettingsBackgroundColor';
import { SettingsShadow } from '@/components/builder/settings/SettingsShadow';

interface StylesProps {
    id: string;
}

export const Styles: FC<StylesProps> = ({ id }) => {
    return (
        <BaseCollapse label="Styles" labelContent={<Icon source={ChevronRightIcon} />}>
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <SettingsBackgroundColor
                            blockId={id}
                            path="background"
                            label="Background"
                            isUpdateConfigs={false}
                        />
                        <SettingsShadow
                            type="box-shadow"
                            blockId={id}
                            isUpdateConfigs={false}
                            path="boxShadow"
                            label="Box shadow"
                        />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
