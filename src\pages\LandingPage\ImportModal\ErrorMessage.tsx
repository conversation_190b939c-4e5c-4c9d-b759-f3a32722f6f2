import { FC, memo } from 'react';
import { Box, Text, Icon, InlineStack } from '@shopify/polaris';
import { AlertCircleIcon } from '@shopify/polaris-icons';

interface ErrorMessageProps {
    isError: boolean;
}

export const ErrorMessage: FC<ErrorMessageProps> = memo(({ isError }) => {
    if (!isError) return null;
    return (
        <Box padding="400" paddingBlockEnd="0">
            <Box
                background="bg-fill-critical-secondary"
                borderRadius="200"
                borderWidth="025"
                borderColor="border-critical-secondary"
                paddingInline="300"
                paddingBlock="200"
            >
                <InlineStack gap="200" blockAlign="start" wrap={false}>
                    <Box padding="0">
                        <Icon source={AlertCircleIcon} tone="textCritical" />
                    </Box>
                    <Text
                        tone="critical"
                        as="p"
                        children="The selected file type is not supported. Please upload a .etk file to continue."
                    />
                </InlineStack>
            </Box>
        </Box>
    );
});
