import { FC, useState } from 'react';
import { DropZone, InlineStack, Button, Tooltip, Text, Icon, Spinner } from '@shopify/polaris';
import { ExchangeIcon, DeleteIcon, UploadIcon } from '@shopify/polaris-icons';
import { useMediaManager } from '@/pages/BuilderPage/components/MediaManager/useMediaManager';
import { useMutation } from '@/hooks';
import { useSettings } from '@/pages/BuilderPage/hooks';
import { apiAddress } from '@/configs/apiAddress';
import './styles.scss';

interface SettingsChooseImageProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const SettingsChooseImage: FC<SettingsChooseImageProps> = ({ blockId, path, isUpdateConfigs }) => {
    const setOpen = useMediaManager((state) => state.setOpen);
    const [urlPreview, setUrlPreview] = useState<string | null>(null);
    const { value, updateValue } = useSettings({ blockId, path, isUpdateConfigs });

    const openMediaManager = () => {
        setOpen(
            { mode: 'manager' },
            {
                currentTab: 'unplash',
                sortBy: 'all',
                allows: 'image',
                onSelect(media) {
                    if (!Array.isArray(media)) {
                        updateValue(media.url);
                    }
                },
            },
        );
    };

    const onDelete = () => {
        if (value) {
            updateValue('');
            setUrlPreview(null);
        }
    };

    const mutation = useMutation({
        url: apiAddress.media.index,
        method: 'POST',
        timeout: 50000,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        onSuccess: (data: { result: { data: { url: string }[] } }) => {
            updateValue(data?.result?.data[0]?.url);
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
        onSettled: () => {
            setUrlPreview(null);
        },
    });

    return (
        <div className="settings-choose-image">
            {value ? (
                <img src={value} className="preview-image" />
            ) : (
                <DropZone
                    overlayText="Drop images to upload"
                    allowMultiple={false}
                    onDrop={(_: File[], acceptedFiles: File[]) => {
                        if (acceptedFiles.length > 0) {
                            const url = URL.createObjectURL(acceptedFiles[0]);
                            setUrlPreview(url);
                            const formData = new FormData();
                            formData.append('mediaType', 'image');
                            acceptedFiles.forEach((file) => {
                                formData.append('files[]', file);
                            });
                            mutation.mutate(formData);
                        }
                    }}
                    outline={!mutation.isLoading}
                >
                    <div
                        className="dropzone-content"
                        style={{ backgroundImage: urlPreview ? `url(${urlPreview})` : 'none' }}
                        onClick={(e) => {
                            e.stopPropagation();
                            openMediaManager();
                        }}
                    >
                        {mutation.isLoading ? (
                            <div className="loading-overlay">
                                <Spinner size="large" />
                            </div>
                        ) : (
                            <>
                                <div className="icon-container">
                                    <Icon source={UploadIcon} />
                                </div>
                                <Text as="p" variant="bodySm" tone="subdued" alignment="center">
                                    <Text as="span" variant="bodySm" tone="inherit">
                                        Click to select
                                    </Text>{' '}
                                    or drag and drop image files
                                </Text>
                            </>
                        )}
                    </div>
                </DropZone>
            )}
            <InlineStack align="end" gap="200">
                <Tooltip content="Change" dismissOnMouseOut>
                    <Button variant="tertiary" icon={ExchangeIcon} size="micro" onClick={openMediaManager} />
                </Tooltip>
                <Tooltip content="Delete" dismissOnMouseOut>
                    <Button variant="tertiary" icon={DeleteIcon} size="micro" onClick={onDelete} />
                </Tooltip>
            </InlineStack>
        </div>
    );
};
