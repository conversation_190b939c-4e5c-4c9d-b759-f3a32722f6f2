/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import { CacheProvider } from '@emotion/react';
import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER } from '@giaminhautoketing/auto-builder';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { emotionNoPrefixCache } from '@/utils/emotionNoPrefixCache';
import { useFormAttributes } from '@/components/builder/blocks/form/hooks/useFormAttributes';
import { FormField } from '@/components/builder/blocks/form/components/FormField';
import {
    DATA_SET_FORM_TITLE,
    DATA_SET_FORM_FIELD_WRAP,
    DATA_SET_FORM_SUBMIT_WRAP,
    DATA_SET_FORM_SUBMIT,
} from '@/components/builder/blocks/form/constants';
import { FormConfig } from '@/components/builder/blocks/form/types';

const FormViewerContent = ({ configs, autoId, bpConfigs }: { configs: FormConfig; autoId: string; bpConfigs: any }) => {
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const formAttributes = useFormAttributes(configs, autoId, currentDevice, bpConfigs);
    return (
        <form {...formAttributes}>
            <h2 {...{ [DATA_SET_FORM_TITLE]: autoId }}>{configs?.contentForm.formTitle}</h2>
            <div {...{ [`${DATA_SET_FORM_FIELD_WRAP}`]: autoId }}>
                {configs?.form.map((field: any, index: number) => (
                    <FormField key={`${field.key}-${index}`} field={field} index={index} autoId={autoId} />
                ))}
            </div>
            <div {...{ [DATA_SET_FORM_SUBMIT_WRAP]: autoId }}>
                <button {...{ [DATA_SET_FORM_SUBMIT]: autoId }} type="submit">
                    {configs?.contentForm.buttonTitle}
                </button>
            </div>
        </form>
    );
};

export const FormViewer: FC<Auto_BlockViewer & { configs: FormConfig }> = ({
    autoId,
    cname,
    label,
    type,
    bpConfigs,
    configs,
}) => {
    if (!configs) return null;

    return (
        <CacheProvider value={emotionNoPrefixCache}>
            <BlockViewer
                autoId={autoId}
                cname={cname}
                label={label}
                type={type}
                bpConfigs={bpConfigs}
                attrs={{ [DATA_SET_VIEWER]: 'true' }}
                configs={configs}
            >
                <FormViewerContent configs={configs} autoId={autoId} bpConfigs={bpConfigs} />
            </BlockViewer>
        </CacheProvider>
    );
};
