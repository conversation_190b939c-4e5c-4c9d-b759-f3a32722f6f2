import { useCallback } from 'react';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';

interface UseStoreUpdateProps {
    blockId: string;
    path: string;
    isUpdateConfigs: boolean;
}

export const useStoreUpdate = ({ blockId, path, isUpdateConfigs }: UseStoreUpdateProps) => {
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const updateStoreData = useCallback(
        (value: string) => {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, `${path}.val`, value);
            } else {
                updateBlockProperty(blockId, currentDevice, `${path}.val`, value);
            }
        },
        [isUpdateConfigs, blockId, path, currentDevice, updateBlockConfigsProperty, updateBlockProperty],
    );

    return { updateStoreData };
};
