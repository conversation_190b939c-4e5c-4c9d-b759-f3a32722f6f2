/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockType } from '@giaminhautoketing/auto-builder';

type FormUnitValue = { val: string; unit?: string };

export interface FormSpacing {
    margin?: {
        top?: FormUnitValue;
        bottom?: FormUnitValue;
        left?: FormUnitValue;
        right?: FormUnitValue;
    };
    padding?: {
        top?: FormUnitValue;
        bottom?: FormUnitValue;
        left?: FormUnitValue;
        right?: FormUnitValue;
    };
}

interface FormBorderRadius {
    'top-left': FormUnitValue;
    'top-right': FormUnitValue;
    'bottom-right': FormUnitValue;
    'bottom-left': FormUnitValue;
}

interface FormBorder {
    color?: string;
    top?: FormUnitValue;
    right?: FormUnitValue;
    bottom?: FormUnitValue;
    left?: FormUnitValue;
    type?: string;
    radius?: FormBorderRadius;
}

interface FormBackgroundImage {
    url: string;
    repeat: string;
    position: string;
    attachment: string;
    fill: string;
}

interface FormBackground {
    type: 'color' | 'image';
    color?: string;
    image?: FormBackgroundImage;
}

interface FormTextStyle {
    fontSize?: FormUnitValue;
    fontWeight?: string;
    fontFamily?: string;
    color?: string;
    textTransform?: string;
    textDecoration?: string;
    textAlign?: string;
    fontStyle?: string;
    textDirection?: string;
    lineHeight?: FormUnitValue;
    letterSpacing?: FormUnitValue;
}

type FormInputFieldType = 'text' | 'email' | 'password' | 'number' | 'long-text' | 'free-text' | 'tel';

export type FormField =
    | {
          id: string;
          type: FormInputFieldType;
          key: string;
          label: string;
          defaultField?: boolean;
          placeholder?: string;
          validations?: {
              required: boolean;
              readOnly: boolean;
          };
          characterLimit?: {
              setCharLimit: boolean;
              charLimit: number;
          };
          pattern?: {
              addPatternValidation: boolean;
              pattern: string;
          };
          numberFormat?: 'whole-number' | 'decimal-number';
          digitsAfterDecimal?: {
              val: number;
          };
          maxValue?: {
              addMaxValue: boolean;
              val: number;
          };
          minValue?: {
              addMinValue: boolean;
              val: number;
          };

          showInitialText?: 'none' | 'default';
          initialText?: string;
          description?: string;
          separateLine?: boolean;
      }
    | {
          id: string;
          type: 'dropdown';
          key: string;
          label: string;
          defaultField?: boolean;
          placeholder?: string;
          options: Array<{ id: string; value: string; label: string }>;
          validations?: {
              required: boolean;
          };

          showInitialText?: 'default' | 'placeholder';
          initialText?: string;
          separateLine?: boolean;
      }
    | {
          id: string;
          type: 'checkbox';
          key: string;
          label: string;
          defaultField?: boolean;
          placeholder?: string;
          validations?: {
              required: boolean;
          };
          options: Array<{
              id: string;
              key: string;
              value: string;
              label: string;
              defaultChecked?: boolean;
              image?: string;
          }>;
          direction?: 'row' | 'column';
          separateLine?: boolean;
      }
    | {
          id: string;
          type: 'radio';
          key: string;
          label: string;
          defaultField?: boolean;
          placeholder?: string;
          options: Array<{ id: string; value: string; label: string }>;
          validations?: {
              required: boolean;
          };
          showInitialText?: 'none' | 'default';
          initialText?: string;
          direction?: 'row' | 'column';
          description?: string;
          separateLine?: boolean;
      };

interface FormLayout {
    spaceTitleField: FormUnitValue;
    space: FormSpacing;
    vertical: FormUnitValue;
    horizontal: FormUnitValue;
    align: 'left' | 'right' | string;
}

export interface FormSubmitStyle extends FormTextStyle {
    background?: FormBackground;
    buttonWidth?: FormUnitValue;
    buttonHeight?: FormUnitValue;
    buttonBorder?: FormBorder;
    buttonLineSpacing?: {
        type?: string;
        value?: FormUnitValue;
    };
    buttonLetterSpacing?: {
        type?: string;
        value?: FormUnitValue;
    };
    textShadow?: string;
    buttonSpacing?: FormSpacing;
    boxShadow?: string;
}

interface FormTitleStyle extends FormTextStyle {
    textShadow?: string;
    titleSpacing: FormSpacing;
}

interface FormFieldLabel {
    labelFontSize: FormUnitValue;
    labelFontWeight: string;
    labelFontFamily: string;
    labelColor: string;
    labelTextTransform: string;
    labelTextDecoration: string;
    labelTextAlign: string;
    labelFontStyle: string;
    labelTextDirection: string;
}

interface FormFieldInput {
    inputFontSize: FormUnitValue;
    inputFontWeight: string;
    inputFontFamily: string;
    inputColor: string;
    inputTextTransform: string;
    inputTextDecoration: string;
    inputTextAlign: string;
    inputFontStyle: string;
    inputTextDirection: string;
}

interface FormFieldPlaceholder {
    placeholderFontSize: FormUnitValue;
    placeholderFontWeight: string;
    placeholderFontFamily: string;
    placeholderColor: string;
    placeholderTextAlign: string;
    placeholderTextDirection: string;
}

interface FormFieldStyle {
    background: FormBackground;
    border: FormBorder;
}

export interface FormBreakpoint {
    backgroundForm: FormBackground;
    color: string;
    width: FormUnitValue;
    formHeight: FormUnitValue;
    position: {
        positionType: 'default' | 'sticky';
        stickTo: {
            stickToType: 'top' | 'bottom';
            top?: FormUnitValue;
            bottom?: FormUnitValue;
        };
    };
    boxShadow?: string;
    formAlignment: {
        alignSelf: string;
        justifySelf: string;
    };
    fieldsStyles: FormFieldStyle;
    fieldType: 'label' | 'input' | 'placeholder';
    fieldsLabel: FormFieldLabel;
    fieldsInput: FormFieldInput;
    fieldsPlaceholder: FormFieldPlaceholder;
    fieldsSpacing: FormSpacing;
    titleForm: FormTitleStyle;
    buttonSubmit: FormSubmitStyle;
    arrangement: {
        buttonAlign: 'left' | 'right' | string;
        spaceTitleField: FormUnitValue;
    };
    formBorder: FormBorder;
    generalForm: FormLayout;
    fieldSizes: {
        [fieldKey: string]: {
            fieldWidth?: FormUnitValue;
            fieldHeight?: FormUnitValue;
            checkboxSize?: FormUnitValue;
            radioSize?: FormUnitValue;
        };
    };
}

export interface FormConfig {
    form: FormField[];
    contentForm: {
        formTitle: string;
        buttonTitle: string;
    };
    submit: {
        action: 'stay-on-page' | 'open-link';
        successMessage: string;
        errorMessage: string;
        openLink: { url: string; open: 'new-window' | 'in-page' };
    };
    displayOnDesktop: boolean;
    displayOnMobile: boolean;
    animation?: {
        type: string;
        duration: FormUnitValue;
        loop: string;
        delay: FormUnitValue;
    };
    customCSS: {
        enable: boolean;
        className: string;
        style: string;
    };
    events?: Record<string, any>;
}

export type FormBreakpoints<T> = {
    desktop: T;
    mobile: T;
};

export interface FormSettings {
    id: string;
    cname: string;
    label: string;
    type: Auto_BlockType;
    bpConfigs: FormBreakpoints<FormBreakpoint>;
    configs: FormConfig;
    overlay: FormBreakpoints<{ width: number; height: number }>;
}
