import { FC, memo } from 'react';
import { Text, BlockStack, InlineGrid } from '@shopify/polaris';
import { ItemChannel } from './ItemChannel';
import { supportChannels } from './configs';
import * as sc from './styled';

export const SupportChannels: FC = memo(() => {
    return (
        <sc.Wrapper
            boxShadow="var(--p-shadow-bevel-100)"
            cornerRadius="12px"
            padding="20px 16px"
            backgroundColor="var(--p-color-bg-surface)"
        >
            <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                    Support Channels
                </Text>
                <InlineGrid columns={{ xs: 1, sm: 1, md: 2, lg: 4 }} gap="300">
                    {supportChannels.map((channel) => (
                        <ItemChannel key={channel.id} {...channel} />
                    ))}
                </InlineGrid>
            </BlockStack>
        </sc.Wrapper>
    );
});
