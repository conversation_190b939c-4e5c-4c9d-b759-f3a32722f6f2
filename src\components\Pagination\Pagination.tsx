import { FC } from 'react';
import { InlineStack, InlineStackProps } from '@shopify/polaris';
import { PaginationRoot, PaginationRootProps } from './PaginationRoot';
import { PaginationItems } from './PaginationItems';
import { PaginationNext } from './PaginationNext';
import { PaginationPrevious } from './PaginationPrevious';

interface PaginationProps extends PaginationRootProps {
    gap?: InlineStackProps['gap'];
}

export const Pagination: FC<PaginationProps> & {
    Root: typeof PaginationRoot;
    Items: typeof PaginationItems;
    Next: typeof PaginationNext;
    Previous: typeof PaginationPrevious;
} = ({ gap = '200', ...others }) => {
    return (
        <PaginationRoot {...others}>
            <InlineStack gap={gap}>
                <PaginationPrevious />
                <PaginationItems />
                <PaginationNext />
            </InlineStack>
        </PaginationRoot>
    );
};

Pagination.Root = PaginationRoot;
Pagination.Items = PaginationItems;
Pagination.Next = PaginationNext;
Pagination.Previous = PaginationPrevious;
