import { FC, ReactNode } from 'react';
import { Text } from '@shopify/polaris';
import { Auto_BlockToolbar, Auto_BlockType } from '@giaminhautoketing/auto-builder';
import { BlockToolbar } from '../../BlockToolbar';

interface BasePreviewLayoutProps {
    items: {
        label: string;
        icon: ReactNode;
        settings: Auto_BlockToolbar;
    }[];
}

export const BasePreviewLayout: FC<BasePreviewLayoutProps> = ({ items }) => {
    return (
        <div
            css={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, minmax(0px, 1fr))',
                gap: '8px',
            }}
        >
            {items.map(({ icon, label, settings }, index) => {
                const { bpConfigs, id, cname, type, overlay, configs, label: blockLabel } = settings;
                return (
                    <BlockToolbar
                        key={index}
                        id={id}
                        cname={cname}
                        label={blockLabel}
                        type={type as Auto_BlockType}
                        bpConfigs={bpConfigs}
                        configs={configs}
                        overlay={overlay}
                        css={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center' }}
                    >
                        <div
                            css={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '100%',
                                height: '70px',
                                borderRadius: '4px',
                                border: '1px solid rgba(235, 235, 235, 1)',
                                ':hover': {
                                    borderColor: 'rgba(138, 138, 138, 1)',
                                },
                            }}
                        >
                            {icon}
                        </div>
                        <Text variant="bodySm" as="p">
                            {label}
                        </Text>
                    </BlockToolbar>
                );
            })}
        </div>
    );
};
