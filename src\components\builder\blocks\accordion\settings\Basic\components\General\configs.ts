import { ChevronDownIcon, PlusIcon } from '@shopify/polaris-icons';
import { ReactComponent as AlignLeftIcon } from '@/assets/svgs/SwitchTabs/align-left.svg';
import { ReactComponent as AlignRightIcon } from '@/assets/svgs/SwitchTabs/align-right.svg';

export const iconPositionOptions = [
    {
        id: 'left',
        contentTooltip: 'Left',
        icon: AlignLeftIcon,
    },
    {
        id: 'right',
        contentTooltip: 'Right',
        icon: AlignRightIcon,
    },
];

export const iconTypeOptions = [
    {
        id: 'arrow',
        contentTooltip: 'Arrow',
        icon: ChevronDownIcon,
    },
    {
        id: 'plus',
        contentTooltip: 'Plus',
        icon: PlusIcon,
    },
];

export const generalOptions = [
    {
        id: 'header',
        contentTooltip: 'Header',
    },
    {
        id: 'collapse',
        contentTooltip: 'Collapse',
    },
];
