:root {
    --atk-colors-baseBorder: #ebebeb;
    --atk-radii-base: 0.5rem;
    --atk-sizes-full: 100%;
    --atk-radii-base: 0.25rem;
}

.setting-space-container {
    position: relative;
    height: 11.5rem;
    border-width: 1px;
    border-style: solid;
    border-color: var(--atk-colors-baseBorder);
    border-radius: var(--atk-radii-base);

    &--margin {
        position: absolute;
        width: var(--atk-sizes-full);
        height: var(--atk-sizes-full);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        user-select: none;
        border-radius: var(--atk-radii-base);
    }

    &--padding {
        position: absolute;
        width: 9.8rem;
        height: 6.6875rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        user-select: none;
        border-width: 1px;
        border-style: dashed;
        border-color: #ebebeb;
    }

    &--link {
        position: absolute;
        border: none;
        outline: none;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 1.625rem;
        height: 1.625rem;
        cursor: pointer;
        background-color: #8a8a8a;
        border-radius: 5px;
    }
}
