.input-suffix {
    position: relative;
    width: 100%;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    display: flex;
    overflow: hidden;
    max-width: 58px;
    width: 100%;

    input {
        border: none;
        padding: 5px;
        width: 100%;
        text-align: center;
        font-size: 12px;
        line-height: 15px;
        background: white;

        &:focus {
            outline: none;
        }
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    }

    .suffix {
        background-color: #f1f1f1;
        color: #616161;
        font-size: 12px;
        padding: 3px 5px;
        display: flex;
        align-items: center;
    }

    &.active {
        outline: var(--p-border-width-050) solid var(--p-color-border-focus);
        outline-offset: var(--p-space-025);
        border-color: #8a8a8a;
    }
}
