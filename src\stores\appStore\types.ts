import type { AppSettingsActions, AppSettingsState } from './appSettings/types';
import type { PageState, PageActions } from './pages/types';
import type { EditorState, EditorActions } from './editor/types';
import type { cssState, cssAction } from './css/types';
// Auth Slice
export interface AuthState {
    token: string;
    isAuthenticated: boolean;
    accordion: {
        [blockId: string]: {
            activeId: string;
        };
    };
}

export interface AuthActions {
    setToken: (token: string) => void;
    setIsAuthenticated: (isAuthenticated: boolean) => void;
    setAccordion: (blockId: string, activeId: string) => void;
}

export type AppState = AuthState & PageState & AppSettingsState & EditorState & cssState;
export type AppActions = AuthActions & PageActions & AppSettingsActions & EditorActions & cssAction;

export type AppStore = AppState & AppActions;

export type Status = 'all' | 'published' | 'draft' | 'saved' | 'default';

export interface IndustryCategory {
    id: number;
    name: string;
}

export interface ItemManageProps {
    [x: string]: number | string | undefined;
    value: string;
    title: string;
    image?: string;
}

export interface AppSettings {
    accountEmail: {
        emailSubscribed: string;
        isSubscribed: number;
    };
    accountStatus: {
        shopifyDomain: string;
    };
    language: string;
    shopName: string;
    storefrontPassword: string;
}
