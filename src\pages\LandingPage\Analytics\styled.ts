import styled from '@emotion/styled';

export const QuantityContainer = styled.div`
    .Polaris-Grid {
        gap: 20px;
        .Polaris-Grid-Cell {
            .Polaris-Box {
                padding-top: 13px;
                .Polaris-Divider {
                    margin-top: 14px;
                    margin-bottom: 12px;
                }
                .Polaris-Text--headingXl {
                    line-height: 31px;
                    font-size: 26px;
                }
                .Polaris-InlineStack {
                    .Polaris-InlineStack {
                        .Polaris-Icon {
                            width: 16px;
                            height: 16px;
                        }
                        .Polaris-Text--root {
                            line-height: 31px;
                        }
                    }
                }
            }
        }
    }
    .apexcharts-canvas {
        margin-top: 40px;
        box-shadow: 0px 1px 0px 0px #e3e3e3 inset, 1px 0px 0px 0px #e3e3e3 inset, -1px 0px 0px 0px #e3e3e3 inset,
            0px -1px 0px 0px #b5b5b5 inset;
        border-radius: 12px;
    }
`;
