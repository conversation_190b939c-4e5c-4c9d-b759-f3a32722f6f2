import { useRef, useEffect, useCallback } from 'react';

export function useEventCallback<T, K>(handler?: (value: T, event: K) => void): (value: T, event: K) => void {
    const callbackRef = useRef(handler);

    useEffect(() => {
        callbackRef.current = handler;
    });

    return useCallback((value: T, event: K) => callbackRef.current && callbackRef.current(value, event), []);
}

export function useMergeRefs<T>(...refs: (React.ForwardedRef<T> | React.RefObject<T | null>)[]) {
    return (r: T) => {
        for (const ref of refs) {
            if (typeof ref === 'function') {
                ref(r);
            } else if (ref) {
                ref.current = r;
            }
        }
    };
}

export const isTouch = (event: MouseEvent | TouchEvent): event is TouchEvent => 'touches' in event;

export const preventDefaultMove = (event: MouseEvent | TouchEvent): void => {
    if (!isTouch(event)) {
        event.preventDefault();
    }
};

export const clamp = (number: number, min = 0, max = 1): number => {
    return number > max ? max : number < min ? min : number;
};

export const getRelativePosition = (node: HTMLDivElement, event: MouseEvent | TouchEvent) => {
    const rect = node.getBoundingClientRect();

    const pointer = isTouch(event) ? event.touches[0] : (event as MouseEvent);

    return {
        left: clamp((pointer.pageX - (rect.left + window.pageXOffset)) / rect.width),
        top: clamp((pointer.pageY - (rect.top + window.pageYOffset)) / rect.height),
        width: rect.width,
        height: rect.height,
        x: pointer.pageX - (rect.left + window.pageXOffset),
        y: pointer.pageY - (rect.top + window.pageYOffset),
    };
};
