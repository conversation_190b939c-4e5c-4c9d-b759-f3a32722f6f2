import { ReactNode } from 'react';
import { Auto_BlockToolbar } from '@giaminhautoketing/auto-builder';
import { ReactComponent as Video1Icon } from '@/assets/svgs/w-video-1.svg';
import { ReactComponent as Video2Icon } from '@/assets/svgs/w-video-2.svg';
import * as settings from './data';

export const previewConfigs: {
    label: string;
    icon: ReactNode;
    settings: Auto_BlockToolbar;
}[] = [
    {
        label: 'Video',
        icon: <Video1Icon />,
        settings: settings.video1,
    },
    {
        label: 'Video',
        icon: <Video2Icon />,
        settings: settings.video2,
    },
];
