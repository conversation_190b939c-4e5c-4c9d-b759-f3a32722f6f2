import { FC } from 'react';
import { Icon } from '@shopify/polaris';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import {
    SettingsSelect,
    SettingsCheckbox,
    SettingsSliderInput,
    SettingsSwitchTab,
} from '@/components/builder/settings';
import { InfoIcon } from '@shopify/polaris-icons';
import { getBlockProperty } from '@/utils/shared';

import { buttonDirectionOptions } from '@/components/builder/data/options';
import {
    useFormFieldPaths,
    useHandleAddOption,
} from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { BaseOptionSettings } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/BaseOptionSettings';

interface RadioSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget?: HTMLElement;
}

export const RadioSettings: FC<RadioSettingsProps> = ({ formField, selectedBlockTarget }) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH, DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const showInitialText = getBlockProperty(`${DEFAULT_PATH}.showInitialText`, selectedBlockId);
    const opts = getBlockProperty(`${DEFAULT_PATH}.options`, selectedBlockId);

    return (
        <BaseOptionSettings
            formField={formField}
            type="radio"
            handleAddOption={useHandleAddOption(formField, opts)}
            insertPosition={[3, 4, 5, 6]}
            selectedBlockTarget={selectedBlockTarget as HTMLElement}
        >
            <SettingsSelect
                path={`${DEFAULT_PATH_FORM}.showInitialText`}
                blockId={selectedBlockId}
                isUpdateConfigs
                direction="column"
                label="Show initial text"
                options={[
                    { id: 'none', content: 'None' },
                    { id: 'default', content: 'Default' },
                ]}
                textProps={{ fontWeight: 'medium' }}
                tooltipContent="Display suggested content in fields to help customers enter correct content"
                hasTooltip
                tooltipChildren={<Icon source={InfoIcon} />}
            />

            {showInitialText === 'default' && (
                <SettingsSelect
                    path={`${DEFAULT_PATH_FORM}.initialText`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    options={opts?.map((item: { value: string; label: string }) => ({
                        id: item.value,
                        content: item.label,
                    }))}
                    direction="column"
                    label="Default"
                    textProps={{ fontWeight: 'medium' }}
                />
            )}

            <SettingsSwitchTab
                path={`${DEFAULT_PATH_FORM}.direction`}
                blockId={selectedBlockId}
                isUpdateConfigs
                label="Direction"
                options={buttonDirectionOptions}
            />

            <SettingsCheckbox
                path={`${DEFAULT_PATH_FORM}.validations.required`}
                blockId={selectedBlockId}
                isUpdateConfigs
                direction="column"
                label="Required"
                textProps={{ fontWeight: 'medium', as: 'p', children: 'Validations' }}
            />

            <SettingsSliderInput
                selectedBlockTarget={selectedBlockTarget as HTMLElement}
                cssVariable={`--form-${formField.key}-radio-size`}
                path={`fieldSizes.${formField.key}.radioSize`}
                blockId={selectedBlockId}
                isUpdateConfigs={false}
                direction="column"
                title="Radio size"
                min={10}
                max={100}
                step={1}
                inputProps={{
                    min: 10,
                    max: 100,
                    step: 1,
                    suffix: 'px',
                }}
            />
        </BaseOptionSettings>
    );
};
