import { ReactElement, isValidElement, cloneElement } from 'react';

interface SlotProps {
    children: ReactElement;
    style?: React.CSSProperties;
    [key: string]: unknown;
}

export const createSlot = ({ children, ...props }: SlotProps) => {
    if (!isValidElement(children)) {
        return null;
    }

    const childProps = children.props as Record<string, unknown>;

    const mergedProps = {
        ...props,
        ...childProps,
        style: {
            ...((props.style as React.CSSProperties) || {}),
            ...((childProps.style as React.CSSProperties) || {}),
        },
    };

    return cloneElement(children, mergedProps);
};

export type AsChildProps<DefaultElementProps> =
    | ({ asChild?: false } & DefaultElementProps)
    | { asChild: true; children: ReactElement };
