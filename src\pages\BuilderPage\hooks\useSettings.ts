import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';

interface UseSettingsProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export function useSettings<T>({ blockId, path, isUpdateConfigs }: UseSettingsProps) {
    const value = isUpdateConfigs ? getBlockProperty(`configs.${path}`, blockId) : getBlockBPProperty(path, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const updateValue = (value: T) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, value as string);
        } else {
            updateBlockProperty(blockId, currentDevice, path, value as string);
        }
    };

    return { value, updateValue };
}
