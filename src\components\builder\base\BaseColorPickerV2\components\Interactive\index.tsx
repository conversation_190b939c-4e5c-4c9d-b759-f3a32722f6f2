import React, {
    useRef,
    useState,
    useCallback,
    useEffect,
    ForwardRefRenderFunction,
    forwardRef,
    HTMLAttributes,
} from 'react';
import { isTouch, preventDefaultMove, getRelativePosition, useEventCallback, useMergeRefs } from '../../helper';

export interface Interaction {
    left: number;
    top: number;
    width: number;
    height: number;
    x: number;
    y: number;
}

export interface InteractiveProps extends HTMLAttributes<HTMLDivElement> {
    onMove?: (interaction: Interaction, event: MouseEvent | TouchEvent) => void;
    onDown?: (offset: Interaction, event: MouseEvent | TouchEvent) => void;
}

const InteractiveFR: ForwardRefRenderFunction<HTMLDivElement, InteractiveProps> = (
    { onMove, onDown, ...otherProps },
    ref,
) => {
    const [isDragging, setDragging] = useState(false);
    const hasTouched = useRef(false);
    const container = useRef<HTMLDivElement>(null);
    const mergeRef = useMergeRefs(container, ref);
    const onMoveCallback = useEventCallback<Interaction, MouseEvent | TouchEvent>(onMove);
    const onKeyCallback = useEventCallback<Interaction, MouseEvent | TouchEvent>(onDown);

    const isValid = (event: MouseEvent | TouchEvent): boolean => {
        if (hasTouched.current && !isTouch(event)) return false;
        hasTouched.current = isTouch(event);
        return true;
    };

    const handleMoveStart = useCallback(
        (event: React.MouseEvent | React.TouchEvent) => {
            preventDefaultMove(event.nativeEvent);
            if (!isValid(event.nativeEvent)) return;
            if (onKeyCallback) {
                onKeyCallback(getRelativePosition(container.current!, event.nativeEvent), event.nativeEvent);
            }
            setDragging(true);
        },
        [onKeyCallback],
    );

    const handleMove = useCallback(
        (event: MouseEvent | TouchEvent) => {
            preventDefaultMove(event);
            const isDown = isTouch(event) ? event.touches.length > 0 : event.buttons > 0;
            if (isDown && container.current) {
                if (onMoveCallback) {
                    onMoveCallback(getRelativePosition(container.current!, event), event);
                }
            } else {
                setDragging(false);
            }
        },
        [onMoveCallback],
    );

    const handleMoveEnd = useCallback(() => setDragging(false), []);

    const toggleDocumentEvents = useCallback((state: boolean) => {
        const toggleEvent = state ? window.addEventListener : window.removeEventListener;
        toggleEvent(hasTouched.current ? 'touchmove' : 'mousemove', handleMove);
        toggleEvent(hasTouched.current ? 'touchend' : 'mouseup', handleMoveEnd);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        toggleDocumentEvents(isDragging);
        return () => {
            if (isDragging) {
                toggleDocumentEvents(false);
            }
        };
    }, [isDragging, toggleDocumentEvents]);

    return (
        <div
            ref={mergeRef}
            tabIndex={0}
            onMouseDown={handleMoveStart}
            onTouchStart={handleMoveStart}
            css={{ touchAction: 'none' }}
            {...otherProps}
        />
    );
};

export const Interactive = forwardRef(InteractiveFR);
