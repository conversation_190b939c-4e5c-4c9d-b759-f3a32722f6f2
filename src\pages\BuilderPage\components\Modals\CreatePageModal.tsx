import { BlockStack, List, TextField } from '@shopify/polaris';
import { FC, useState } from 'react';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { Box } from '@shopify/polaris';
import { RequestName } from '@/stores/appStore/pages/types';

interface CreatePageModalProps {
    modalChannel: BroadcastChannel;
    isLoading: (requestName: RequestName) => boolean;
}

export const CreatePageModal: FC<CreatePageModalProps> = ({ modalChannel, isLoading }) => {
    const [pageTitle, setPageTitle] = useState('Untitled');
    const handleCloseCreateModal = () => {
        shopify.modal.hide('builder-modal-create');
    };

    const handleChangePageTitle = (value: string) => {
        setPageTitle(value);
    };

    const handleCreate = () => {
        modalChannel.postMessage({
            action: 'Create',
            title: pageTitle,
        });
    };

    return (
        <Modal id="builder-modal-create" variant="base">
            <TitleBar title="Create new page">
                <button onClick={handleCloseCreateModal}>Cancel</button>
                <button variant={'primary'} onClick={handleCreate} loading={isLoading('createPage') ? '' : undefined}>
                    Create
                </button>
            </TitleBar>
            <Box padding="400" paddingBlockEnd="800">
                <BlockStack gap="400">
                    <TextField
                        label="Page title"
                        value={pageTitle}
                        onChange={handleChangePageTitle}
                        autoComplete="off"
                        requiredIndicator
                    />
                    <List type="bullet">
                        <List.Item>Once you click "save", the page will be ready for preview.</List.Item>
                        <List.Item>Publish the page to see the new changes you have made.</List.Item>
                    </List>
                </BlockStack>
            </Box>
        </Modal>
    );
};
