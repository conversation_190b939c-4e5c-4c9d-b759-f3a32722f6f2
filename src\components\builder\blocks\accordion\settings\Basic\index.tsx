import { FC } from 'react';
import { BlockStack } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingElementID } from '@/components/builder/settings/SettingElementID';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { Styles } from './components/Styles';
import { General } from './components/General';
import { Border } from './components/Border';

export const Basic: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <>
            <BaseHasBorderLayout>
                <BlockStack gap="400">
                    <SettingElementID value={selectedBlockId} />
                    <Styles id={selectedBlockId} label="Styles" />
                    <General id={selectedBlockId} label="General" isUpdateConfigs />
                    <Border id={selectedBlockId} label="Border" />
                </BlockStack>
            </BaseHasBorderLayout>
        </>
    );
};
