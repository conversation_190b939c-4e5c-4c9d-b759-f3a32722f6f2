import { FC } from 'react';
import { BlockStack } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { BaseInputFieldSettings } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/BaseInputFieldSettings';
import { SettingsInput, SettingsSelect, SettingsSliderInput, SettingsToggle } from '@/components/builder/settings';
import { useFormFieldPaths } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { getBlockProperty } from '@/utils/shared';
import { capitalizeFirstLetter } from '@/utils';

interface NumberSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget?: HTMLElement;
}

export const NumberFormatSettings: FC<{ path: string; blockId: string }> = ({ path, blockId }) => (
    <SettingsSelect
        path={`${path}.numberFormat`}
        blockId={blockId}
        isUpdateConfigs
        direction="column"
        label="Number format"
        options={[
            { id: 'whole-number', content: 'Whole number' },
            { id: 'decimal-number', content: 'Decimal number' },
        ]}
    />
);

export const NumberOfDigitsAfterDecimalSettings: FC<{
    path: string;
    blockId: string;
    selectedBlockTarget?: HTMLElement;
}> = ({ path, blockId, selectedBlockTarget }) => {
    return (
        <SettingsSliderInput
            selectedBlockTarget={selectedBlockTarget as HTMLElement}
            cssVariable={`--form-digitsAfterDecimal`}
            path={`${path}.digitsAfterDecimal`}
            blockId={blockId}
            isUpdateConfigs
            direction="column"
            title="Number of digits after decimal"
            min={0}
            max={5}
            step={1}
            inputProps={{
                align: 'center',
                min: 0,
                max: 5,
                step: 1,
            }}
        />
    );
};

const ToggleValueSetting: FC<{
    path: string;
    blockId: string;
    label: string;
    toggleKey: 'maxValue' | 'minValue';
    showInput: boolean;
    placeholder: string;
}> = ({ path, blockId, label, toggleKey, showInput, placeholder }) => (
    <>
        <SettingsToggle
            path={`${path}.${toggleKey}.add${capitalizeFirstLetter(toggleKey)}`}
            blockId={blockId}
            isUpdateConfigs
            label={`Set a ${toggleKey === 'maxValue' ? 'maximum' : 'minimum'} value`}
            toggleProps={{
                id: `${path}.${toggleKey}.add${capitalizeFirstLetter(toggleKey)}`,
            }}
            textProps={{ fontWeight: 'medium' }}
            containerClassName="number-settings"
        />
        {showInput && (
            <SettingsInput
                path={`${path}.${toggleKey}`}
                blockId={blockId}
                isUpdateConfigs
                label={label}
                inputProps={{ placeholder, id: `form_${toggleKey}` }}
                containerClassName="number-settings"
            />
        )}
    </>
);

export const NumberSettings: FC<NumberSettingsProps> = ({ formField }) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH, DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const addMaxValue = getBlockProperty(`${DEFAULT_PATH}.maxValue.addMaxValue`, selectedBlockId);
    const addMinValue = getBlockProperty(`${DEFAULT_PATH}.minValue.addMinValue`, selectedBlockId);
    const numberFormat = getBlockProperty(`${DEFAULT_PATH}.numberFormat`, selectedBlockId);

    const customSettings = (
        <BlockStack gap="400">
            <NumberFormatSettings key="number-format" path={DEFAULT_PATH_FORM} blockId={selectedBlockId} />
            {numberFormat === 'decimal-number' && (
                <NumberOfDigitsAfterDecimalSettings
                    key="digits-after-decimal"
                    path={DEFAULT_PATH_FORM}
                    blockId={selectedBlockId}
                />
            )}
            <ToggleValueSetting
                key="max-value"
                path={DEFAULT_PATH_FORM}
                blockId={selectedBlockId}
                label="Add maximum value"
                toggleKey="maxValue"
                showInput={addMaxValue}
                placeholder="Enter max value"
            />
            <ToggleValueSetting
                key="min-value"
                path={DEFAULT_PATH_FORM}
                blockId={selectedBlockId}
                label="Add minimum value"
                toggleKey="minValue"
                showInput={addMinValue}
                placeholder="Enter min value"
            />
        </BlockStack>
    );

    return (
        <BaseInputFieldSettings
            formField={formField}
            customSettings={customSettings}
            insertPosition={4}
            showCharLimit={false}
        />
    );
};
