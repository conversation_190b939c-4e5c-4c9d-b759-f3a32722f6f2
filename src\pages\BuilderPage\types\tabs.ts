import { IconSource } from '@shopify/polaris';

export interface TabItemProps {
    id: string;
    title: string | React.ReactNode;
    icon: string | IconSource;
    description?: string;
    type: string;
}

export type CategoryProps = {
    name: string;
    id: string;
    items: TabItemProps[];
};

export type TabProps = {
    id: string;
    name: string;
    searchPlaceholder: string;
    categories: CategoryProps[];
};
