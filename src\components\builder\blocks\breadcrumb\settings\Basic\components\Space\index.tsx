import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BlockStack } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsSliderInput } from '@/components/builder/settings/SettingsSliderInput';
interface SpaceProps {
    id: string;
}

export const Space: FC<SpaceProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Space"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <div>
                            <SettingsSliderInput
                                path={`spaceBetweenTextAndIcon`}
                                blockId={id}
                                isUpdateConfigs={false}
                                title="Space between text and icon"
                                max={64}
                                min={2}
                                inputProps={{
                                    suffix: 'px',
                                    min: 2,
                                    step: 1,
                                }}
                                direction="column"
                            />
                        </div>
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
