import { useState } from 'react';
import { Icon, Tooltip } from '@shopify/polaris';
import { ColorIcon } from '@shopify/polaris-icons';
import { BaseColorPickerV2 } from '@/components/builder/base';
import { useRTE } from '../context';

export function Highlight() {
    const [color, setColor] = useState('#000000');
    const { editor } = useRTE();

    return (
        <BaseColorPickerV2
            color={color}
            onChange={(value) => {
                editor?.chain().setSpotlightEnabled(true).spotlightSelection().run();
                editor?.chain().setHighlight({ color: value }).run();
                setColor(value);
            }}
            popoverOptions={{
                offsetOptions: {
                    mainAxis: 56,
                    crossAxis: -5,
                },
            }}
            triggerRender={
                <div>
                    <Tooltip content="Background Color">
                        <button className="Polaris-Button rte-tool">
                            <Icon source={ColorIcon} tone="base" />
                        </button>
                    </Tooltip>
                </div>
            }
        />
    );
}
