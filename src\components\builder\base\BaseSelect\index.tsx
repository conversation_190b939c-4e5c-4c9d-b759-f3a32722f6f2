/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useState, useCallback, useMemo } from 'react';
import { Popover, ActionList, Button, InlineStack, ActionListItemDescriptor, Icon, Tooltip } from '@shopify/polaris';
import { CheckIcon } from '@shopify/polaris-icons';
import clsx from 'clsx';
import './styles.scss';

export interface BaseSelectProps {
    options: ActionListItemDescriptor[];
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    className?: string;
    fullWidth?: boolean;
    disabled?: boolean;
    tooltip?: string;
    valueMap?: Record<string, string>;
    isActiveCheck?: (value: string) => boolean;
}

export const BaseSelect: FC<BaseSelectProps> = ({
    options,
    value,
    onChange,
    placeholder = 'Select an option',
    className,
    fullWidth = true,
    disabled = false,
    tooltip,
    valueMap,
    isActiveCheck,
}) => {
    const [popoverActive, setPopoverActive] = useState(false);
    const togglePopoverActive = useCallback(() => setPopoverActive((popoverActive) => !popoverActive), []);

    const findIdByValue = useCallback(
        (val: string | undefined) => {
            if (!valueMap || val === undefined) return val?.toString();

            const entry = Object.entries(valueMap).find(([_, mappedValue]) => mappedValue === val);
            return entry ? entry[0] : val?.toString();
        },
        [valueMap],
    );

    const displayId = useMemo(() => {
        if (isActiveCheck && valueMap) {
            const activeId = Object.entries(valueMap).find(([_, val]) => isActiveCheck(val))?.[0];
            return activeId || findIdByValue(value);
        }
        return findIdByValue(value);
    }, [findIdByValue, value, isActiveCheck, valueMap]);

    const selectedOption = useMemo(() => options.find((opt) => opt.id === displayId), [options, displayId]);

    const displayContent = selectedOption?.content || placeholder;

    const handleChange = useCallback(
        (newId: string) => {
            const actualValue = valueMap ? valueMap[newId] : newId;
            onChange?.(actualValue);
            setPopoverActive(false);
        },
        [onChange, valueMap],
    );

    const actionListItems = useMemo(
        () =>
            options.map((item) => ({
                ...item,
                active: item.id === displayId,
                onAction: () => handleChange(item.id as string),
                variant: 'menu' as const,
                suffix: item.id === displayId ? <Icon source={CheckIcon} tone="info" /> : null,
                truncate: true,
            })),
        [options, displayId, handleChange],
    );

    const buttonContent = (
        <Button
            id="base-select-activator"
            pressed={popoverActive}
            variant="secondary"
            size="large"
            fullWidth={fullWidth}
            textAlign="left"
            disclosure="select"
            onClick={togglePopoverActive}
            disabled={disabled}
        >
            {displayContent}
        </Button>
    );

    const activator = tooltip ? (
        <Tooltip content={tooltip} preferredPosition="above" dismissOnMouseOut activatorWrapper="div">
            {buttonContent}
        </Tooltip>
    ) : (
        buttonContent
    );

    return (
        <div className={clsx('base-select', className)}>
            <InlineStack blockAlign="center" gap="150" wrap={false}>
                <div className="base-select-container">
                    <Popover
                        active={popoverActive && !disabled}
                        activator={activator}
                        autofocusTarget="first-node"
                        onClose={togglePopoverActive}
                        fullWidth
                    >
                        <Popover.Pane>
                            <div className="base-select-dropdown" style={{ width: '100%' }}>
                                <ActionList actionRole="menuitem" items={actionListItems} />
                            </div>
                        </Popover.Pane>
                    </Popover>
                </div>
            </InlineStack>
        </div>
    );
};
