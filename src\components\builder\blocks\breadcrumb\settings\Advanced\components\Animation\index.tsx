import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsAnimation } from '@/components/builder/settings/SettingsAnimation';
interface AnimationProps {
    id: string;
}

export const Animation: FC<AnimationProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Animation"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <SettingsAnimation path="animation" blockId={id} isUpdateConfigs />
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
