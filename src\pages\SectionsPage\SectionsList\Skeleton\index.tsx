import { FC } from 'react';
import { SkeletonBodyText } from '@shopify/polaris';
import { SkeletonItem } from './SkeletonItem';

interface SkeletonProps {
    count: number;
    active?: boolean;
}

export const Skeleton: FC<SkeletonProps> = ({ count, active }) => {
    if (!active) return null;
    return (
        <div>
            <div className="skeleton-header">
                <SkeletonBodyText lines={1} />
            </div>
            {Array.from({ length: count }).map((_, index) => (
                <SkeletonItem key={index} />
            ))}
            <div className="skeleton-footer">
                <SkeletonBodyText lines={1} />
            </div>
        </div>
    );
};
