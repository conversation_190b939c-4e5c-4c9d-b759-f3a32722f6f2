import { BlockStack, Box, Divider } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsChooseImage, SettingElementID, SettingsInput } from '@/components/builder/settings';

export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <Box paddingInline="200" paddingBlockStart="400">
            <BlockStack gap="300">
                <SettingElementID value={selectedBlockId} />
                <SettingsChooseImage blockId={selectedBlockId} path="url" isUpdateConfigs />
                <Divider />
                <SettingsInput
                    label="Alt"
                    path="alt"
                    direction="column"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    inputProps={{ type: 'text', placeholder: 'Enter alt text' }}
                />
            </BlockStack>
        </Box>
    );
};
