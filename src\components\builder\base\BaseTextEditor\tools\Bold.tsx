import { TextBoldIcon } from '@shopify/polaris-icons';
import { useRTE } from '../context';
import { BaseTool } from './BaseTool';

export const Bold = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('bold');
    const toggleBold = () => {
        editor?.chain().focus().toggleBold().run();
    };
    return <BaseTool onClick={toggleBold} icon={TextBoldIcon} tooltip="Bold" isActive={isActive} />;
};
