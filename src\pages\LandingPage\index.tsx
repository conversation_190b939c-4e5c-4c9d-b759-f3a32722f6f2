import { FC, useCallback, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ActionList, Button, ButtonGroup, Page, Popover } from '@shopify/polaris';
import { IncomingIcon } from '@shopify/polaris-icons';
import { ImportModal } from './ImportModal';
import { pathnames } from '@/configs/pathNames';
import { PageList } from '@/components/PageList';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { BuilderModal } from '@/pages/BuilderPage/components/Modals/BuilderModal';
export const LandingPage: FC = () => {
    const navigate = useNavigate();
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const isAuthenticated = useAppStore((state) => state.isAuthenticated);
    const templateType = useAppStore((state) => state.templateType);
    const setTemplateType = useAppStore((state) => state.setTemplateType);
    const setPageCreationStatus = useAppStore((state) => state.setPageCreationStatus);
    const setTemplateTypeId = useAppStore((state) => state.setTemplateTypeId);

    useEffect(() => {
        if (isAuthenticated) {
            setTemplateType();
        }
    }, [isAuthenticated, setTemplateType]);

    const handleOpenImportModal = useCallback(() => {
        setIsImportModalOpen(true);
    }, []);

    const handleCloseImportModal = useCallback(() => {
        setIsImportModalOpen(false);
    }, []);

    const handleCreateBlankPage = useCallback(() => {
        setIsPopoverOpen(true);
    }, []);

    const handleActionAnyItem = useCallback(
        (id: number) => {
            setPageCreationStatus('starting');
            setTemplateTypeId(id);
            shopify.modal.show('builder-modal');
        },
        [setPageCreationStatus, setTemplateTypeId],
    );

    const activator = <Button onClick={handleCreateBlankPage}>Create blank page</Button>;

    const items = templateType.map((item) => ({
        content: item.typeName,
        id: item.id.toString(),
        onAction: () => handleActionAnyItem(item.id),
    }));
    return (
        <>
            <Page
                title="Landing & Pages"
                secondaryActions={
                    <ButtonGroup>
                        <Button icon={IncomingIcon} onClick={handleOpenImportModal}>
                            Import
                        </Button>
                        <Popover activator={activator} active={isPopoverOpen} onClose={() => setIsPopoverOpen(false)}>
                            <Popover.Pane>
                                <ActionList actionRole="menuitem" items={items} />
                            </Popover.Pane>
                        </Popover>
                    </ButtonGroup>
                }
                primaryAction={{
                    content: 'Create from template',
                    onAction: () => navigate(pathnames.createPage),
                }}
            >
                <PageList />
            </Page>

            <ImportModal isImportModalOpen={isImportModalOpen} handleCloseImportModal={handleCloseImportModal} />
            <BuilderModal />
        </>
    );
};

export { CreatePage } from './CreatePage';
export { Analytics } from './Analytics';
