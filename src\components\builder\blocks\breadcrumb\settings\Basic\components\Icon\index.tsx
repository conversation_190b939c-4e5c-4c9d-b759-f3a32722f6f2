import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BlockStack } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsColorPicker, SettingsSliderInput } from '@/components/builder/settings';
import { SettingsIcon } from '@/components/builder/settings/SettingsIcon';
interface StylesProps {
    id: string;
}

const DEFAULT_PATH = {
    SEPARATOR_ICON: 'content.separatorIcon',
};
export const Icons: FC<StylesProps> = ({ id }) => {
    return (
        <BaseCollapse label="Icon" labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}>
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <SettingsIcon
                            path={`${DEFAULT_PATH.SEPARATOR_ICON}.icon`}
                            blockId={id}
                            isUpdateConfigs
                            direction="column"
                            label=""
                        />
                        <SettingsColorPicker
                            path={`${DEFAULT_PATH.SEPARATOR_ICON}.color`}
                            blockId={id}
                            isUpdateConfigs
                            label="Icon Color"
                        />
                        <SettingsSliderInput
                            path={`${DEFAULT_PATH.SEPARATOR_ICON}.iconSize`}
                            blockId={id}
                            isUpdateConfigs
                            title="Icon size"
                            max={64}
                            min={8}
                            inputProps={{
                                suffix: 'px',
                                min: 8,
                                step: 1,
                            }}
                            direction="column"
                        />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
