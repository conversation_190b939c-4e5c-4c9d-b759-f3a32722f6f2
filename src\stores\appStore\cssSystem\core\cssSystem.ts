import { CSSProperties } from 'react';
import { Auto_BlockData, useBlockStore } from '@giaminhautoketing/auto-builder';
import { Breakpoint, Generator, ValueOf } from './types';
import { cssObjectToString, mapObjectKeys } from './helper';

export class CssSystem {
    private static instance: CssSystem;
    private styleSheet: CSSStyleSheet | null = null;
    public cssGenerators: Record<string, Generator[] | ((blockData: Auto_BlockData) => Generator[])> = {};
    public editorRules: Record<string, Record<string, CSSProperties>> = {};
    public publishRules: Record<string, Record<string, CSSProperties>> = {};

    private constructor() {}

    public static getInstance() {
        if (!CssSystem.instance) {
            CssSystem.instance = new CssSystem();
        }
        return CssSystem.instance;
    }

    public get cssEditorString() {
        const cssDesktop = cssObjectToString(this.editorRules['desktop']);
        const cssMobile = cssObjectToString(
            mapObjectKeys(this.editorRules['mobile'], (selector) => `[data-device="mobile"] ${selector}`),
        );
        return cssDesktop + '\n' + cssMobile;
    }

    public get cssPublishString() {
        return cssObjectToString({
            ...this.publishRules['desktop'],
            '@media (max-width: 768px)': this.publishRules['mobile'],
        });
    }

    public registerCSSGenerator(data: Record<string, Generator[] | ((blockData: Auto_BlockData) => Generator[])>) {
        this.cssGenerators = data;
    }

    public rebuild() {
        this.editorRules = { desktop: {}, mobile: {} };
        this.publishRules = { desktop: {}, mobile: {} };

        const blocks = useBlockStore.getState().blocks;

        Object.keys(blocks).forEach((blockId) => {
            this.updateCssRules(blockId, blocks[blockId]);
        });

        this.applyCss();
    }
    private genResponsiveCSS(blockId: string, blockData: Auto_BlockData, breakpoint: Breakpoint) {
        const blockType = blockData.type;
        const editorCSS: Record<string, CSSProperties> = {};
        const publishCSS: Record<string, CSSProperties> = {};

        const generatorsData = this.cssGenerators[blockType];
        if (!generatorsData) return { editorCSS, publishCSS };

        const cssGenerators = typeof generatorsData === 'function' ? generatorsData(blockData) : generatorsData;

        cssGenerators.forEach(({ selector, generator, applyTo = 'all' }) => {
            const css = generator(blockData, breakpoint);
            const finalSelector = typeof selector === 'function' ? selector(blockId) : selector;

            if (applyTo === 'all' || applyTo === 'editor') {
                editorCSS[finalSelector] = { ...css, ...editorCSS[finalSelector] };
            }

            if (applyTo === 'all' || applyTo === 'publish') {
                publishCSS[finalSelector] = { ...css, ...publishCSS[finalSelector] };
            }
        });

        return { editorCSS, publishCSS };
    }

    private genDiffCSS(
        sourceCSS: Record<string, CSSProperties>,
        targetCSS: Record<string, CSSProperties>,
    ): Record<string, CSSProperties> {
        const diffCSS: Record<string, CSSProperties> = {};

        Object.keys(targetCSS).forEach((selector) => {
            if (!sourceCSS[selector]) {
                diffCSS[selector] = targetCSS[selector];
            } else {
                const sourceProps = sourceCSS[selector];
                const targetProps = targetCSS[selector];
                const diffProps: Record<string, ValueOf<CSSProperties>> = {};

                Object.keys(targetProps).forEach((prop) => {
                    const key = prop as keyof CSSProperties;
                    if (JSON.stringify(targetProps[key]) !== JSON.stringify(sourceProps[key])) {
                        diffProps[key] = targetProps[key];
                    }
                });

                if (Object.keys(diffProps).length > 0) {
                    diffCSS[selector] = diffProps;
                }
            }
        });

        return diffCSS;
    }

    public updateCssRules(blockId: string, blockData: Auto_BlockData) {
        const desktopResult = this.genResponsiveCSS(blockId, blockData, 'desktop');
        const mobileResult = this.genResponsiveCSS(blockId, blockData, 'mobile');

        const desktopEditorCSS = desktopResult.editorCSS;
        const mobileEditorCSS = mobileResult.editorCSS;
        const editorDiffCSS = this.genDiffCSS(desktopEditorCSS, mobileEditorCSS);

        const desktopPublishCSS = desktopResult.publishCSS;
        const mobilePublishCSS = mobileResult.publishCSS;
        const publishDiffCSS = this.genDiffCSS(desktopPublishCSS, mobilePublishCSS);

        this.editorRules['desktop'] = { ...this.editorRules['desktop'], ...desktopEditorCSS };
        this.editorRules['mobile'] = { ...this.editorRules['mobile'], ...editorDiffCSS };

        this.publishRules['desktop'] = { ...this.publishRules['desktop'], ...desktopPublishCSS };
        this.publishRules['mobile'] = { ...this.publishRules['mobile'], ...publishDiffCSS };
    }

    public applyCss() {
        if (!this.styleSheet) {
            this.styleSheet = new CSSStyleSheet();
            document.adoptedStyleSheets = [...document.adoptedStyleSheets, this.styleSheet];
        }

        this.styleSheet.replaceSync(this.cssEditorString);
    }
}
