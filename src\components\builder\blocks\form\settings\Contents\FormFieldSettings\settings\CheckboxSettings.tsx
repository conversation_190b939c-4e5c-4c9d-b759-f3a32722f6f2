/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsCheckbox, SettingsSliderInput, SettingsSwitchTab } from '@/components/builder/settings';
import { buttonDirectionOptions } from '@/components/builder/data/options';
import {
    useFormFieldPaths,
    useHandleAddOption,
} from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { BaseOptionSettings } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/BaseOptionSettings';
import { getBlockProperty } from '@/utils/shared';

interface CheckboxSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget?: HTMLElement;
}

export const CheckboxSettings: FC<CheckboxSettingsProps> = ({ formField, selectedBlockTarget }) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH, DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const opts = getBlockProperty(`${DEFAULT_PATH}.options`, selectedBlockId) as any;

    return (
        <>
            <BaseOptionSettings
                formField={formField}
                handleAddOption={useHandleAddOption(formField, opts)}
                type="checkbox"
                insertPosition={[3, 4, -1]}
                selectedBlockTarget={selectedBlockTarget as HTMLElement}
            >
                <SettingsSwitchTab
                    path={`${DEFAULT_PATH_FORM}.direction`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    label="Direction"
                    options={buttonDirectionOptions}
                />
                <SettingsCheckbox
                    path={`${DEFAULT_PATH_FORM}.validations.required`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    direction="column"
                    label="Required"
                    textProps={{ fontWeight: 'medium', children: 'Validations' }}
                />
                <SettingsSliderInput
                    selectedBlockTarget={selectedBlockTarget as HTMLElement}
                    cssVariable={`--form-${formField.key}-checkbox-size`}
                    path={`fieldSizes.${formField.key}.checkboxSize`}
                    blockId={selectedBlockId}
                    isUpdateConfigs={false}
                    direction="column"
                    title="Checkbox size"
                    min={10}
                    sliderProps={{
                        min: 10,
                        max: 100,
                        step: 1,
                    }}
                    inputProps={{
                        min: 10,
                        max: 100,
                        step: 1,
                        suffix: 'px',
                    }}
                />
            </BaseOptionSettings>
        </>
    );
};
