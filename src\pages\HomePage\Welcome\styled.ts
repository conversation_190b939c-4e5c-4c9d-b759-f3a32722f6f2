import styled from '@emotion/styled';

export const WelcomeContainer = styled.div`
    .Polaris-ShadowBevel > .Polaris-Box {
        padding: 20px 16px;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
    }
`;
export const WelcomeHeader = styled.div`
    .Polaris-Box {
        display: flex;
        column-gap: 8px;
        margin-bottom: 8px;
        h2.Polaris-Text--headingSm {
            font-weight: 650;
            font-size: 20px;
            letter-spacing: -0.2px;
            line-height: 24px;
        }
        .Polaris-SkeletonDisplayText--sizeSmall {
            width: 400px;
        }
    }

    span.Polaris-Text--subdued {
        font-size: 13px;
    }
`;
export const WelcomeContent = styled.div`
    .Polaris-Grid-Cell {
        a {
            display: flex;
            justify-content: space-between;
            column-gap: 16px;
            box-shadow: 0px 1px 0px 0px #e3e3e3 inset, 1px 0px 0px 0px #e3e3e3 inset, -1px 0px 0px 0px #e3e3e3 inset,
                0px -1px 0px 0px #b5b5b5 inset;
            border-radius: 12px;
            padding: 16px;
            height: 100%;
            text-decoration: none;
            &:hover {
                background: #fafafa;
            }
            .Polaris-Box {
                padding: 0;
                h2.Polaris-Text--headingSm {
                    font-size: 14px;
                    font-weight: 700;
                    margin-bottom: 4px;
                    color: #303030;
                }
                span.Polaris-Text--subdued {
                    font-weight: 450;
                    font-size: 13px;
                }
            }
            .Polaris-Icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 36px;
                min-width: 36px;
                background: #f1f4fe;
                color: #6086f2;
                border-radius: 8px;
                margin: 0;
                svg {
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }
`;
