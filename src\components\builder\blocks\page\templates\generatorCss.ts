import { CSSProperties } from 'react';
import { generateBackground, getResponsiveValue } from '@/stores/appStore/cssSystem';
import type { Generator, Background } from '@/stores/appStore/cssSystem';

export const pageCssGenerators: Generator[] = [
    {
        selector: () => `[data-auto-id="page"]`,
        generator: (blockData, breakpoint) => {
            const background = getResponsiveValue<Background>(blockData, 'background', breakpoint);
            const color = getResponsiveValue<string>(blockData, 'color', breakpoint);
            const width = getResponsiveValue<{ val: string; unit: string }>(blockData, 'width', breakpoint);
            const height = getResponsiveValue<{ val: string; unit: string }>(blockData, 'height', breakpoint);
            const position = getResponsiveValue<string>(blockData, 'position', breakpoint);
            const zIndex = getResponsiveValue<number>(blockData, 'zIndex', breakpoint);

            // Padding properties
            const pt = getResponsiveValue<{ val: string; unit: string }>(blockData, 'pt', breakpoint);
            const pr = getResponsiveValue<{ val: string; unit: string }>(blockData, 'pr', breakpoint);
            const pb = getResponsiveValue<{ val: string; unit: string }>(blockData, 'pb', breakpoint);
            const pl = getResponsiveValue<{ val: string; unit: string }>(blockData, 'pl', breakpoint);

            return {
                ...generateBackground(background),
                ...(color && { color }),
                ...(width?.val && { width: `${width.val}${width.unit || 'px'}` }),
                ...(height?.val && { height: `${height.val}${height.unit || 'px'}` }),
                ...(position && { position: position as CSSProperties['position'] }),
                ...(zIndex && { zIndex }),
                ...(pt?.val && { paddingTop: `${pt.val}${pt.unit || 'px'}` }),
                ...(pr?.val && { paddingRight: `${pr.val}${pr.unit || 'px'}` }),
                ...(pb?.val && { paddingBottom: `${pb.val}${pb.unit || 'px'}` }),
                ...(pl?.val && { paddingLeft: `${pl.val}${pl.unit || 'px'}` }),
            };
        },
        applyTo: 'all',
    },
];
