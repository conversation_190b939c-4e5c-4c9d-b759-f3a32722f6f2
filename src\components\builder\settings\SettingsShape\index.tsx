/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import { Button, DropZone } from '@shopify/polaris';
import { getBlockProperty } from '@/utils/shared';
import { useAppStore } from '@/stores/appStore';
import { apiAddress } from '@/configs/apiAddress';
import { useMutation } from '@/hooks/useMutation';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import './style.scss';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];

interface SettingsShapeProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
}

export const SettingsShape: FC<SettingsShapeProps> = ({ path, blockId }) => {
    const value = getBlockProperty(`configs.${path}.svg`, blockId);
    const type = getBlockProperty(`configs.${path}.type`, blockId);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const setIsOpenModalShape = useAppStore((state) => state.setIsOpenModalShape);
    const mutation = useMutation({
        url: apiAddress.shape,
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        timeout: 50000,
        onMutate: () => {
            shopify.toast.show('Uploading...');
        },
        onSuccess: (response: any) => {
            const newSvg = response?.result?.data?.[0]?.value || '';
            updateBlockConfigsProperty(blockId, `${path}.svg`, newSvg);
            updateBlockConfigsProperty(blockId, `${path}.type`, 'svg');
            shopify.toast.show('Uploaded successfully');
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
    });

    const handleUpload = (files: File[]) => {
        const formData = new FormData();
        formData.append('mediaType', 'svg');
        files.forEach((file) => {
            formData.append('files[]', file);
        });
        mutation.mutate(formData);
    };

    return (
        <div className="settings-shape">
            <div
                css={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '160px',
                    borderRadius: '4px',
                    backgroundColor: '#F1F1F1',
                    cursor: 'pointer',
                }}
            >
                {type === 'image' && <img src={value} alt="image" style={{ width: '70px', height: '70px' }} />}
                {type === 'svg' && (
                    <div
                        style={{
                            ...(type === 'svg' && {
                                maskImage: `url("data:image/svg+xml,${encodeURIComponent(value)}")`,
                                maskSize: '100% 100%',
                                WebkitMaskSize: '100% 100%',
                                WebkitMaskImage: `url("data:image/svg+xml,${encodeURIComponent(value)}")`,
                            }),
                            background: '#8a8a8a',
                            width: '70px',
                            height: '70px',
                        }}
                    />
                )}
            </div>
            <div className="settings-shape__buttons">
                <Button onClick={() => setIsOpenModalShape(true)}>Choose shape</Button>
                <DropZone
                    outline={false}
                    onDrop={(_, acceptedFiles) => {
                        handleUpload(acceptedFiles);
                    }}
                    accept={'.svg'}
                >
                    <Button size="medium" loading={mutation.isLoading}>
                        Update shape
                    </Button>
                </DropZone>
            </div>
        </div>
    );
};
