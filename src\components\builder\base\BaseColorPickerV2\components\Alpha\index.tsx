import { FC, ForwardRefRenderFunction, forwardRef } from 'react';
import { HsvaColor, hsvaToHslaString } from '@uiw/color-convert';
import { Interactive, Interaction } from '../Interactive';
import { Pointer, PointerProps } from './Pointer';

export interface AlphaProps {
    hsva: HsvaColor;
    direction?: 'vertical' | 'horizontal';
    pointer?: FC<PointerProps>;
    onChange?: (newAlpha: { a: number }, offset: Interaction) => void;
    background?: string;
}

const AlphaFR: ForwardRefRenderFunction<HTMLDivElement, AlphaProps> = (
    { hsva, background, direction = 'horizontal', onChange, pointer, ...otherProps },
    ref,
) => {
    const handleChange = (offset: Interaction) => {
        if (onChange) {
            onChange({ ...hsva, a: direction === 'horizontal' ? offset.left : offset.top }, offset);
        }
    };

    const colorTo = hsvaToHslaString(Object.assign({}, hsva, { a: 1 }));

    const innerBackground = `linear-gradient(to ${
        direction === 'horizontal' ? 'right' : 'bottom'
    }, rgba(244, 67, 54, 0) 0%, ${colorTo} 100%)`;

    const args = direction === 'horizontal' ? { left: `${hsva?.a * 100}%` } : { top: `${hsva?.a * 100}%` };

    const pointerElement = pointer && typeof pointer === 'function' ? pointer({ ...args }) : <Pointer {...args} />;

    return (
        <div
            ref={ref}
            css={{
                position: 'relative',
                width: direction === 'horizontal' ? '100%' : '16px',
                height: direction === 'horizontal' ? '16px' : '100%',
                borderRadius: '8px',
                backgroundImage:
                    'conic-gradient(rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 25%, transparent 0deg, transparent 50%, rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 75%, transparent 0deg)',
                backgroundSize: '16px 16px',
            }}
            {...otherProps}
        >
            <div
                css={{
                    position: 'absolute',
                    inset: 0,
                    borderRadius: '8px',
                    background: background || innerBackground,
                }}
            />
            <Interactive
                css={{
                    position: 'absolute',
                    inset: direction === 'horizontal' ? '0 0.25rem' : '0.25rem 0',
                    zIndex: 1,
                }}
                onMove={handleChange}
                onDown={handleChange}
            >
                {pointerElement as React.ReactNode}
            </Interactive>
        </div>
    );
};

export const Alpha = forwardRef(AlphaFR);
