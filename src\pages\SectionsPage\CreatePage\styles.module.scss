.filtersContainer {
    margin-inline: -8px;

    :global(.Polaris-Filters__Container) {
        border-bottom: none;

        :global(.Polaris-InlineStack) {
            gap: 8px;
        }

        :global(.Polaris-TextField__Backdrop) {
            border: none;
            box-shadow: 0px 1px 0px 0px #e3e3e3 inset, 1px 0px 0px 0px #e3e3e3 inset, -1px 0px 0px 0px #e3e3e3 inset,
                0px -1px 0px 0px #b5b5b5 inset;
        }
    }

    :global(.Polaris-Filters__FiltersWrapper) {
        height: auto;
        overflow: visible;
        border-bottom: none;

        :global(.Polaris-Filters-FilterPill__ToggleButton) {
            height: auto;
            min-height: 24px;
            overflow: visible;

            :global(.Polaris-Text--root) {
                text-align: start;
                text-wrap: auto;
            }
        }
    }

    :global(.Polaris-TextField__Input) {
        font-size: var(--p-font-size-325);
        line-height: var(--p-font-line-height-500);
    }
}

.customChoiceList {
    :global(.Polaris-Bleed) {
        position: relative;
        :global(.Polaris-ChoiceList__ChoiceChildren) {
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            background-color: white;
            padding-left: 8px;
        }
    }
}
