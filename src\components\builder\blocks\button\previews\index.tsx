import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { ButtonSettingsConfigs } from '@/components/builder/blocks/button/configs';
import { FC } from 'react';
import { Text } from '@shopify/polaris';

export const ButtonPreview: FC = () => {
    return (
        <div css={{ display: 'flex', flexDirection: 'row', gap: '10px' }} onClick={() => console.log('clicked')}>
            {ButtonSettingsConfigs.map((option) => {
                const type = option.configs?.content?.type;

                if (type === 'text-only') {
                    return (
                        <PreviewWrapper key={option.id} label="Text">
                            <BlockToolbar
                                {...option}
                                configs={option.configs as unknown as Record<string, unknown>}
                                type="button"
                                css={{
                                    width: '100%',
                                    height: '80px',
                                    background: 'white',
                                    borderRadius: '8px',
                                    border: '1px solid #e5e5e5',
                                    cursor: 'move',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    gap: '10px',
                                }}
                            >
                                <div
                                    css={{
                                        width: '73px',
                                        height: '20px',
                                        background: '#8A8A8A',
                                        borderRadius: '100px',
                                        color: 'white',
                                    }}
                                ></div>
                            </BlockToolbar>
                        </PreviewWrapper>
                    );
                }

                if (type === 'text-and-icon') {
                    return (
                        <PreviewWrapper key={option.id} label="Text & Icon">
                            <BlockToolbar
                                {...option}
                                configs={option.configs as unknown as Record<string, unknown>}
                                type="button"
                                css={{
                                    width: '100%',
                                    height: '80px',
                                    background: 'white',
                                    borderRadius: '8px',
                                    border: '1px solid #e5e5e5',
                                    cursor: 'move',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <span
                                    css={{
                                        color: 'white',
                                        fontSize: '12px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <svg
                                        width="28"
                                        height="28"
                                        viewBox="0 0 32 32"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M17.8048 6.59571C17.0803 5.07747 14.9192 5.07747 14.1947 6.59571L11.9714 11.2549L6.85324 11.9296C5.18543 12.1495 4.5176 14.2048 5.73767 15.363L9.48182 18.9172L8.54186 23.9934C8.23557 25.6475 9.98395 26.9178 11.4625 26.1153L15.9997 23.6528L20.537 26.1153C22.0155 26.9178 23.7639 25.6475 23.4576 23.9934L22.5177 18.9172L26.2618 15.363C27.4819 14.2048 26.8141 12.1495 25.1463 11.9296L20.028 11.2549L17.8048 6.59571Z"
                                            fill="#4A4A4A"
                                        />
                                    </svg>
                                </span>
                                <div
                                    css={{
                                        width: '73px',
                                        height: '20px',
                                        background: '#8A8A8A',
                                        borderRadius: '100px',
                                        color: 'white',
                                    }}
                                ></div>
                            </BlockToolbar>
                        </PreviewWrapper>
                    );
                }

                return null;
            })}
        </div>
    );
};

const PreviewWrapper: FC<{ label: string; children: React.ReactNode }> = ({ label, children }) => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', alignItems: 'center', flex: 1 }}>
        {children}
        <Text variant="headingSm" as="h2">
            {label}
        </Text>
    </div>
);
