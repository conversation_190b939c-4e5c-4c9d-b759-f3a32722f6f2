/* eslint-disable @typescript-eslint/no-explicit-any */
export const AVAILABLE_MODES = ['mobile', 'desktop'] as const;
export type DeviceMode = (typeof AVAILABLE_MODES)[number];

export const createCssVarName = (prefix: string, mode: DeviceMode): string => {
    const capitalizedMode = mode.charAt(0).toUpperCase() + mode.slice(1);
    return `--${prefix}${capitalizedMode}`;
};

export const createAttrName = (prefix: string, mode: DeviceMode): string => {
    const capitalizedMode = mode.charAt(0).toUpperCase() + mode.slice(1);
    return `${prefix}${capitalizedMode}`;
};

export const createAllCssVars = (prefix: string): Record<DeviceMode, string> => {
    return AVAILABLE_MODES.reduce((acc, mode) => {
        acc[mode] = createCssVarName(prefix, mode);
        return acc;
    }, {} as Record<DeviceMode, string>);
};

export const createModeAttributes = (prefix: string, cssVars: Record<DeviceMode, string>): Record<string, any> => {
    return AVAILABLE_MODES.reduce((acc, mode) => {
        const attrName = createAttrName(prefix, mode);
        const cssVar = cssVars[mode];

        acc[attrName] = {
            default: null,
            parseHTML: (element: HTMLElement) => element.style.getPropertyValue(cssVar).replace(/["']+/g, ''),
            renderHTML: (attributes: Record<string, any>) => {
                if (!attributes[attrName]) {
                    return {};
                }

                if (prefix === 'fontSize') {
                    const match = attributes[attrName].match(/\d+/);
                    const lineHeight = match ? parseInt(match[0], 10) : null;

                    const lineHeightAttr = createCssVarName('lineHeight', mode);

                    return {
                        style: `${cssVar}: ${attributes[attrName]};
                         ${lineHeightAttr}: ${lineHeight && lineHeight * 0.8}px
                        `,
                    };
                }

                return {
                    style: `${cssVar}: ${attributes[attrName]}`,
                };
            },
        };

        return acc;
    }, {} as Record<string, any>);
};

export const getModeAttrName = (prefix: string, mode: DeviceMode): string => {
    return createAttrName(prefix, mode);
};

export const createModeAttrValue = (prefix: string, mode: DeviceMode, value: string): Record<string, string> => {
    const attrName = getModeAttrName(prefix, mode);
    return { [attrName]: value };
};

export const createResetAttrs = (prefix: string, mode?: DeviceMode): Record<string, null> => {
    if (!mode) {
        return AVAILABLE_MODES.reduce((acc, m) => {
            acc[getModeAttrName(prefix, m)] = null;
            return acc;
        }, {} as Record<string, null>);
    }

    return { [getModeAttrName(prefix, mode)]: null };
};
