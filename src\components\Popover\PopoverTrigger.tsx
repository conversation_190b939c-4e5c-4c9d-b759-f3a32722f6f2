import * as React from 'react';
import { useMergeRefs } from '@floating-ui/react';
import { usePopoverContext } from './context';

interface PopoverTriggerProps {
    children: React.ReactNode;
    asChild?: boolean;
}

export const PopoverTrigger = React.forwardRef<HTMLElement, React.HTMLProps<HTMLElement> & PopoverTriggerProps>(
    function PopoverTrigger({ children, asChild = false, ...props }, propRef) {
        const context = usePopoverContext();
        const ref = useMergeRefs([context.refs.setReference, propRef]);

        if (asChild && React.isValidElement(children)) {
            return React.cloneElement(
                children,
                context.getReferenceProps({
                    ref,
                    ...props,
                    ...(children.props as React.HTMLAttributes<HTMLElement>),
                }),
            );
        }

        return (
            <button ref={ref} type="button" {...context.getReferenceProps(props)}>
                {children}
            </button>
        );
    },
);
