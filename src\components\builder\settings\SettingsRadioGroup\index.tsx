import { FC } from 'react';
import { TextProps } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '../../base/BaseItemLayout';
import { BaseRadioGroup } from '../../base/BaseRadioGroup';
import { RadioDataProps } from '../../base/BaseRadioGroup/types';

interface SettingsRadioGroupProps {
    data: RadioDataProps[];
    textProps?: Omit<Partial<TextProps>, 'children'>;
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
    label?: string;
}

export const SettingsRadioGroup: FC<SettingsRadioGroupProps> = ({
    data,
    textProps,
    blockId,
    path,
    isUpdateConfigs,
    label,
    ...otherProps
}) => {
    const defaultTextProps = { as: 'p', variant: 'bodyMd' };
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const onChangeRadio = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}`, value);
        }
    };

    const modelValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    return (
        <BaseItemLayout
            textProps={{ ...defaultTextProps, ...textProps, children: label || 'Default Accordion' } as TextProps}
            direction="column"
            containerClassName="settings-grid-gap-200"
            {...otherProps}
        >
            <BaseRadioGroup data={data} onChangeRadio={onChangeRadio} activeRadio={modelValue} />
        </BaseItemLayout>
    );
};
