import React, { FC } from 'react';
import { Box, Text, DropZone, BlockStack, Link, Card, IndexTable } from '@shopify/polaris';
import { FileContentProps } from './types';

export const FileContent: FC<FileContentProps> = React.memo(
    ({
        files,
        hasFiles,
        fileCountText,
        totalPages,
        currentPage,
        rowMarkup,
        handleDropZoneDrop,
        handleNextPage,
        handlePreviousPage,
    }) => {
        return (
            <Box padding="400">
                <BlockStack gap="300">
                    <Text as="p" variant={hasFiles ? 'headingMd' : undefined}>
                        {hasFiles ? fileCountText : 'Add files'}
                    </Text>
                    {!hasFiles ? (
                        <DropZone onDrop={handleDropZoneDrop} allowMultiple={false} accept={'.atk'}>
                            <DropZone.FileUpload actionHint="Accepts .atk only" />
                        </DropZone>
                    ) : (
                        <Card padding="0">
                            <IndexTable
                                headings={[{ title: 'Title' }, { title: 'Page Type' }]}
                                itemCount={files.length}
                                selectable={false}
                                pagination={{
                                    hasNext: currentPage < totalPages,
                                    hasPrevious: currentPage > 1,
                                    label: `${currentPage} / ${totalPages}`,
                                    onNext: handleNextPage,
                                    onPrevious: handlePreviousPage,
                                }}
                            >
                                {rowMarkup}
                            </IndexTable>
                        </Card>
                    )}
                    <Box paddingBlockStart="200">
                        <Text as="p">
                            {hasFiles ? (
                                'Please review your importing pages.'
                            ) : (
                                <>
                                    Learn more about <Link url="#">Export & Import</Link> or contact us for{' '}
                                    <Link url="#">Support.</Link>
                                </>
                            )}
                        </Text>
                    </Box>
                </BlockStack>
            </Box>
        );
    },
);
