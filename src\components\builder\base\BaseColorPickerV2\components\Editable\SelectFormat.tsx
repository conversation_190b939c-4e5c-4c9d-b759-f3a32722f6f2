import { FC, useState } from 'react';
import { useClick, useFloating, offset, useInteractions } from '@floating-ui/react';
import { Icon } from '@shopify/polaris';
import { CaretDownIcon } from '@shopify/polaris-icons';

interface SelectFormatProps {
    format: 'hex' | 'rgba';
    onChange: (format: 'hex' | 'rgba') => void;
}

export const SelectFormat: FC<SelectFormatProps> = ({ format, onChange }) => {
    const [isOpen, setIsOpen] = useState(false);

    const { refs, floatingStyles, context } = useFloating({
        open: isOpen,
        onOpenChange: setIsOpen,
        placement: 'bottom-start',
        middleware: [offset({ mainAxis: 2 })],
    });
    const click = useClick(context);
    const { getReferenceProps, getFloatingProps } = useInteractions([click]);

    const handleFormatChange = (format: 'hex' | 'rgba') => {
        return () => {
            onChange(format);
            setIsOpen(false);
        };
    };
    return (
        <div>
            <div
                ref={refs.setReference}
                css={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingInlineStart: '8px',
                    paddingInlineEnd: '4px',
                    width: '64px',
                    height: '32px',
                    borderRadius: '8px',
                    border: '1px solid #ebebeb',
                    cursor: 'pointer',
                    '& .Polaris-Icon': {
                        transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'all 0.25s ease',
                    },
                }}
                {...getReferenceProps()}
            >
                <span>{format === 'hex' ? 'Hex' : 'RGB'}</span>
                <Icon source={CaretDownIcon} />
            </div>
            {isOpen && (
                <div
                    ref={refs.setFloating}
                    style={floatingStyles}
                    css={{
                        minWidth: '64px',
                        background: 'white',
                        borderRadius: '4px',
                        border: '1px solid #ebebeb',
                        boxShadow: '0px 20px 32px 0px rgba(96, 97, 112, 0.24), 0px 2px 8px 0px rgba(40, 41, 61, 0.08)',
                        overflow: 'hidden',
                        zIndex: 1,
                    }}
                    {...getFloatingProps()}
                >
                    <div
                        css={{
                            padding: '4px 8px',
                            cursor: 'pointer',
                            '&:hover': { background: '#f5f5f5' },
                        }}
                        onClick={handleFormatChange('hex')}
                    >
                        Hex
                    </div>
                    <div
                        css={{
                            padding: '4px 8px',
                            cursor: 'pointer',
                            '&:hover': { background: '#f5f5f5' },
                        }}
                        onClick={handleFormatChange('rgba')}
                    >
                        RGB
                    </div>
                </div>
            )}
        </div>
    );
};
