import { FC } from 'react';
import { BlockStack, Box, BoxProps } from '@shopify/polaris';
import { SettingsSelect, SettingsInput } from '@/components/builder/settings';
import './styles.scss';
import { getBlockProperty } from '@/utils/shared';
interface SettingsFormSubmitProps {
    blockId: string;
    boxProps?: BoxProps;
    isUpdateConfigs: boolean;
    path: string;
}

export const SettingsFormSubmit: FC<SettingsFormSubmitProps> = ({ blockId, isUpdateConfigs, boxProps, path }) => {
    const submitAction = getBlockProperty('configs.submit.action', blockId);

    return (
        <Box {...boxProps} paddingBlockStart="300">
            <BlockStack gap="400">
                <SettingsSelect
                    path={`${path}.action`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    options={[
                        {
                            id: 'stay-on-page',
                            content: 'Stay on page',
                        },
                        {
                            id: 'open-link',
                            content: 'Open link',
                        },
                    ]}
                    label="Event"
                />
                {submitAction === 'open-link' && (
                    <>
                        <SettingsInput
                            path={`${path}.openLink.url`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            label="URL"
                            direction="column"
                            inputProps={{
                                type: 'text',
                                autoComplete: 'off',
                                placeholder: 'Enter URL',
                            }}
                        />
                        <SettingsSelect
                            path={`${path}.openLink.open`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            options={[
                                {
                                    id: 'in-page',
                                    content: 'In page',
                                },
                                {
                                    id: 'new-window',
                                    content: 'New window',
                                },
                            ]}
                            label="Open link"
                        />
                    </>
                )}

                <SettingsInput
                    path={`${path}.successMessage`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Success Message"
                    direction="column"
                    inputProps={{
                        type: 'text',
                        autoComplete: 'off',
                        placeholder: 'Enter success message',
                        multiline: 3,
                    }}
                />
                <SettingsInput
                    path={`${path}.errorMessage`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Error Message"
                    direction="column"
                    inputProps={{
                        type: 'text',
                        autoComplete: 'off',
                        placeholder: 'Enter error message',
                    }}
                />
            </BlockStack>
        </Box>
    );
};
