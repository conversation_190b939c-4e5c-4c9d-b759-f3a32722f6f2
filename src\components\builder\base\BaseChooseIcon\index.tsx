import { ComponentType, FC, useCallback } from 'react';
import { Icon, IconSource, InlineStack, Tooltip } from '@shopify/polaris';
import clsx from 'clsx';
import './style.scss';

export interface OptionProps {
    id: string;
    icon?: ComponentType | IconSource;
    contentTooltip: string;
    isPolarisIcon?: boolean;
}

export interface BaseChooseIconProps {
    options: OptionProps[];
    valueData: string;
    onChange: (value: string) => void;
    containerClassName?: string;
    label?: string;
    fullWidth?: boolean;
}

export const BaseChooseIcon: FC<BaseChooseIconProps> = ({
    options,
    valueData,
    onChange,
    containerClassName,
    label,
    fullWidth,
}) => {
    const handleChange = useCallback(
        (value: string) => {
            onChange(value);
        },
        [onChange],
    );

    return (
        <InlineStack wrap={false} blockAlign="center" gap="150">
            <div className={clsx('choose-icon__container', containerClassName)}>
                <ul
                    className={clsx('choose-icon__container--list', {
                        'choose-icon__container--full-width': fullWidth,
                    })}
                    aria-label={label}
                >
                    {options.map(({ id, contentTooltip, icon: IconComponent, isPolarisIcon }) => {
                        const isActive = valueData === id;
                        return (
                            <li key={id}>
                                <Tooltip key={id} content={contentTooltip} dismissOnMouseOut activatorWrapper="div">
                                    <button
                                        type="button"
                                        className={`choose-icon__container--list--button${isActive ? ' active' : ''}`}
                                        onClick={() => handleChange(id)}
                                    >
                                        {isPolarisIcon ? (
                                            <Icon source={IconComponent as IconSource} tone="base" />
                                        ) : IconComponent ? (
                                            <IconComponent />
                                        ) : (
                                            contentTooltip
                                        )}
                                    </button>
                                </Tooltip>
                            </li>
                        );
                    })}
                </ul>
            </div>
        </InlineStack>
    );
};
