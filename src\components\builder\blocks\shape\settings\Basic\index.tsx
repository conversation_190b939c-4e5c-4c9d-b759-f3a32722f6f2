import { FC } from 'react';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingElementID } from '@/components/builder/settings/SettingElementID';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { SettingsShape } from '@/components/builder/settings/SettingsShape';
import { ShapeManage } from './ShapeManage';

const DEFAULT_PATH_CONFIGS_CONTENT = 'content';

export const Basic: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);
    return (
        <>
            <BaseHasBorderLayout>
                <SettingElementID value={selectedBlockId || ''} />
                <SettingsShape path="content" blockId={selectedBlockId || ''} />
                {/* <CustomShape label="Custom shape" path={DEFAULT_PATH_CONFIGS_CONTENT} blockId={selectedBlockId || ''} /> */}
            </BaseHasBorderLayout>
            <ShapeManage blockId={selectedBlockId || ''} path={DEFAULT_PATH_CONFIGS_CONTENT} isUpdateConfigs />
        </>
    );
};
