@use './_variable.scss';
@use './_block-form.scss';
@import 'animate.css';

.bg-fill-secondary {
    background-color: var(--p-color-bg-fill-secondary);
}

@media (max-width: 768px) {
    :root {
        --left-sidebar-width: 40px;
        --more-sidebar-width: 200px;
        --header-height: 48px;
        --header-gap: 16px;
        --header-padding: 12px 8px;
    }
}

.builder-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    &__header {
        height: var(--header-height);
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        gap: var(--header-gap);
        flex-direction: row;
        justify-content: space-between;
        padding: var(--header-padding);

        #desktop-button,
        #mobile-button {
            width: 28px;
            height: 28px;
            margin: 0px;
            &:focus-visible {
                outline: 2px solid var(--p-color-focus);
                outline-offset: 2px;
            }
        }
        &-title {
            display: flex;
            align-items: center;
            flex-direction: row;
            gap: 4px;
            padding: var(--p-space-050) var(--p-space-200);
            border-radius: var(--p-border-radius-200);
            justify-content: flex-start;
            flex-wrap: nowrap;
            overflow: hidden;
            height: var(--title-height);
            background-color: var(--p-color-bg-fill-secondary);
        }
    }
    &__content {
        flex: 1 1 0%;
        position: relative;
        &__editor-layout {
            height: 100%;
            display: grid;
            grid-template-columns: var(--left-sidebar-width) auto;
        }
        &__sidebar-drawer {
            position: relative;
            z-index: 10;
            height: 100vh;
            width: var(--more-sidebar-width);
            display: none;
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
            pointer-events: none;
            & > div {
                height: 100%;
            }
            &--active {
                display: block;
                transform: translateX(0);
                pointer-events: auto;
            }
            &__content {
                height: 100%;
                display: flex;
                flex-direction: column;
                &__header {
                    width: 100%;
                    padding: var(--sidebar-header-padding);
                    height: var(--sidebar-header-height);
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid var(--p-color-border);
                }
                &__body {
                    flex: 1;
                    position: relative;
                    width: 100%;
                    &__elements {
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        .element-tab {
                            background: transparent;
                            border: none;
                            outline: none;
                            flex: 1;
                            padding: var(--p-space-100) var(--p-space-200);
                            cursor: pointer;
                            border-radius: var(--p-border-radius-200);
                            min-width: var(--element-tab-min-width);
                            &__container {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                gap: var(--p-space-200);
                                width: 100%;
                                &.hide {
                                    display: none;
                                }
                            }
                            &:hover,
                            &.active {
                                @extend .bg-fill-secondary;
                            }
                            &:focus-visible {
                                outline: 2px solid var(--p-color-focus);
                                outline-offset: 2px;
                            }
                        }
                        .elements-scrollable {
                            overflow-y: auto;
                            flex: 1 1 0%;
                            position: relative;
                            scrollbar-width: none;
                            -ms-overflow-style: none;
                            &::-webkit-scrollbar {
                                display: none;
                            }
                            &__category {
                                cursor: pointer;
                            }
                            .elements-scrollable__container {
                                padding: var(--p-space-400) var(--p-space-200);
                                box-sizing: border-box;
                            }
                            .elements-scrollable__items {
                                display: flex;
                                flex-direction: column;
                                gap: var(--p-space-100);
                                &__item {
                                    border-radius: var(--p-border-radius-200);
                                    cursor: pointer;
                                    &:hover,
                                    &.active {
                                        @extend .bg-fill-secondary;
                                    }
                                }
                            }
                        }
                    }
                    &__elements--preview {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        visibility: hidden;
                        opacity: 0;
                        position: absolute;
                        left: var(--more-sidebar-width);
                        z-index: 1;
                        top: var(--preview-offset-top);
                        height: 100%;
                        transform: translateX(-10px);
                        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
                        pointer-events: none;
                        &.visible {
                            visibility: visible;
                            opacity: 1;
                            transform: translateX(0);
                            pointer-events: auto;
                        }
                    }
                }
            }
        }
        &__main-content {
            flex: 1 1 0%;
            width: 100%;
            max-height: calc(100vh - var(--header-height));
            background: var(--p-color-bg);
            overflow: hidden;
            padding: var(--main-content-padding);
            position: relative;
            .auto-editor {
                max-width: var(--main-content-width);
                width: 100%;
                margin: 0 auto;
                overflow-y: auto;
                overflow-x: hidden;
                scrollbar-width: none;
                -ms-overflow-style: none;
                &::-webkit-scrollbar {
                    display: none;
                }
            }
        }
    }
}

.Polaris-Labelled__Error {
    margin-bottom: 4px;
    overflow: hidden;
    max-width: 222px;
}

form [data-atk-form-input-read-only='true'] {
    background: var(--p-color-bg-surface-disabled);
    border-color: var(--p-color-border-tertiary);
    opacity: 0.8;
}
