.base-select {
    width: 140px;
    &-container {
        width: 100%;
    }

    &-dropdown {
        width: 140px;
    }
}

#base-select-activator {
    box-shadow: none;
    border: 1px solid var(--p-color-input-border);
    min-height: 32px;
    padding: 5px 12px;
    &:hover {
        background-color: transparent;
    }

    &.Polaris-Button--pressed {
        box-shadow: var(--pc-button-box-shadow_pressed);
        border-color: transparent;
    }

    .Polaris-Text--medium {
        font-weight: 450;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
}

.Polaris-ActionList__Item.Polaris-ActionList--active {
    background-color: transparent;

    .Polaris-Text--semibold {
        font-weight: 450;
    }

    svg {
        fill: #005bd3;
    }
}
