.base-modal-shape {
    position: absolute;
    inset: 0px;
    z-index: 1;
    border-radius: var(--atk-radii-base);
    border: none;
    cursor: pointer;
    background: #ffffff;

    &.margin-top,
    &.padding-top {
        clip-path: polygon(0px 0px, 100% 0px, 50% 50%);
    }
    &.margin-right,
    &.padding-right {
        clip-path: polygon(100% 0px, 50% 50%, 100% 100%);
    }
    &.margin-bottom,
    &.padding-bottom {
        clip-path: polygon(0px 100%, 50% 50%, 100% 100%);
    }
    &.margin-left,
    &.padding-left {
        clip-path: polygon(0px 0px, 50% 50%, 0% 100%);
    }

    &:hover {
        background: #ebebeb;
    }

    &-text {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        z-index: 5;
        width: 3rem;
        overflow: hidden;
        font-weight: 400;
        font-style: normal;
        color: #303030;
        font-size: 12px;
        text-transform: capitalize;

        &.margin-top,
        &.padding-top {
            transform: translateX(-50%);
            top: 0.625rem;
            left: 50%;
        }
        &.margin-right,
        &.padding-right {
            transform: translateY(-50%);
            top: 50%;
            right: 0.5rem;
        }
        &.margin-bottom,
        &.padding-bottom {
            transform: translateX(-50%);
            bottom: 0.625rem;
            left: 50%;
        }
        &.margin-left,
        &.padding-left {
            transform: translateY(-50%);
            top: 50%;
            left: 0.5rem;
        }
    }
}
.base-popover {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 18.25rem;
    padding: 1rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0px 20px 32px 0px #6061703d, 0px 2px 8px 0px #28293d14;
    z-index: 100000;

    &-arrow {
        display: flex;
        color: #fff;
        svg {
            fill: currentColor;
        }
    }

    &-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    &-edit {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 0.25rem;
        background: #f3f4f6;
        border: none;
        cursor: pointer;

        &:hover {
            background: #e5e7eb;
        }
    }

    &-suggestions {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}
