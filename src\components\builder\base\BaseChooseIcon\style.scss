.choose-icon__container {
    position: relative;
    flex-grow: 1;
    max-width: 100%;
    &--list {
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 140px;
        border-radius: var(--p-border-radius-200);
        padding: var(--p-space-050) 0;
        gap: var(--p-space-300);
        margin: 0;
        list-style: none;
        &--button {
            border: none;
            background: transparent;
            outline: none;
            cursor: pointer;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background-color: #f1f1f1;
            border-radius: var(--p-border-radius-200);
            &.active {
                box-shadow: 0 0 0 2px #005bd3 inset;
            }
            &:hover {
                background-color: #ebeaea;
            }
            svg {
                width: 2rem;
                height: 2rem;
            }
        }
    }

    &--full-width {
        max-width: 100%;
        li {
            flex: 1;
        }
        .switch-tabs__container--list--button {
            width: 100%;
        }
    }
}
