/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import {
    DATA_SET_FORM_CHECKBOX_ELEMENT,
    DATA_SET_FORM_CHECKBOX_MULTI_ELEMENT,
    DATA_SET_FORM_CHECKBOX_ELEMENT_WRAP,
    DATA_SET_FORM_CHECKBOX_ELEMENT_INNER,
    DATA_SET_FORM_CHECKBOX_LABEL,
    DATA_SET_FORM_FIELD_ID,
} from '@/components/builder/blocks/form/constants';

interface FormCheckboxProps {
    field: any;
    index: number;
    autoId: string;
}

export const FormCheckbox: FC<FormCheckboxProps> = ({ field, index, autoId }) => (
    <div
        {...{ [`${DATA_SET_FORM_CHECKBOX_MULTI_ELEMENT}-${index}`]: autoId }}
        {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
    >
        {field.options?.map((option: any, optionIndex: number) => (
            <div key={`${field.key}-checkbox-${optionIndex}`} {...{ [`${DATA_SET_FORM_CHECKBOX_ELEMENT}`]: autoId }}>
                <div {...{ [`${DATA_SET_FORM_CHECKBOX_ELEMENT_WRAP}`]: autoId }}></div>
                <div {...{ [`${DATA_SET_FORM_CHECKBOX_ELEMENT_INNER}`]: autoId }}>
                    <input type="checkbox" defaultChecked={option.defaultChecked} />
                    <label {...{ [`${DATA_SET_FORM_CHECKBOX_LABEL}`]: autoId }}>{option.label}</label>
                </div>
            </div>
        ))}
    </div>
);
