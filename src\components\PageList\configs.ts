import type { SortButtonChoice } from '@shopify/polaris';
import { ToastOptionsProps } from './types';

export const resourceName = {
    singular: 'page',
    plural: 'pages',
};

export const sortOptions: SortButtonChoice[] = [
    {
        label: 'Page title',
        value: 'name asc',
        directionLabel: 'A-Z',
    },
    {
        label: 'Page title',
        value: 'name desc',
        directionLabel: 'Z-A',
    },
    {
        label: 'Date Modified',
        value: 'updatedAt asc',
        directionLabel: 'Oldest first',
    },
    {
        label: 'Date Modified',
        value: 'updatedAt desc',
        directionLabel: 'Newest first',
    },
    {
        label: 'Date Published',
        value: 'createdAt asc',
        directionLabel: 'Oldest first',
    },
    {
        label: 'Date Published',
        value: 'createdAt desc',
        directionLabel: 'Newest first',
    },
];

export const getSortParams = (sortValue: string) => {
    const [field, direction] = sortValue.split(' ');

    switch (field) {
        case 'name':
            return {
                sortName: 'title',
                order: direction,
            };
        case 'updatedAt':
            return {
                sortName: 'dateModified',
                order: direction,
            };
        case 'createdAt':
            return {
                sortName: 'datePublished',
                order: direction,
            };
        default:
            return {};
    }
};

export const pageTypeChoices = [
    { label: 'Home page', value: 'home_page' },
    { label: 'Product page', value: 'product_page' },
    { label: 'Collection page', value: 'collection_page' },
    { label: 'Collection list page', value: 'collection_list_page' },
    { label: 'Blog page', value: 'blog_page' },
    { label: 'Blog post page', value: 'blog_post_page' },
    { label: 'Search page', value: 'search_page' },
    { label: '404', value: '404' },
    { label: 'Contact page', value: 'contact_page' },
    { label: 'FQAs page', value: 'FQAs_page' },
    { label: 'About Page', value: 'about_page' },
];

export const pageStatusChoices = [
    { label: 'Published', value: 'published' },
    { label: 'Draft', value: 'draft' },
];

export type SkeletonCountState = {
    [tabIndex: number]: {
        [page: number]: number;
    };
};

export function disambiguateLabel(
    key: string,
    value: string | string[],
    data?: { typeName?: string; name?: string; id: number }[],
): string {
    const getValueString = (val: string | string[]) => (Array.isArray(val) ? val.join(', ') : val);

    switch (key) {
        case 'status':
            return `Status is ${getValueString(value)}`;
        case 'pageType': {
            const pageTypes = Array.isArray(value) ? value : [value];
            const typeNames = pageTypes
                .map((v) => data?.find((item) => item.id === Number(v))?.typeName)
                .filter(Boolean);
            return `Type is ${typeNames}`;
        }
        default:
            return getValueString(value);
    }
}

export function isEmpty(value: string | string[]) {
    if (Array.isArray(value)) {
        return value.length === 0;
    } else {
        return value === '' || value == null;
    }
}

export const getStatusFromChoices = (choice: string) => {
    const statusMap: Record<string, number> = {
        published: 1,
        draft: 2,
    };
    return statusMap[choice] || undefined;
};

export const updateSkeletonCount = (
    count: number,
    tabSelected: number,
    page: number,
    setSkeletonCount: React.Dispatch<React.SetStateAction<SkeletonCountState>>,
) => {
    setSkeletonCount((prev: SkeletonCountState) => ({
        ...prev,
        [tabSelected]: {
            ...prev?.[tabSelected],
            [page]: count,
        },
    }));
};

export const industryChoices = [
    { label: 'Art & crafts', value: 'art_crafts' },
    { label: 'Book, music & videos', value: 'book_music_videos' },
    { label: 'Clothing', value: 'clothing' },
    { label: 'Electronics', value: 'electronics' },
    { label: 'Food & drink', value: 'food_drink' },
    { label: 'Hardware & automotive', value: 'hardware_automotive' },
    { label: 'Health & beauty', value: 'health_beauty' },
    { label: 'Jewelry & acccessories', value: 'jewelry_accessories' },
    { label: 'Pet supplies', value: 'pet_supplies' },
    { label: 'Service', value: 'service' },
    { label: 'Sports & recreation', value: 'sports_recreation' },
    { label: 'Baby & kids', value: 'baby_kids' },
    { label: 'Outdoors & gardening', value: 'outdoors_gardening' },
    { label: 'Other', value: 'other' },
];

export const showToast = (message: string, isError = false, options: ToastOptionsProps = {}) => {
    shopify.toast.show(message, {
        duration: 1500,
        isError,
        ...options,
    });
};

export const handleApiCall = async <T>(
    apiCall: () => Promise<T>,
    successMessage: string,
    errorMessage: string,
    onSuccess?: (result: T) => void,
    setModalState: (state: { open: boolean; kind: string }) => void = () => {},
    setSelectedItems?: React.Dispatch<React.SetStateAction<string[]>>,
) => {
    try {
        const result = await apiCall();
        setModalState({ open: false, kind: 'delete' });
        setSelectedItems?.([]);
        showToast(successMessage);
        onSuccess?.(result);
        return true;
    } catch (error) {
        console.error(`${errorMessage}:`, error);
        showToast(errorMessage, true);
        return false;
    }
};
