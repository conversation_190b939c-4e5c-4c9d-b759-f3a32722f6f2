import { useState, useCallback } from 'react';
import { type RangeSliderValue } from '@shopify/polaris/build/ts/src/components/RangeSlider/types';
import { restrictValueInput } from '@/utils/restrictValueInput';
import { updateCssVariable, removeCssVariable } from '@/utils/cssVariableUtils';

interface UseSliderControlProps {
    min: number;
    max: number;
    initialValue: string;
    cssVariable: string;
    selectedBlockTarget: HTMLElement;
    sliderUnit?: string;
    onValueChange: (value: string) => void;
}

export const useSliderControl = ({
    min,
    max,
    initialValue,
    cssVariable,
    selectedBlockTarget,
    sliderUnit,
    onValueChange,
}: UseSliderControlProps) => {
    const [localValue, setLocalValue] = useState(initialValue);

    const handleSliderChange = useCallback(
        (value: RangeSliderValue) => {
            setLocalValue(value.toString());
            updateCssVariable(selectedBlockTarget, cssVariable, value.toString(), sliderUnit);
        },
        [selectedBlockTarget, cssVariable, sliderUnit],
    );

    const handleInputChange = useCallback((value: string) => {
        setLocalValue(value);
    }, []);

    const handleMouseUp = useCallback(
        (e: React.MouseEvent<HTMLDivElement>) => {
            const value = localValue.toString() === '' ? '0' : localValue.toString();
            onValueChange(value);
            e.currentTarget.blur();
            removeCssVariable(selectedBlockTarget, cssVariable);
        },
        [localValue, onValueChange, selectedBlockTarget, cssVariable],
    );

    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, min, max);
                setLocalValue(finalValue);
                onValueChange(finalValue);
            }

            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, min, max);
                updateCssVariable(selectedBlockTarget, cssVariable, finalValue, sliderUnit);
            }
        },
        [min, max, onValueChange, selectedBlockTarget, cssVariable, sliderUnit],
    );

    const handleKeyUp = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, min, max);
                setLocalValue(finalValue);
                onValueChange(finalValue);
                removeCssVariable(selectedBlockTarget, cssVariable);
            }
        },
        [min, max, onValueChange, selectedBlockTarget, cssVariable],
    );

    const handleBlur = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            const finalValue = restrictValueInput(e.currentTarget.value, min, max);
            setLocalValue(finalValue);
            onValueChange(finalValue);
        },
        [min, max, onValueChange],
    );

    return {
        localValue,
        setLocalValue,
        handleSliderChange,
        handleInputChange,
        handleMouseUp,
        handleKeyDown,
        handleKeyUp,
        handleBlur,
    };
};
