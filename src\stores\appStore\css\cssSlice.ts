import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { StateCreator } from 'zustand';
import { AppActions, AppState } from '../types';
import { cssAction, cssState } from './types';
import { getCssGeneratorByBlockType, generateUniqueCss } from '@/utils/cssBlockGenerator';

// Cache CSS cho mỗi block theo ID
const blockCssCache = new Map<string, { desktop: string; mobile: string; hash: string }>();

export const createCssSlice: StateCreator<AppState & AppActions, [], [], cssState & cssAction> = (set, get) => ({
    cssDesktop: '',
    cssMobile: '',

    setCss: (css: string, device: 'desktop' | 'mobile') => {
        set((state) =>
            device === 'desktop'
                ? { ...state, cssDesktop: state.cssDesktop + css }
                : { ...state, cssMobile: state.cssMobile + css },
        );
    },

    generateCssFromBlocks: async (blocks: Record<string, Auto_BlockData>) => {
        for (const cachedBlockId of blockCssCache.keys()) {
            if (!blocks[cachedBlockId]) {
                blockCssCache.delete(cachedBlockId);
            }
        }

        let desktopCssResult = '';
        let mobileCssResult = '';

        for (const [blockId, blockData] of Object.entries(blocks)) {
            const blockConfigHash = JSON.stringify({
                type: blockData.type,
                bpConfigs: blockData.bpConfigs,
                configs: blockData.configs,
            });

            const cachedBlockCss = blockCssCache.get(blockId);
            if (cachedBlockCss?.hash === blockConfigHash) {
                desktopCssResult += cachedBlockCss.desktop;
                mobileCssResult += cachedBlockCss.mobile;
                continue;
            }

            const cssGenerator = await getCssGeneratorByBlockType(blockData.type);
            if (!cssGenerator) continue;

            const blockDesktopCss = await generateUniqueCss(blockId, blockData, 'desktop');
            let blockMobileCss = await generateUniqueCss(blockId, blockData, 'mobile');

            blockMobileCss = optimizeMobileCss(blockDesktopCss, blockMobileCss);

            blockCssCache.set(blockId, {
                desktop: blockDesktopCss,
                mobile: blockMobileCss,
                hash: blockConfigHash,
            });

            desktopCssResult += blockDesktopCss;
            mobileCssResult += blockMobileCss;
        }

        set(() => ({
            cssDesktop: desktopCssResult,
            cssMobile: mobileCssResult,
        }));
    },

    getCombinedCss: () => {
        const { cssDesktop, cssMobile } = get();

        return cssMobile
            ? `${cssDesktop}
                @media only screen and (max-width: 767px) {
                    ${cssMobile}
                }`
            : cssDesktop;
    },
});

/**
 * Optimizes mobile CSS by removing rules that are identical to desktop CSS
 * @param desktopCss The desktop CSS string
 * @param mobileCss The mobile CSS string
 * @returns Optimized mobile CSS with only the different rules
 */
function optimizeMobileCss(desktopCss: string, mobileCss: string): string {
    if (!mobileCss) return '';

    // Parse CSS blocks for comparison
    const desktopRules = parseCssRules(desktopCss);
    const mobileRules = parseCssRules(mobileCss);

    // Filter out mobile rules that are identical to desktop
    const optimizedMobileRules = new Map<string, Map<string, string>>();

    for (const [selector, mobileProperties] of mobileRules.entries()) {
        const desktopProperties = desktopRules.get(selector);

        // If selector doesn't exist in desktop, keep all mobile properties
        if (!desktopProperties) {
            optimizedMobileRules.set(selector, mobileProperties);
            continue;
        }

        // Filter out properties that are the same in desktop and mobile
        const differentProperties = new Map<string, string>();
        for (const [property, value] of mobileProperties.entries()) {
            const desktopValue = desktopProperties.get(property);
            if (desktopValue !== value) {
                differentProperties.set(property, value);
            }
        }

        // Only add the selector if it has different properties
        if (differentProperties.size > 0) {
            optimizedMobileRules.set(selector, differentProperties);
        }
    }

    // Convert back to CSS string
    return serializeCssRules(optimizedMobileRules);
}

/**
 * Parses CSS string into a map of selectors and their properties
 * @param css CSS string to parse
 * @returns Map of selectors to their properties
 */
function parseCssRules(css: string): Map<string, Map<string, string>> {
    const rules = new Map<string, Map<string, string>>();

    // Split the CSS into rule blocks
    const ruleBlocks = css.split('}').filter((block) => block.trim());

    for (const block of ruleBlocks) {
        const [selectorPart, propertiesPart] = block.split('{');

        if (!selectorPart || !propertiesPart) continue;

        const selector = selectorPart.trim();
        const properties = new Map<string, string>();

        // Split the properties part into individual property declarations
        const propertyDeclarations = propertiesPart.split(';').filter((decl) => decl.trim());

        for (const declaration of propertyDeclarations) {
            const colonIndex = declaration.indexOf(':');
            if (colonIndex === -1) continue;

            const property = declaration.substring(0, colonIndex).trim();
            const value = declaration.substring(colonIndex + 1).trim();

            if (property && value) {
                properties.set(property, value);
            }
        }

        if (properties.size > 0) {
            rules.set(selector, properties);
        }
    }

    return rules;
}

/**
 * Converts a map of CSS rules back to a CSS string
 * @param rules Map of selectors to their properties
 * @returns CSS string
 */
function serializeCssRules(rules: Map<string, Map<string, string>>): string {
    let css = '';

    for (const [selector, properties] of rules.entries()) {
        if (properties.size === 0) continue;

        css += `${selector} {\n`;

        for (const [property, value] of properties.entries()) {
            css += `    ${property}: ${value};\n`;
        }

        css += '}\n';
    }

    return css;
}
