import { FC } from 'react';
import { Box, InlineStack } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { alignHorizontalOptions, alignVerticalOptions } from '@/components/builder/data/options';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';
interface AlignProps {
    id: string;
}

export const Align: FC<AlignProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Align"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <InlineStack blockAlign="center" align="space-between">
                        <SettingsSwitchTab
                            options={alignHorizontalOptions}
                            blockId={id}
                            path="breadcrumbAlignment.alignSelf"
                            direction="column"
                            isUpdateConfigs={false}
                            switchTabProps={{
                                fullWidth: true,
                            }}
                        />
                        <SettingsSwitchTab
                            options={alignVerticalOptions}
                            blockId={id}
                            path="breadcrumbAlignment.justifySelf"
                            direction="column"
                            isUpdateConfigs={false}
                            switchTabProps={{
                                fullWidth: true,
                            }}
                        />
                    </InlineStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
