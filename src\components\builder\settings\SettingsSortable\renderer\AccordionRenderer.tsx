/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect } from 'react';
import { ActionList, Text } from '@shopify/polaris';
import {
    DragHandleIcon,
    SettingsIcon,
    DuplicateIcon,
    EditIcon,
    DeleteIcon,
    MinusCircleIcon,
} from '@shopify/polaris-icons';
import {
    useFloating,
    flip,
    offset,
    shift,
    useClick,
    useDismiss,
    useInteractions,
    FloatingPortal,
} from '@floating-ui/react';
import { UniqueIdentifier } from '@dnd-kit/core';
import { genRandomBlockId, useBlockStore } from '@giaminhautoketing/auto-builder';
import { useClickOutside, useValidatedInput } from '@/hooks';
import { getBlockProperty } from '@/utils/shared';
import { useAppStore } from '@/stores/appStore';
import { SortableItemRequireProps, RendererProps } from '@/components/Sortable';

export function AccordionRenderer<T extends SortableItemRequireProps>({
    blockId,
    // index,
    clone,
    data,
    isDragging,
    onRemove,
}: // insertPosition,
RendererProps<T> & {
    blockId: string;
}) {
    const inputRef = useRef<HTMLInputElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isEditing, setIsEditing] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const isGhost = !clone && isDragging;

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const handleDone = () => {
        setIsEditing(false);
    };

    const accordion = useAppStore((state) => state.accordion);
    const setAccordion = useAppStore((state) => state.setAccordion);
    const isActive = accordion[blockId].activeId === data.id;

    const handleAction = (action: string) => {
        switch (action) {
            case 'Duplicate':
                onDuplicate(data.id);
                break;
            case 'Collapse':
                onUnCheckCollapse();
                break;
            case 'Rename':
                setIsEditing(true);
                break;
            case 'Delete':
                onRemove?.();
                break;
        }
        setIsOpen(false);
    };

    const blockData = getBlockProperty(`configs.blockData`, blockId);

    const onUnCheckCollapse = () => {
        if (isActive) {
            setAccordion(blockId, '');
        }
    };

    const onDuplicate = (id: UniqueIdentifier) => {
        const itemToDuplicate = blockData.find((item: { id: UniqueIdentifier }) => item.id === id);
        if (!itemToDuplicate) return;

        const duplicatedItem = {
            ...itemToDuplicate,
            id: genRandomBlockId(),
            title: `${itemToDuplicate.title} Copy`,
        };
        const updatedItems = [...blockData, duplicatedItem];
        updateBlockConfigsProperty(blockId, 'blockData', updatedItems as any);
    };

    const onRename = (id: UniqueIdentifier, newTitle: string) => {
        const updatedItems = blockData.map((item: { id: UniqueIdentifier }) =>
            item.id === id ? { ...item, title: newTitle } : item,
        );
        updateBlockConfigsProperty(blockId, 'blockData', updatedItems);
    };

    const {
        value,
        acceptedValue,
        onChange: onInputChange,
        onBlur,
        onKeyDown,
    } = useValidatedInput<string>({
        initialValue: data.title,
        validator: (value, acceptedValue) => {
            return value.trim() !== '' && value.trim() !== acceptedValue;
        },
        onApply: () => {
            onRename(data.id, value);
            handleDone();
        },
    });

    const isDisabled = value.trim() === '' || value.trim() === acceptedValue;

    const { refs, floatingStyles, context } = useFloating({
        open: isOpen,
        onOpenChange: setIsOpen,
        placement: 'right-start',
        middleware: [
            flip(),
            shift(),
            offset({
                mainAxis: 16,
                crossAxis: -6,
            }),
        ],
    });
    const click = useClick(context);
    const dismiss = useDismiss(context);
    const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss]);

    useClickOutside(inputRef, () => setIsEditing(false), [buttonRef]);

    useEffect(() => {
        if (isEditing) {
            inputRef.current?.focus();
            inputRef.current?.select();
        }
    }, [isEditing]);

    return (
        <div className="accordion-sortable-item">
            <div
                css={{
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                    height: '32px',
                    backgroundColor: '#ffffff',
                    borderWidth: '1px',
                    borderColor: isActive ? '#005BD3' : 'rgba(138, 138, 138, 1)',
                    borderStyle: isGhost ? 'dashed' : 'solid',
                    borderRadius: '8px',
                    padding: '0 6px',
                    cursor: clone ? 'grabbing' : 'grab',
                    tabIndex: 0,
                    ':before': {
                        content: '""',
                        position: 'absolute',
                        inset: '-2px',
                        borderRadius: '8px',
                        boxShadow: '0px 0px 0px 2px #005BD3',
                        pointerEvents: 'none',
                        opacity: isEditing ? 1 : 0,
                    },
                    ':hover': {
                        '& .accordion-sortable-item-settings-icon': {
                            opacity: 1,
                        },
                        '& .icon-drag-handle': {
                            opacity: 1,
                        },
                    },
                }}
                onDoubleClick={() => {
                    setIsEditing(!isEditing);
                }}
                onClick={() => {
                    setAccordion(blockId, data.id as string);
                }}
            >
                {isGhost ? null : isEditing ? (
                    <>
                        <input
                            ref={inputRef}
                            type="text"
                            css={{
                                flexGrow: 1,
                                height: '100%',
                                paddingInlineStart: '24px',
                                marginRight: '12px',
                                outline: 'none',
                                border: 'none',
                            }}
                            value={value}
                            onChange={(e) => onInputChange(e.target.value)}
                            onBlur={onBlur}
                            onKeyDown={(e) => onKeyDown(e, () => setIsEditing(false))}
                        />
                        <button
                            ref={buttonRef}
                            css={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '46px',
                                height: '18px',
                                borderRadius: '4px',
                                color: '#ffffff',
                                background: 'rgba(0, 91, 211, 1)',
                                fontSize: '12px',
                                fontWeight: '450',
                                lineHeight: '16px',
                                outline: 'none',
                                border: 'none',
                                opacity: isDisabled ? 0.5 : 1,
                                cursor: 'pointer',
                            }}
                            disabled={isDisabled}
                            onClick={handleDone}
                            onDoubleClick={(e) => e.stopPropagation()}
                        >
                            Done
                        </button>
                    </>
                ) : (
                    <>
                        <div
                            css={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '4px',
                                userSelect: 'none',
                            }}
                        >
                            <div
                                className="icon-drag-handle"
                                css={{ width: '20px', height: '20px', opacity: 0, cursor: 'grab' }}
                            >
                                <DragHandleIcon />
                            </div>
                            <Text as="p" variant="bodyMd">
                                {value}
                            </Text>
                        </div>
                        <div css={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                            <div
                                ref={refs.setReference}
                                {...getReferenceProps({
                                    onClick: (e) => e.stopPropagation(),
                                    onDoubleClick: (e) => e.stopPropagation(),
                                })}
                                className="accordion-sortable-item-settings-icon"
                                css={{ width: '20px', height: '20px', opacity: isOpen ? 1 : 0, cursor: 'pointer' }}
                            >
                                <SettingsIcon />
                            </div>
                            {isOpen && (
                                <FloatingPortal>
                                    <div
                                        ref={refs.setFloating}
                                        style={floatingStyles}
                                        css={{
                                            width: '267px',
                                            background: '#fff',
                                            borderRadius: '12px',
                                            boxShadow:
                                                '0px 4px 6px -2px rgba(26, 26, 26, 0.2),0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset,0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset,-1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset,1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset',
                                            zIndex: 99,
                                        }}
                                        {...getFloatingProps({
                                            onPointerDown: (e) => e.stopPropagation(),
                                            onDoubleClick: (e) => e.stopPropagation(),
                                        })}
                                    >
                                        <div
                                            onClickCapture={(e) => {
                                                const target = e.target as HTMLElement;
                                                const button = target.closest('button');
                                                if (button && button.id) {
                                                    e.stopPropagation();
                                                    handleAction(button.id);
                                                }
                                            }}
                                        >
                                            <ActionList
                                                actionRole="menuitem"
                                                items={[
                                                    {
                                                        content: 'Collapse',
                                                        icon: MinusCircleIcon,
                                                        onAction: () => handleAction('Collapse'),
                                                        id: 'Collapse',
                                                    },
                                                    {
                                                        content: 'Duplicate',
                                                        icon: DuplicateIcon,
                                                        onAction: () => handleAction('Duplicate'),
                                                        id: 'Duplicate',
                                                    },
                                                    {
                                                        content: 'Rename',
                                                        icon: EditIcon,
                                                        onAction: () => handleAction('Rename'),
                                                        id: 'Rename',
                                                    },
                                                    {
                                                        content: 'Delete',
                                                        icon: DeleteIcon,
                                                        onAction: () => handleAction('Delete'),
                                                        id: 'Delete',
                                                    },
                                                ]}
                                            />
                                        </div>
                                    </div>
                                </FloatingPortal>
                            )}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
