
.toggle-title {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: center;
}

.toggle-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.toggle-button {
    width: 130px;
    height: 70px;
    border: 2px solid #ccc;
    border-radius: 10px;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    color: #555;
    transition: all 0.3s ease;
}

.toggle-button.selected {
    background-color: #e8f0fe;
    border-color: #4285f4;
    color: #4285f4;
}

.icon {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 6px;
}

.bar {
    width: 25px;
    height: 6px;
    background-color: #555;
    border-radius: 3px;
}

.dot {
    width: 6px;
    height: 6px;
    background-color: #555;
    border-radius: 50%;
}
