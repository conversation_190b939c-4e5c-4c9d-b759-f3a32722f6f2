import { FC, useState } from 'react';
import { BlockStack, Box, Button, Text } from '@shopify/polaris';
import { CheckIcon, ClipboardIcon } from '@shopify/polaris-icons';

interface ElementIDProps {
    value: string;
}

export const ElementID: FC<ElementIDProps> = ({ value }) => {
    const [isCopied, setIsCopied] = useState(false);
    const handleToClipboard = () => {
        navigator.clipboard.writeText(value);
        setIsCopied(true);
        shopify.toast.show('Copied Element ID to clipboard', {
            duration: 2000,
        });
        setTimeout(() => {
            setIsCopied(false);
        }, 2000);
    };
    return (
        <Box
            background="bg-fill-secondary"
            paddingBlock="150"
            paddingInlineStart="200"
            paddingInlineEnd="100"
            borderRadius="200"
            width="130px"
        >
            <BlockStack>
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '39px',
                        justifyContent: 'space-between',
                    }}
                >
                    <Box width="100%" maxWidth="59px">
                        <Text as="p" variant="bodyMd" fontWeight="medium">
                            {value}
                        </Text>
                    </Box>
                    <Button icon={isCopied ? CheckIcon : ClipboardIcon} variant="plain" onClick={handleToClipboard} />
                </div>
            </BlockStack>
        </Box>
    );
};
