import * as React from 'react';
import { Placement, OffsetOptions, FlipOptions, ShiftOptions } from '@floating-ui/react';
import { usePopover } from './usePopover';
import { PopoverContext } from './context';

export interface PopoverOptions {
    initialOpen?: boolean;
    placement?: Placement;
    modal?: boolean;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
    offsetOptions?: OffsetOptions;
    flipOptions?: FlipOptions;
    shiftOptions?: ShiftOptions;
}

export function Popover({
    children,
    modal = false,
    ...restOptions
}: {
    children: React.ReactNode;
} & PopoverOptions) {
    const popover = usePopover({ modal, ...restOptions });
    return <PopoverContext.Provider value={popover}>{children}</PopoverContext.Provider>;
}
