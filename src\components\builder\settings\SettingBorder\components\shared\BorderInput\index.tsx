import { FC, useCallback, useEffect, useState } from 'react';
import './style.scss';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { useBlockStore, Auto_BlockDeviceType, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { restrictValueInput } from '@/utils/restrictValueInput';
interface BorderInputProps {
    id: string;
    isActive: boolean;
    className?: string;
    isUpdateConfigs?: boolean;
    path: string;
    blockId: string;
    isLock?: boolean;
    onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
    onBlur?: () => void;
    type?: 'border' | 'radius';
}

export const BorderInput: FC<BorderInputProps> = ({
    id,
    isActive,
    className,
    isUpdateConfigs,
    path,
    blockId,
    isLock,
    onFocus,
    onBlur,
    type = 'border',
}) => {
    const modelValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.val`, blockId)
        : getBlockBPProperty(`${path}.val`, blockId);

    const [localValue, setLocalValue] = useState(modelValue);

    useEffect(() => {
        setLocalValue(modelValue);
    }, [modelValue]);

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice as Auto_BlockDeviceType);

    const handleChange = useCallback((value: string) => {
        setLocalValue(value);
    }, []);

    const handleFocus = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            e.target.select();
            onFocus?.(e);
        },
        [onFocus],
    );

    const handleUpdateValue = useCallback(
        (inputValue: string) => {
            const value = inputValue === '' || inputValue.includes('-') ? '0' : inputValue;
            const basePath = path.split('.').slice(0, -1).join('.');

            const update = (targetPath: string) => {
                if (isUpdateConfigs) {
                    updateBlockConfigsProperty(blockId, targetPath, value);
                } else {
                    updateBlockProperty(blockId, currentDevice, targetPath, value);
                }
            };

            if (type === 'border') {
                const sides = ['top', 'right', 'bottom', 'left'];
                if (!isLock) {
                    sides.forEach((side) => update(`${basePath}.${side}.val`));
                } else {
                    update(`${path}.val`);
                }
            } else if (type === 'radius') {
                const sides = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
                if (!isLock) {
                    sides.forEach((side) => update(`${basePath}.${side}.val`));
                } else {
                    update(`${path}.val`);
                }
            }
        },
        [isUpdateConfigs, updateBlockConfigsProperty, updateBlockProperty, blockId, path, currentDevice, isLock, type],
    );

    const handleBlur = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            const finalValue = restrictValueInput(e.target.value, 0, 100);
            handleUpdateValue(finalValue);
            setLocalValue(finalValue);
            e.target.blur();
            onBlur?.();
        },
        [handleUpdateValue, onBlur],
    );

    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, 0, 100);
                handleUpdateValue(finalValue);
                setLocalValue(finalValue);
            }
        },
        [handleUpdateValue],
    );

    const handleKeyUp = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                const finalValue = restrictValueInput((e.target as HTMLInputElement).value, 0, 100);
                setLocalValue(finalValue);
                handleUpdateValue(finalValue);
            }
        },
        [handleUpdateValue],
    );

    return (
        <div className={`input-suffix ${isActive ? 'active' : ''} ${className || ''}`} onKeyDown={handleKeyDown}>
            <input
                type="number"
                inputMode="decimal"
                pattern="[0-9]*(.[0-9]+)?"
                id={id}
                onBlur={(e) => handleBlur(e)}
                value={localValue}
                onChange={(e) => handleChange(e.target.value)}
                onKeyUp={handleKeyUp}
                role="spinbutton"
                aria-valuemin={0}
                aria-valuemax={100}
                autoComplete="off"
                autoCorrect="off"
                aria-valuenow={localValue}
                aria-valuetext={localValue?.toString()}
                onFocus={(e) => {
                    handleFocus(e);
                }}
            />
            <div className="suffix">px</div>
        </div>
    );
};
