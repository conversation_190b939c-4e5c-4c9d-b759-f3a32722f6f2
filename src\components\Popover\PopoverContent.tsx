import * as React from 'react';
import { FloatingPortal, FloatingFocusManager, useMergeRefs } from '@floating-ui/react';
import { usePopoverContext } from './context';

export const PopoverContent = React.forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>(function PopoverContent(
    { style, ...props },
    propRef,
) {
    const { context: floatingContext, ...context } = usePopoverContext();
    const ref = useMergeRefs([context.refs.setFloating, propRef]);

    if (!floatingContext.open) return null;

    return (
        <FloatingPortal>
            <FloatingFocusManager context={floatingContext} modal={context.modal}>
                <div ref={ref} style={{ ...context.floatingStyles, ...style }} {...context.getFloatingProps(props)}>
                    {props.children}
                </div>
            </FloatingFocusManager>
        </FloatingPortal>
    );
});
