import { FC } from 'react';
import { <PERSON>, Divider, Grid, InlineGrid, InlineStack, SkeletonBodyText, SkeletonDisplayText } from '@shopify/polaris';
import styled from '@emotion/styled';
import { analyticsConfigs } from './configs';

export const SkeletonAnalytics: FC = () => {
    const Container = styled.div`
        .Polaris-SkeletonBodyText__SkeletonBodyTextContainer {
            width: 54px;
            .Polaris-SkeletonBodyText {
                height: 12px;
            }
        }
        .Polaris-InlineGrid {
            align-items: start;
        }
        .Polaris-SkeletonDisplayText--sizeSmall {
            width: 12px;
            height: 12px;
        }
        .Polaris-InlineStack {
            width: 100%;
            .Polaris-InlineStack {
                align-items: end;
                .Polaris-SkeletonDisplayText--sizeSmall {
                    width: 72px;
                    height: 26px;
                }
                .Polaris-SkeletonBodyText__SkeletonBodyTextContainer {
                    width: 46px;
                    height: 16px;
                    .Polaris-SkeletonBodyText {
                        height: 16px;
                    }
                }
            }
        }
        .Polaris-Divider {
            margin-top: 20px !important;
            margin-bottom: 16px !important;
        }
    `;

    const ContainerTraffic = styled.div`
        width: 100%;
        margin-top: 47px;
        .Polaris-Box {
            padding: 0;
        }
        .Polaris-ShadowBevel {
            height: 503px;
            background-color: #fff;
            border-radius: 16px;
            padding: 28px 40px;
        }

        .Polaris-SkeletonDisplayText--sizeMedium {
            width: 93px;
            height: 20px;
        }
        .Polaris-SkeletonBodyText__SkeletonBodyTextContainer {
            width: 295px;
            .Polaris-SkeletonBodyText {
                height: 14px;
            }
        }
    `;
    const ContainerTrafficHeaderLeft = styled.div`
        display: flex;
        row-gap: 12px;
        flex-direction: column;
    `;
    const ContainerTrafficHeaderRight = styled.div`
        display: flex;
        column-gap: 20px;
        flex-direction: row;
        justify-content: flex-end;
        .Polaris-InlineGrid {
            justify-content: center;
        }
        .Polaris-InlineStack {
            align-items: center;
            width: 67px;
            gap: 5px;
        }
        .Polaris-SkeletonBodyText__SkeletonBodyTextContainer {
            width: 50px;
            .Polaris-SkeletonBodyText {
                height: 6px;
            }
        }
        .Polaris-SkeletonDisplayText--sizeMedium {
            width: 12px;
            height: 12px;
        }
    `;
    const ContainerTrafficBody = styled.div`
        display: flex;
        flex-direction: row;
        column-gap: 22px;
        margin-top: 41px;
    `;
    const ContainerTrafficBodyLeft = styled.div`
        display: flex;
        flex-direction: column;
        row-gap: 35px;

        .Polaris-SkeletonDisplayText--sizeMedium {
            width: 52px;
            height: 15px;
        }
    `;
    const ContainerTrafficBodyRight = styled.div`
        display: flex;
        flex-direction: column;
        row-gap: 35px;
        width: 100%;
        .Polaris-SkeletonBodyText__SkeletonBodyTextContainer {
            width: 100%;
            padding-top: 13px;
            .Polaris-SkeletonBodyText {
                height: 299px;
            }
        }
        .Polaris-InlineStack {
            justify-content: space-between;
            .Polaris-SkeletonDisplayText--sizeMedium {
                width: 52px;
                height: 15px;
            }
        }
    `;
    return (
        <Container>
            <Grid>
                {analyticsConfigs.map((config) => (
                    <Grid.Cell key={config.id} columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                        <Card padding="400" background="bg-surface-secondary">
                            <InlineStack gap="050">
                                <SkeletonBodyText lines={1} />
                                <SkeletonDisplayText size="small" />
                            </InlineStack>
                            <Divider borderColor="border" />
                            <InlineStack>
                                <InlineStack align="space-between">
                                    <SkeletonDisplayText size="small" />
                                    <SkeletonBodyText lines={1} />
                                </InlineStack>
                            </InlineStack>
                        </Card>
                    </Grid.Cell>
                ))}
            </Grid>
            <ContainerTraffic>
                <Card>
                    <InlineGrid columns={2} alignItems="end">
                        <ContainerTrafficHeaderLeft>
                            <SkeletonDisplayText />
                            <SkeletonBodyText lines={1} />
                        </ContainerTrafficHeaderLeft>
                        <ContainerTrafficHeaderRight>
                            <InlineStack>
                                <SkeletonDisplayText />
                                <SkeletonBodyText lines={1} />
                            </InlineStack>
                            <InlineStack>
                                <SkeletonDisplayText />
                                <SkeletonBodyText lines={1} />
                            </InlineStack>
                        </ContainerTrafficHeaderRight>
                    </InlineGrid>
                    <ContainerTrafficBody>
                        <ContainerTrafficBodyLeft>
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                            <SkeletonDisplayText />
                        </ContainerTrafficBodyLeft>
                        <ContainerTrafficBodyRight>
                            <SkeletonBodyText lines={1} />
                            <InlineStack>
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                                <SkeletonDisplayText />
                            </InlineStack>
                        </ContainerTrafficBodyRight>
                    </ContainerTrafficBody>
                </Card>
            </ContainerTraffic>
        </Container>
    );
};
