import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import {
    SettingElementID,
    SettingsDisplay,
    SettingsDimension,
    SettingsCustomCSS,
    SettingsAlign,
    SettingsAnimation,
} from '@/components/builder/settings';
import { BaseCollapse, BaseToggle, BaseHasBorderLayout } from '@/components/builder/base';

export const Advanced = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <BaseHasBorderLayout>
            <SettingElementID value={selectedBlockId} />
            <BaseCollapse label="Align">
                <SettingsAlign blockId={selectedBlockId} path="alignment" />
            </BaseCollapse>
            <BaseCollapse label="Size">
                <SettingsDimension blockId={selectedBlockId} boxProps={{ paddingBlockStart: '300' }} />
            </BaseCollapse>
            <BaseCollapse label="Display on">
                <SettingsDisplay blockId={selectedBlockId} boxProps={{ paddingBlockStart: '300' }} />
            </BaseCollapse>
            <BaseCollapse label="Animations">
                <SettingsAnimation path="animation" blockId={selectedBlockId} isUpdateConfigs />
            </BaseCollapse>
            {/* <BaseCollapse label="Position">
                <div>Position</div>
            </BaseCollapse> */}
            <BaseCollapse
                label="Custom CSS"
                labelContent={(open, setOpen) => <BaseToggle valueData={open} onChange={() => setOpen?.(!open)} />}
            >
                <SettingsCustomCSS path="customCSS" blockId={selectedBlockId} isUpdateConfigs label="Custom CSS" />
            </BaseCollapse>
        </BaseHasBorderLayout>
    );
};
