import { useState, useMemo } from 'react';
import clsx from 'clsx';
import { AppliedFilterInterface, SortButtonChoice } from '@shopify/polaris';
import { apiAddress } from '@/configs/apiAddress';
import { capitalizeFirstLetter } from '@/utils';
import { TemplateSectionResponse, SectionCategoryResponse } from '@/pages/SectionsPage/types';
import { QueryResult, useQueries } from './useQueries';
import { useDebounce } from './useDebounce';

interface TemplatesResultsMap {
    templates: QueryResult<TemplateSectionResponse>;
    categories: QueryResult<SectionCategoryResponse>;
}

export function useTemplateSections() {
    const [category, setCategory] = useState<string[]>([]);
    const [queryValue, setQueryValue] = useState<string>('');
    const [sortSelected, setSortSelected] = useState<string[]>(['updated desc']);
    const [page, setPage] = useState<number>(1);
    const LIMIT = 9;

    const searchValue = useDebounce(queryValue, 500, () => setPage(1));

    const templatesUrl = useMemo(() => {
        return clsx(
            apiAddress.shopSections.templates,
            `?currentPage=${page}`,
            `&perPage=${LIMIT}`,
            `&sortName=${sortSelected[0].split(' ')[0]}`,
            `&order=${sortSelected[0].split(' ')[1]}`,
            searchValue ? `&keyword=${searchValue}` : '',
            category.length > 0 && `&categoryId=${category}`,
        ).replace(/\s+/g, '');
    }, [page, LIMIT, sortSelected, searchValue, category]);

    const { results, isLoading, isError, refetchAll } = useQueries<TemplatesResultsMap>([
        {
            key: 'categories',
            url: apiAddress.shopSections.category,
            method: 'GET',
            sleepTime: 300,
        },
        {
            key: 'templates',
            url: templatesUrl,
            method: 'GET',
            sleepTime: 300,
            dependencies: [templatesUrl],
        },
    ]);

    const categories = useMemo(() => results.categories?.data?.result?.data || [], [results.categories]);
    const templates = useMemo(() => results.templates?.data?.result?.data?.list || [], [results.templates]);
    const totalTemplates = useMemo(() => results.templates?.data?.result?.data?.total || 0, [results.templates]);

    const isLoadingCategories = results.categories?.isLoading || false;
    const isLoadingTemplates = results.templates?.isLoading || false;

    const categoriesError = results.categories?.error || null;
    const templatesError = results.templates?.error || null;

    const sortChoices: SortButtonChoice[] = [
        {
            label: 'Updated',
            value: 'updated asc',
            directionLabel: 'Oldest First',
        },
        {
            label: 'Updated',
            value: 'updated desc',
            directionLabel: 'Newest First',
        },
    ];

    const categoryChoices = useMemo(() => {
        return categories.map((item) => ({
            label: item.title,
            value: item.id,
        }));
    }, [categories]);

    const appliedFilters: AppliedFilterInterface[] = [];

    if (category && category.length > 0) {
        const key = 'category';
        appliedFilters.push({
            key,
            label:
                'Category is ' +
                category
                    .map((id) => capitalizeFirstLetter(categoryChoices.find((item) => item.value === id)?.label || ''))
                    .join(', '),
            onRemove: () => setCategory([]),
        });
    }

    return {
        // Data
        templates,
        categories,
        totalTemplates,
        categoryChoices,
        sortChoices,
        appliedFilters,

        // State
        page,
        category,
        queryValue,
        sortSelected,
        searchValue,

        // Action handlers
        setPage,
        setCategory,
        setQueryValue,
        setSortSelected,

        // Query state
        isLoading,
        isLoadingCategories,
        isLoadingTemplates,
        isError,
        categoriesError,
        templatesError,

        // Constants
        limit: LIMIT,

        // Actions
        refetch: refetchAll,
    };
}
