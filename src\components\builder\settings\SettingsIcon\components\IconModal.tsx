import { FC, ReactNode, useState, useEffect, useCallback, useMemo, Fragment, useRef } from 'react';
import { GridCellRenderer, MultiGrid } from 'react-virtualized';
import { Box, Button, InlineStack, TextField, Tabs, Icon, BlockStack, Text, Spinner, Modal } from '@shopify/polaris';
import { SearchIcon, XIcon } from '@shopify/polaris-icons';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';
import { BaseModal } from '@/components/builder/base/BaseModal';
import { useQuery } from '@/hooks/useQuery';
import { apiAddress } from '@/configs/apiAddress';
import '@/components/builder/settings/SettingsIcon/style.scss';
import { ItemManageProps } from '@/stores/appStore/types';
import { httpRequest } from '@/configs/api';

interface IconModalProps {
    elementTriggerProps?: { children: ReactNode } & Partial<typeof InlineStack>;
    isOpenModalShape: boolean;
    onOpenModalShape: (isOpen: boolean) => void;
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

const PREFIXES = ['flex1-', 'flex2-', 'flex3-', 'flex4-', 'flex5-'];
const DEFAULT_TABS = [
    { id: 'customer', content: 'Uploaded', accessibilityLabel: 'Uploaded', panelID: 'customer-content' },
    { id: 'icons', content: 'Icons', accessibilityLabel: 'Icons', panelID: 'icons-content' },
];

export const IconModal: FC<IconModalProps> = ({
    elementTriggerProps,
    blockId,
    path,
    isOpenModalShape,
    onOpenModalShape,
    isUpdateConfigs,
}) => {
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const [iconSearchTerm, setIconSearchTerm] = useState<string>('');
    const currentIcon = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path, blockId);

    const [selectedTab, setSelectedTab] = useState(0);
    const [selectedIconValue, setSelectedIconValue] = useState(currentIcon);
    const [gridDimensions, setGridDimensions] = useState({ width: 0, height: 0, columns: 0 });
    const [iconToDelete, setIconToDelete] = useState<ItemManageProps | null>(null);
    const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);

    const iconsDataCache = useRef<{ result: { data: { data: ItemManageProps[] } } } | null>(null);
    const [iconsTabData, setIconsTabData] = useState<ItemManageProps[]>([]);
    const [customerTabData, setCustomerTabData] = useState<ItemManageProps[]>([]);

    useEffect(() => {
        setSelectedIconValue(currentIcon);
    }, [isOpenModalShape, currentIcon]);

    useEffect(() => {
        const updateGridDimensions = () => {
            const width = Math.min(document.documentElement.clientWidth - 140, 1288);
            const height = Math.min(document.documentElement.clientHeight - 314, 706);
            const columns = Math.min(Math.floor(document.documentElement.clientWidth / 130 - 1), 9);
            setGridDimensions({ width, height, columns });
        };

        updateGridDimensions();
        window.addEventListener('resize', updateGridDimensions);
        return () => window.removeEventListener('resize', updateGridDimensions);
    }, []);

    const handleTabChange = useCallback((selectedTabIndex: number) => {
        setSelectedTab(selectedTabIndex);
        setIconSearchTerm('');
    }, []);

    const handleSearchInputChange = useCallback((value: string) => setIconSearchTerm(value), []);
    const handleChangeIcon = useCallback((newValue: string) => setSelectedIconValue(newValue), []);
    const handleClearSearch = useCallback(() => setIconSearchTerm(''), []);
    const handleCancel = useCallback(() => setSelectedIconValue(''), []);

    const handleChooseIcon = useCallback(() => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, selectedIconValue);
        } else {
            updateBlockProperty(blockId, currentDevice, path, selectedIconValue);
        }
    }, [
        blockId,
        currentDevice,
        path,
        updateBlockConfigsProperty,
        updateBlockProperty,
        selectedIconValue,
        isUpdateConfigs,
    ]);

    const shouldFetchIcons = selectedTab === 0 && !iconsDataCache.current;
    const { isLoading: isIconsLoading } = useQuery<{
        result: { data: { data: ItemManageProps[] } };
    }>({
        url: apiAddress.shape + `?currentPage=1&perPage=2200&type=icons`,
        method: 'GET',
        sleepTime: 300,
        enabled: shouldFetchIcons,
        onSuccess: (data) => {
            iconsDataCache.current = data;
            setIconsTabData(data?.result?.data?.data || []);
        },
    });

    const { isLoading: isUploadedLoading } = useQuery<{
        result: { data: { data: ItemManageProps[] } };
    }>({
        url: apiAddress.shape + `?currentPage=1&perPage=2200&type=customer`,
        method: 'GET',
        sleepTime: 300,
        enabled: selectedTab === 0,
        extraDeps: selectedTab,
        onSuccess: (data) => {
            setCustomerTabData(data?.result?.data?.data || []);
        },
    });

    useEffect(() => {
        if (iconsDataCache.current && selectedTab === 0) {
            setIconsTabData(iconsDataCache.current?.result?.data?.data || []);
        }
    }, [selectedTab]);

    const currentTabData = selectedTab === 1 ? iconsTabData : customerTabData;
    const isLoading = (selectedTab === 1 && isIconsLoading) || (selectedTab === 1 && isUploadedLoading);

    const filteredIconList = useMemo(() => {
        const lowerSearchValue = iconSearchTerm.toLowerCase();
        return {
            iconNew: currentTabData.filter((item: { title: string }) =>
                item.title.toLowerCase().includes(lowerSearchValue),
            ),
        };
    }, [iconSearchTerm, currentTabData]);

    const renderIconGridCell = useCallback(
        (configKey: keyof typeof filteredIconList, flexPrefix: string) => {
            const cellRenderer: GridCellRenderer = ({ columnIndex, key, rowIndex, style }) => {
                const index = rowIndex * gridDimensions.columns + columnIndex;
                const iconName = filteredIconList[configKey][index];

                if (!iconName) return null;

                const isSelected = selectedIconValue === iconName.value;
                const commonStyle = {
                    ...style,
                    left: Number(style.left),
                    top: Number(style.top),
                    background: isSelected ? '#ebebeb' : 'transparent',
                };

                const handleClick = () => {
                    // Clear all highlighted elements
                    PREFIXES.forEach((prefix) => {
                        document.querySelectorAll(`[id^="${prefix}"]`).forEach((element) => {
                            (element as HTMLElement).style.background = 'transparent';
                        });
                    });

                    // Highlight clicked element
                    const element = document.getElementById(`${flexPrefix}${key}`);
                    if (element) {
                        element.style.background = '#ebebeb';
                    }

                    handleChangeIcon(iconName.value);
                };

                const handleDeleteIcon = (iconItem: ItemManageProps) => {
                    setIconToDelete(iconItem);
                    setIsConfirmDeleteOpen(true);
                };

                // Only show delete button for "customer" tab
                const isCustomerTab = selectedTab === 0;

                return (
                    <Fragment key={`${flexPrefix}-${key}`}>
                        <div
                            className="shape-manage__item"
                            id={`${flexPrefix}${key}`}
                            style={commonStyle}
                            onClick={handleClick}
                        >
                            <div className="item__svg" dangerouslySetInnerHTML={{ __html: iconName.value }} />
                            {isCustomerTab && (
                                <button
                                    className="shape-manage__item__delete"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteIcon(iconName);
                                    }}
                                >
                                    <Icon source={XIcon} />
                                </button>
                            )}
                        </div>
                    </Fragment>
                );
            };
            return cellRenderer;
        },
        [filteredIconList, gridDimensions.columns, handleChangeIcon, selectedIconValue, selectedTab],
    );

    // UI Components
    const renderSearchField = (
        <div className="shape-manage__content__search">
            <TextField
                label="Search for icon"
                labelHidden
                value={iconSearchTerm}
                onChange={handleSearchInputChange}
                prefix={<Icon source={SearchIcon} />}
                suffix={iconSearchTerm ? <Button variant="tertiary" icon={XIcon} onClick={handleClearSearch} /> : null}
                placeholder="Search for icon"
                autoComplete="off"
            />
        </div>
    );

    const renderEmptyState = (
        <BlockStack align="center" inlineAlign="center" gap="200">
            <Icon source={SearchIcon} />
            <Text as="p" variant="headingMd" tone="subdued">
                No icon found
            </Text>
            <Text as="p" variant="bodyMd" tone="subdued">
                Try changing the search term
            </Text>
        </BlockStack>
    );

    const renderLoadingState = (
        <BlockStack align="center" inlineAlign="center" gap="200">
            <Spinner size="small" />
            <Text as="p" variant="bodyMd" tone="subdued">
                Loading icons...
            </Text>
        </BlockStack>
    );

    // Show empty grid while loading customer tab to prevent flickering
    const renderIconGrid = (
        <Box>
            <div className="shape-manage__icons">
                {isLoading && selectedTab === 1 ? (
                    renderLoadingState
                ) : filteredIconList.iconNew.length === 0 ? (
                    renderEmptyState
                ) : (
                    <MultiGrid
                        cellRenderer={renderIconGridCell('iconNew', 'flex5-')}
                        columnWidth={130}
                        columnCount={gridDimensions.columns}
                        height={gridDimensions.height}
                        rowHeight={130}
                        rowCount={Math.ceil(filteredIconList.iconNew.length / gridDimensions.columns)}
                        width={gridDimensions.width}
                    />
                )}
            </div>
        </Box>
    );

    // Xác nhận xóa icon
    const handleConfirmDelete = async () => {
        if (!iconToDelete) return;
        const response = await httpRequest.delete(`${apiAddress.shape}/${iconToDelete.id}`);
        if (response.status === 200) {
            setIsConfirmDeleteOpen(false);
            setIconToDelete(null);

            // Refresh customer tab data after deletion
            if (selectedTab === 1) {
                const refreshResponse = await httpRequest.get(
                    apiAddress.shape + `?currentPage=1&perPage=2200&type=customer`,
                );
                if (refreshResponse.status === 200) {
                    setCustomerTabData(refreshResponse.data?.result?.data?.data || []);
                }
            }
        }
    };

    const handleCancelDelete = () => {
        setIsConfirmDeleteOpen(false);
        setIconToDelete(null);
    };

    return (
        <>
            <Modal
                open={isConfirmDeleteOpen}
                onClose={handleCancelDelete}
                title="Confirm delete svg"
                primaryAction={{
                    content: 'Delete',
                    destructive: true,
                    onAction: handleConfirmDelete,
                }}
                secondaryActions={[
                    {
                        content: 'Cancel',
                        onAction: handleCancelDelete,
                    },
                ]}
            >
                <Modal.Section>Are you sure you want to delete this svg?</Modal.Section>
            </Modal>
            <BaseModal
                isOpen={isOpenModalShape}
                onOpenChange={(isOpen) => {
                    if (isConfirmDeleteOpen) return;
                    onOpenModalShape(isOpen);
                }}
                onOk={handleChooseIcon}
                isDisabled={!selectedIconValue}
                onCancel={handleCancel}
                okTitle="Change"
                cancelTitle="Cancel"
                elementContentProps={{}}
                elementTriggerProps={elementTriggerProps}
                textTooltip="Help"
                elementModalContentProps={{
                    title: 'Change icon',
                    children: (
                        <div className="shape-manage__content">
                            {renderSearchField}
                            <Tabs tabs={DEFAULT_TABS} selected={selectedTab} onSelect={handleTabChange}>
                                {renderIconGrid}
                            </Tabs>
                        </div>
                    ),
                }}
            />
        </>
    );
};
