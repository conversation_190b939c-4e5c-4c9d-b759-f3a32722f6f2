import { forwardRef, HTMLAttributes } from 'react';
import { mergeProps } from '@zag-js/react';
import { Text } from '@shopify/polaris';
import { createSlot, AsChildProps } from './helper';
import { useColorPickerContext } from './use-color-picker-context.ts';

type ColorPickerTriggerProps = AsChildProps<HTMLAttributes<HTMLButtonElement>> & {
    label?: string;
};

export const ColorPickerTrigger = forwardRef<HTMLButtonElement, ColorPickerTriggerProps>((props, ref) => {
    const { asChild, label, ...restProps } = props;
    const colorPicker = useColorPickerContext();
    const mergedProps = mergeProps(colorPicker.getTriggerProps(), restProps);

    if (asChild) {
        return createSlot({ ...mergedProps, ref });
    }

    return (
        <div css={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Text as="p" variant="bodyMd">
                {label}
            </Text>
            <button {...mergedProps} ref={ref}>
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="14" cy="13.9987" r="11.6667" stroke="#CCCCCC" />
                    <circle cx="14" cy="13.9987" r="8.16667" fill={colorPicker.valueAsString} />
                </svg>
            </button>
        </div>
    );
});

ColorPickerTrigger.displayName = 'ColorPickerTrigger';
