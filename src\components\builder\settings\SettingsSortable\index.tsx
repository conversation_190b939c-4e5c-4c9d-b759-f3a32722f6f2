import { SortableProps, SortableItemRequireProps } from '@/components';
import { useSettings } from '@/pages/BuilderPage/hooks';
import { BaseSortable } from '../../base';

type SettingsSortableProps<T extends SortableItemRequireProps> = Partial<
    Omit<SortableProps<T>, 'items' | 'onChange'>
> & {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
};

export function SettingsSortable<T extends SortableItemRequireProps>({
    blockId,
    path,
    isUpdateConfigs,
    extendNode,
    itemRenderer = ({ data }) => <div>{data.id}</div>,
    ...otherProps
}: SettingsSortableProps<T>) {
    const { value: items, updateValue: updateItems } = useSettings({
        blockId,
        path,
        isUpdateConfigs,
    });

    const onChange = (items: T[]) => {
        updateItems(items);
    };

    return (
        <BaseSortable
            items={items}
            onChange={onChange}
            extendNode={extendNode}
            itemRenderer={itemRenderer}
            {...otherProps}
        />
    );
}
