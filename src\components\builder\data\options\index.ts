import { ActionListItemDescriptor } from '@shopify/polaris';
import {
    ListBulletedIcon,
    ListNumberedIcon,
    TextIndentIcon,
    OutdentIcon,
    TextAlignLeftIcon,
    TextAlignCenterIcon,
    TextAlignRightIcon,
    ViewportWideIcon,
    ViewportTallIcon,
} from '@shopify/polaris-icons';
import { IconSource, TabProps } from '@shopify/polaris';
import { ReactComponent as OverlineIcon } from '@/assets/svgs/SwitchTabs/overline.svg';
import { ReactComponent as LineThroughIcon } from '@/assets/svgs/SwitchTabs/line-through.svg';
import { ReactComponent as UnderlineIcon } from '@/assets/svgs/SwitchTabs/underline.svg';
import { ReactComponent as DefaultTextIcon } from '@/assets/svgs/SwitchTabs/default-text.svg';
import { ReactComponent as AlignTopIcon } from '@/assets/svgs/SwitchTabs/align-top.svg';
import { ReactComponent as AlignMiddleIcon } from '@/assets/svgs/SwitchTabs/align-middle.svg';
import { ReactComponent as AlignBottomIcon } from '@/assets/svgs/SwitchTabs/align-bottom.svg';
import { ReactComponent as AlignLeftIcon } from '@/assets/svgs/SwitchTabs/align-left.svg';
import { ReactComponent as AlignRightIcon } from '@/assets/svgs/SwitchTabs/align-right.svg';
import { ReactComponent as AlignCenterIcon } from '@/assets/svgs/SwitchTabs/align-center.svg';
import { ReactComponent as LeftToRightIcon } from '@/assets/svgs/SwitchTabs/left-to-right.svg';
import { ReactComponent as RightToLeftIcon } from '@/assets/svgs/SwitchTabs/right-to-left.svg';
import { ReactComponent as TextJustifyIcon } from '@/assets/svgs/SwitchTabs/text-justify.svg';
import { OptionProps } from '@/components/builder/base/BaseSwitchTab';
// TODO : if icon is IconSource , we need to use icon as IconSource  to put into icon prop
// Example : { id: 'underline', contentTooltip: 'Underline', icon: TextUnderlineIcon as IconSource, isPolarisIcon: true },

export const textDecorationOptions: OptionProps[] = [
    { id: 'default', contentTooltip: 'Default', icon: DefaultTextIcon },
    { id: 'overline', contentTooltip: 'Overline', icon: OverlineIcon },
    { id: 'line-through', contentTooltip: 'Line through', icon: LineThroughIcon },
    { id: 'underline', contentTooltip: 'Underline', icon: UnderlineIcon },
];

export const textOrderOptions: OptionProps[] = [
    { id: 'ordered', contentTooltip: 'Ordered list', icon: ListNumberedIcon as IconSource, isPolarisIcon: true },
    { id: 'bullet', contentTooltip: 'Bullet list', icon: ListBulletedIcon as IconSource, isPolarisIcon: true },
];

export const textDirectionOptions: OptionProps[] = [
    { id: 'rtl', contentTooltip: 'RTL', icon: RightToLeftIcon },
    { id: 'ltr', contentTooltip: 'LTR', icon: LeftToRightIcon },
];

export const textContentOptions: OptionProps[] = [
    { id: 'indent', contentTooltip: 'Indent', icon: TextIndentIcon as IconSource, isPolarisIcon: true },
    { id: 'outdent', contentTooltip: 'Outdent', icon: OutdentIcon as IconSource, isPolarisIcon: true },
];

export const textAlignOptions: OptionProps[] = [
    { id: 'left', contentTooltip: 'Text left', icon: TextAlignLeftIcon as IconSource, isPolarisIcon: true },
    { id: 'center', contentTooltip: 'Text center', icon: TextAlignCenterIcon as IconSource, isPolarisIcon: true },
    { id: 'right', contentTooltip: 'Text right', icon: TextAlignRightIcon as IconSource, isPolarisIcon: true },
    { id: 'justify', contentTooltip: 'Text justify', icon: TextJustifyIcon },
];

export const alignHorizontalOptions: OptionProps[] = [
    { id: 'left', contentTooltip: 'Left', icon: AlignLeftIcon },
    { id: 'center', contentTooltip: 'Center', icon: AlignCenterIcon },
    { id: 'right', contentTooltip: 'Right', icon: AlignRightIcon },
];

export const alignHorizontalOptions2: OptionProps[] = [
    { id: 'start', contentTooltip: 'Start', icon: AlignLeftIcon },
    { id: 'center', contentTooltip: 'Center', icon: AlignCenterIcon },
    { id: 'end', contentTooltip: 'End', icon: AlignRightIcon },
];

export const alignVerticalOptions: OptionProps[] = [
    { id: 'top', contentTooltip: 'Top', icon: AlignTopIcon },
    { id: 'middle', contentTooltip: 'Middle', icon: AlignMiddleIcon },
    { id: 'bottom', contentTooltip: 'Bottom', icon: AlignBottomIcon },
];

export const tabsInspectorOptions: TabProps[] = [
    { id: 'basic', content: 'Basic' },
    { id: 'advanced', content: 'Advanced' },
];

export const textFormatOptions: ActionListItemDescriptor[] = [
    { content: 'Heading 1', id: 'heading-1' },
    { content: 'Heading 2', id: 'heading-2' },
    { content: 'Heading 3', id: 'heading-3' },
    { content: 'Heading 4', id: 'heading-4' },
    { content: 'Heading 5', id: 'heading-5' },
    { content: 'Heading 6', id: 'heading-6' },
    { content: 'Paragraph', id: 'paragraph' },
];

export const textFontWeightOptions: ActionListItemDescriptor[] = [
    { content: '100', id: '100' },
    { content: '200', id: '200' },
    { content: '300', id: '300' },
    { content: '400', id: '400' },
    { content: '500', id: '500' },
    { content: '600', id: '600' },
    { content: '700', id: '700' },
    { content: '800', id: '800' },
    { content: '900', id: '900' },
];

export const textTransformOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Uppercase', id: 'uppercase' },
    { content: 'Lowercase', id: 'lowercase' },
    { content: 'Capitalize', id: 'capitalize' },
];

export const textFontStyleOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Italic', id: 'italic' },
];

export const borderTypeOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Solid', id: 'solid' },
    { content: 'Dashed', id: 'dashed' },
    { content: 'Dotted', id: 'dotted' },
    { content: 'Double', id: 'double' },
    { content: 'Groove', id: 'groove' },
    { content: 'Ridge', id: 'ridge' },
    { content: 'Inset', id: 'inset' },
    { content: 'Outset', id: 'outset' },
];

export const buttonTypeOptions: ActionListItemDescriptor[] = [
    { content: 'Text and icon', id: 'text-and-icon' },
    { content: 'Icon only', id: 'icon-only' },
    { content: 'Text only', id: 'text-only' },
    { content: 'Nothing', id: 'nothing' },
];

export const textShadowTypeOptions: OptionProps[] = [
    { id: 'box', contentTooltip: 'Box' },
    { id: 'realistic', contentTooltip: 'Realistic' },
];

export const textShadowPositionOptions: OptionProps[] = [
    { id: 'outside', contentTooltip: 'Outside' },
    { id: 'inside', contentTooltip: 'Inside' },
];

export const buttonAnimationTypeOptions: ActionListItemDescriptor[] = [
    { content: 'None', id: 'none' },
    { content: 'Bounce', id: 'bounce' },
    { content: 'Flash', id: 'flash' },
    { content: 'Pulse', id: 'pulse' },
    { content: 'Rubber band', id: 'rubberBand' },
    { content: 'ShakeX', id: 'shakeX' },
    { content: 'ShakeY', id: 'shakeY' },
    { content: 'Swing', id: 'swing' },
    { content: 'Tada', id: 'tada' },
];

export const buttonAnimationLoopOptions: ActionListItemDescriptor[] = [
    { content: '1', id: '1' },
    { content: '2', id: '2' },
    { content: '3', id: '3' },
    { content: 'Infinite    ', id: 'infinite' },
];

export const buttonPositionOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Sticky', id: 'sticky' },
    { content: 'Pinned', id: 'pinned' },
];

export const backgroundAttachmentOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Fixed', id: 'fixed' },
    { content: 'Local', id: 'local' },
    { content: 'Scroll', id: 'scroll' },
];

export const backgroundPositionOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Left top', id: 'left top' },
    { content: 'Left center', id: 'left center' },
    { content: 'Left bottom', id: 'left bottom' },
    { content: 'Center top', id: 'center top' },
    { content: 'Center center', id: 'center center' },
    { content: 'Center bottom', id: 'center bottom' },
    { content: 'Right top', id: 'right top' },
    { content: 'Right center', id: 'right center' },
    { content: 'Right bottom', id: 'right bottom' },
];

export const backgroundFillOptions: ActionListItemDescriptor[] = [
    { content: 'Cover', id: 'cover' },
    { content: 'Contain', id: 'contain' },
    { content: 'Percent', id: 'percent' },
];

export const backgroundRepeatOptions: OptionProps[] = [
    { id: 'no', contentTooltip: 'No' },
    { id: 'yes', contentTooltip: 'Yes' },
];

export const buttonDirectionOptions: OptionProps[] = [
    { id: 'row', contentTooltip: 'Row', icon: ViewportWideIcon as IconSource, isPolarisIcon: true },
    { id: 'column', contentTooltip: 'Column', icon: ViewportTallIcon as IconSource, isPolarisIcon: true },
];

export const letterSpacingOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: 'Auto', id: 'auto' },
    { content: 'Narrow', id: 'narrow' },
    { content: 'Wide', id: 'wide' },
    { content: 'Custom', id: 'custom' },
];

export const lineSpacingOptions: ActionListItemDescriptor[] = [
    { content: 'Default', id: 'default' },
    { content: '1 line', id: '1' },
    { content: '1.5', id: '1.5' },
    { content: '2', id: '2' },
    { content: 'Custom', id: 'custom' },
];
