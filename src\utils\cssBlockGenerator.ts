import { Auto_BlockData } from '@giaminhautoketing/auto-builder';

type CssGenerator = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile') => string;

export const getBlockClassName = (blockId?: string, blockType?: string) => {
    return blockType && blockId ? `atk-${blockType}-${blockId}` : blockId ? `atk-${blockId}` : '';
};

const cssGenerators: Record<string, CssGenerator | null> = {};

export const getCssGeneratorByBlockType = async (blockType: string): Promise<CssGenerator | null> => {
    if (cssGenerators[blockType] !== undefined) {
        return cssGenerators[blockType];
    }
    const generatorModule = await import(
        /* @vite-ignore */ `/src/components/builder/blocks/${blockType}/templates/cssGenerator.ts`
    );
    cssGenerators[blockType] = generatorModule.generateCss;
    return generatorModule.generateCss;
};

const uniqueCssCache = new Map<string, string>();

export const generateUniqueCss = async (
    blockId: string,
    block: Auto_BlockData,
    device: 'desktop' | 'mobile',
): Promise<string> => {
    const cacheKey = JSON.stringify({
        type: block.type,
        device,
        configs: block.configs,
        bpConfigs: block.bpConfigs?.[device],
    });

    if (uniqueCssCache.has(cacheKey)) {
        const cachedCss = uniqueCssCache.get(cacheKey)!;
        const blockClassName = getBlockClassName(blockId, block.type);
        const regex = new RegExp(`atk-${block.type}-[^\\s{]+`, 'g');
        return cachedCss.replace(regex, blockClassName);
    }

    const cssGenerator = await getCssGeneratorByBlockType(block.type);
    if (!cssGenerator) return '';

    const css = cssGenerator(blockId, block, device);
    uniqueCssCache.set(cacheKey, css);

    return css;
};
