import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsAnimation } from '@/components/builder/settings/SettingsAnimation';
import { SettingsDisplay } from '@/components/builder/settings/SettingsDisplay';
import { SettingsDimension } from '@/components/builder/settings/SettingsDimension';
import { SettingsCustomCSS } from '@/components/builder/settings/SettingsCustomCSS';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BaseToggle } from '@/components/builder/base/BaseToggle';
import { Events } from './components/Events';
// import { Position } from './components/Position';
import { Box } from '@shopify/polaris';

const DEFAULT_PATH_ANIMATION = 'animation';
// const DEFAULT_PATH_POSITION = 'position';
const DEFAULT_PATH_EVENTS = 'events';

export const Advanced = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    return (
        <BaseHasBorderLayout>
            <BaseCollapse label="Size">
                <SettingsDimension blockId={selectedBlockId} isUpdateConfigs={false} />
            </BaseCollapse>

            <BaseCollapse label="Display on">
                <Box paddingBlockStart="300">
                    <SettingsDisplay blockId={selectedBlockId} />
                </Box>
            </BaseCollapse>

            <Events blockId={selectedBlockId} path={DEFAULT_PATH_EVENTS} label="Events" isUpdateConfigs />
            <BaseCollapse label="Animation">
                <SettingsAnimation path={DEFAULT_PATH_ANIMATION} blockId={selectedBlockId} isUpdateConfigs />
            </BaseCollapse>

            {/* <Position blockId={selectedBlockId} path={DEFAULT_PATH_POSITION} label="Position" isUpdateConfigs={false} /> */}

            <BaseCollapse
                label="Custom CSS"
                isUpdateToggle
                blockId={selectedBlockId}
                path="customCSS.enable"
                isUpdateConfigs
                labelContent={(open, setOpen) => <BaseToggle valueData={open} onChange={() => setOpen?.(!open)} />}
            >
                <SettingsCustomCSS blockId={selectedBlockId} isUpdateConfigs path="customCSS" label="Custom CSS" />
            </BaseCollapse>
        </BaseHasBorderLayout>
    );
};
