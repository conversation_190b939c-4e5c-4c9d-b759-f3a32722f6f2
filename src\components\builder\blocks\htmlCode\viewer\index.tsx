import { FC } from 'react';
import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { ReactComponent as CodeOutlineIcon } from '@/assets/svgs/code-outline.svg';
import { DATA_SET_ATK_DISPLAY_ON_MOBILE } from '@/components/builder/constants/constants';
import { DATA_SET_ATK_DISPLAY_ON_DESKTOP } from '@/components/builder/constants/constants';

export const HtmlCodeViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            bpConfigs={bpConfigs}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            configs={configs}
        >
            {configs.content ? (
                <div
                    style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        pointerEvents: 'none',
                        backgroundColor: 'transparent',
                        fontFamily: 'Inter',
                        color: '#000',
                    }}
                    {...(currentDevice === 'desktop'
                        ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                        : {})}
                    {...(currentDevice === 'mobile'
                        ? { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile }
                        : {})}
                    dangerouslySetInnerHTML={{ __html: configs?.content as string }}
                />
            ) : (
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        rowGap: '0.75rem',
                        width: '100%',
                        height: '100%',
                        backgroundColor: '#f5f7f7',
                        color: '#000',
                        fontFamily: 'Inter',
                    }}
                    {...(currentDevice === 'desktop'
                        ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                        : {})}
                    {...(currentDevice === 'mobile'
                        ? { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile }
                        : {})}
                >
                    <CodeOutlineIcon style={{ width: '2.5rem', height: '2.5rem' }} />
                    <div style={{ fontSize: '18px', textAlign: 'center' }}>HTML Code</div>
                </div>
            )}
        </BlockViewer>
    );
};
