import { useState } from 'react';

interface UseUncontrolledInput<T> {
    value?: T;
    defaultValue?: T;
    finalValue?: T;
    onChange?(value: T): void;
}

export function useUncontrolled<T>({
    value,
    defaultValue,
    finalValue,
    onChange = () => {},
}: UseUncontrolledInput<T>): [T, (value: T) => void, boolean] {
    const [uncontrolledValue, setUncontrolledValue] = useState(defaultValue !== undefined ? defaultValue : finalValue);

    const handleUncontrolledChange = (val: T) => {
        setUncontrolledValue(val);
        onChange?.(val);
    };

    if (value !== undefined) {
        return [value as T, onChange, true];
    }

    return [uncontrolledValue as T, handleUncontrolledChange, false];
}
