.switch-tabs__container {
    position: relative;
    flex-grow: 1;
    max-width: 100%;
    &--list {
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 140px;
        border-radius: var(--p-border-radius-200);
        padding: var(--p-space-050) 5px;
        background-color: var(--p-color-bg-fill-secondary);
        gap: var(--p-space-150);
        margin: 0;
        list-style: none;
        &--button {
            border: none;
            background: transparent;
            outline: none;
            cursor: pointer;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: var(--p-border-radius-200);
            &.active {
                background-color: #fff;
                box-shadow: var(--p-shadow-button);
            }
            &:hover {
                background-color: #fff;
            }
        }
    }

    &--full-width {
        max-width: 100%;
        li {
            flex: 1;
        }
        .switch-tabs__container--list--button {
            width: 100%;
            padding: 4px 5px;
        }
    }
}
