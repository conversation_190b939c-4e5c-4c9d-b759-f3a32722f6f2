import { FC } from 'react';
import { FormSettings } from '@/components/builder/blocks/form/types';
import { BlockToolbar } from '@/components/builder/BlockToolbar';

interface FormUnderlinedProps {
    option: FormSettings;
}

export const FormUnderlined: FC<FormUnderlinedProps> = ({ option }) => {
    return (
        <BlockToolbar
            {...option}
            id={option.id}
            configs={option.configs as unknown as Record<string, unknown>}
            key={option.id}
            style={{
                width: '100%',
                height: '100%',
                background: '#FAFBFB',
                borderRadius: '8px',
                border: '1px solid #EBEBEB',
                cursor: 'move',
                display: 'flex',
                padding: '9px 13px',
                justifyContent: 'center',
                flexDirection: 'column',
                alignItems: 'center',
            }}
        >
            <div
                css={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'center',
                    gap: '10px',
                    pointerEvents: 'none',
                }}
            >
                <input
                    type="text"
                    css={{
                        width: '100%',
                        height: '32px',
                        border: 'none',
                        borderBottom: '1px solid #616161',
                    }}
                />
                <input
                    type="text"
                    css={{
                        width: '100%',
                        height: '32px',
                        border: 'none',
                        borderBottom: '1px solid #616161',
                    }}
                />
                <div
                    css={{
                        float: 'right',
                        height: '32px',
                        background: '#616161',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '8px',
                        color: 'white',
                        padding: '0 10px',
                    }}
                >
                    Submit
                </div>
            </div>
        </BlockToolbar>
    );
};
