/* eslint-disable @typescript-eslint/no-explicit-any */
import { DATA_SET_AUTO_ID, DATA_SET_AUTO_ID_INNER, useBlockStore } from '@giaminhautoketing/auto-builder';
import { CssPropertyDefinition } from '@giaminhautoketing/auto-builder/dist/types/lib/blockSchemas';
import { generateBoxShadow, generateTextShadow } from '@/utils';
import { SettingsShadowData } from '@/components/builder/settings/SettingsShadow/types';
import {
    generateBackground,
    generateBorder,
    generateTypography,
    transformLabelToTypography,
    transformInputToTypography,
    transformPlaceholderToTypography,
    generateSubmitButtonStyles,
    generateButtonArrangement,
    generateGeneralFormVariable,
    generateSpacing,
    generateFormFieldVariable,
    generateSizeFormFieldVariable,
    generateFormAlignment,
    generateContentStyles,
    generateButtonSpace,
    generateAlignment,
} from '@/components/builder/data/utils/CssGenerator/utils';
import { Background, type Border } from '@/components/builder/data/utils/CssGenerator/types';
import {
    DATA_SET_FORM_FIELD_LABEL,
    DATA_SET_FORM_SUBMIT,
    DATA_SET_FORM_SUBMIT_WRAP,
    DATA_SET_FORM_INPUT,
    DATA_SET_FORM_TITLE,
    DATA_SET_FORM_CHECKBOX_LABEL,
    DATA_SET_FORM_RADIO_LABEL,
    DATA_SET_FORM_CSS_VARIABLE,
} from '@/components/builder/blocks/form/constants';
import { DATA_SET_BUTTON_TEXT } from '@/components/builder/blocks/button/constants';
import {
    DATA_SET_AUTO_ID_BREADCRUMB_SPACING,
    DATA_SET_AUTO_ID_SPACE_BETWEEN_TEXT_AND_ICON,
} from '../blocks/breadcrumb/constants';
import {
    DATA_SET_TABS_CONTENT,
    DATA_SET_TABS_NAV_ITEM,
    DATA_SET_TABS_NAV_LIST,
} from '@/components/builder/blocks/tabs/constants';
import {
    getStylesTabContainer,
    getStyleTabContent,
    getStyleTabHover,
    getStyleTabNormal,
    getStyleTabSelected,
} from '@/components/builder/blocks/tabs/utils/helper';

const blockTypeSelectors: Record<string, Record<string, string | string[] | ((blockId: string) => string)>> = {
    section: {
        innerDisplay: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        maxWidth: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        height: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        gridTemplateRows: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        gridTemplateColumns: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        mt: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        mb: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        ml: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        mr: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        pt: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        pb: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        pl: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        pr: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
    },
    shape: {
        background: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
    },
    button: {
        buttonsSpace: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        content: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        typography: (blockId) => ` [${DATA_SET_BUTTON_TEXT}="${blockId}"]`,
        borderButton: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        backgroundButton: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        boxShadow: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        height: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        width: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
    },

    form: {
        // forms
        backgroundForm: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"],`,
        formBorder: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        formHeight: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        boxShadow: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        generalForm: (blockId) => `[${DATA_SET_FORM_CSS_VARIABLE}="${blockId}"]`,
        form: (blockId) => `[${DATA_SET_FORM_CSS_VARIABLE}="${blockId}"]`,

        //fields
        formTitle: (blockId) => `[${DATA_SET_FORM_TITLE}="${blockId}"]`,
        fieldsInput: (blockId) =>
            `[${DATA_SET_FORM_INPUT}="${blockId}"],[${DATA_SET_FORM_CHECKBOX_LABEL}="${blockId}"],[${DATA_SET_FORM_RADIO_LABEL}="${blockId}"],`,

        fieldsSpacing: (blockId) => `[${DATA_SET_FORM_INPUT}="${blockId}"]`,
        fieldsStyles: (blockId) => `[${DATA_SET_FORM_INPUT}="${blockId}"]`,
        fieldsPlaceholder: (blockId) => `[${DATA_SET_FORM_INPUT}="${blockId}"]::-webkit-input-placeholder`,
        fieldsLabel: (blockId) => `[${DATA_SET_FORM_FIELD_LABEL}="${blockId}"]`,
        fieldSizes: (blockId) => `[${DATA_SET_FORM_CSS_VARIABLE}="${blockId}"]`,
        formAlignment: (blockId) => `[${DATA_SET_AUTO_ID}="${blockId}"]`,
        // button
        submitButton: (blockId) => `[${DATA_SET_FORM_SUBMIT}="${blockId}"]`,
        arrangement: (blockId) => `[${DATA_SET_FORM_SUBMIT_WRAP}="${blockId}"]`,
    },
    breadcrumb: {
        typography: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        backgroundBreadcrumb: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        borderBreadcrumb: (blockId) => `[${DATA_SET_AUTO_ID_INNER}="${blockId}"]`,
        spaceBetweenTextAndIcon: (blockId) => `[${DATA_SET_AUTO_ID_SPACE_BETWEEN_TEXT_AND_ICON}="${blockId}"]`,
        breadcrumbAlignment: (blockId) => `[${DATA_SET_AUTO_ID}="${blockId}"]`,
        breadcrumbSpacing: (blockId) => `[${DATA_SET_AUTO_ID_BREADCRUMB_SPACING}="${blockId}"]`,
    },
    tabs: {
        tabContainer: (blockId) => `[${DATA_SET_TABS_NAV_LIST}="${blockId}"]`,
        containers: (blockId) => `[${DATA_SET_TABS_CONTENT}="${blockId}"]`,
        tabNormal: (blockId) => `[${DATA_SET_TABS_NAV_ITEM}="${blockId}"]`,
        tabHover: (blockId) => `.tabs-nav-item[${DATA_SET_TABS_NAV_ITEM}="${blockId}"]:hover`,
        tabSelected: (blockId) => `[${DATA_SET_TABS_NAV_ITEM}="${blockId}"].selected`,
    },
};

function getBlockSelector(blockId: string, property: string): string | string[] {
    const state = useBlockStore.getState();

    if (state?.blocks?.[blockId]) {
        const blockType = state.blocks[blockId].type as string;

        const selector = blockTypeSelectors[blockType]?.[property];

        if (typeof selector === 'function') {
            return selector(blockId);
        }
        if (selector) {
            return selector;
        }
    }
    return `[${DATA_SET_AUTO_ID}="${blockId}"]`;
}

export const formCssPropertyDefinitions: Record<string, CssPropertyDefinition> = {
    fieldsLabel: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateTypography(transformLabelToTypography(value));
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldsLabel'),
    },
    fieldsInput: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateTypography(transformInputToTypography(value));
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldsInput'),
    },
    fieldsSpacing: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateSpacing(value.padding, 'padding', '--form-fields-spacing');
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldsSpacing'),
    },
    fieldsPlaceholder: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateTypography(transformPlaceholderToTypography(value));
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldsPlaceholder'),
    },
    titleForm: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return [
                    generateTypography(value, '--form-title-letter-spacing', '--form-title-line-height'),
                    generateSpacing(value.titleSpacing.padding, 'padding', '--form-title-spacing'),
                    generateSpacing(value.titleSpacing.margin, 'margin', '--form-title-spacing'),
                ]
                    .filter(Boolean)
                    .join(' ');
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'formTitle'),
    },
    buttonSubmit: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateSubmitButtonStyles(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'submitButton'),
    },
    formBorder: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBorder(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'formBorder'),
    },
    fieldsStyles: {
        property: (value: any) => {
            if (typeof value !== 'object' || !value) return null;
            const { background, border } = value;
            return [generateBackground(background), generateBorder(border)].filter(Boolean).join(' ');
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldsStyles'),
    },
    generalForm: {
        property: (value: any) => {
            if (typeof value !== 'object' || !value) return null;
            return generateGeneralFormVariable(value);
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'generalForm'),
    },

    formHeight: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return `height: auto !important;`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'formHeight'),
    },
    arrangement: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateButtonArrangement(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'arrangement'),
    },
    backgroundForm: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBackground(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'background'),
    },

    fieldSizes: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateSizeFormFieldVariable(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'fieldSizes'),
    },
    form: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateFormFieldVariable(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'form'),
    },
    formAlignment: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateFormAlignment(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'formAlignment'),
    },
};

export const buttonCssPropertyDefinitions: Record<string, CssPropertyDefinition> = {
    typography: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateTypography(value, '--atk-button-letter-spacing', '--atk-button-line-height');
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'typography'),
    },
    borderButton: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBorder(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'borderButton'),
    },
    backgroundButton: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBackground(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'backgroundButton'),
    },
    height: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return `height: ${value.val}${value.unit};`;
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'height'),
    },
    width: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return `width: ${value.val}${value.unit};`;
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'width'),
    },
    content: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateContentStyles(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'content'),
    },
    buttonsSpace: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateButtonSpace(value, '--atk-button-spacing');
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'buttonsSpace'),
    },
};

export const breadcrumbCssPropertyDefinitions: Record<string, CssPropertyDefinition> = {
    // typography: {
    //     property: (value: any) => {
    //         if (value && typeof value === 'object') {
    //             return generateTypography(value);
    //         }
    //         return null;
    //     },
    //     valueType: 'object',
    //     selector: (blockId: string) => getBlockSelector(blockId, 'typography'),
    // },

    backgroundBreadcrumb: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBackground(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'backgroundBreadcrumb'),
    },
    borderBreadcrumb: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateBorder(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'borderBreadcrumb'),
    },
    spaceBetweenTextAndIcon: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return `margin: 0 ${value.val}${value.unit};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'spaceBetweenTextAndIcon'),
    },
    breadcrumbAlignment: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateFormAlignment(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'breadcrumbAlignment'),
    },
    breadcrumbSpacing: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateSpacing(value.padding, 'padding') + generateSpacing(value.margin, 'margin');
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'breadcrumbSpacing'),
    },
};

export const tabsCssPropertyDefinitions: Record<string, CssPropertyDefinition> = {
    tabContainer: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return getStylesTabContainer(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'tabContainer'),
    },
    containers: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return getStyleTabContent(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'containers'),
    },
    tabNormal: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return getStyleTabNormal(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'tabNormal'),
    },
    tabHover: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return getStyleTabHover(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'tabHover'),
    },
    tabSelected: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return getStyleTabSelected(value);
            }
            return null;
        },
        valueType: 'object',
        selector: (blockId: string) => getBlockSelector(blockId, 'tabSelected'),
    },
};

export const cssPropertyDefinitions: Record<string, CssPropertyDefinition> = {
    display: {
        property: 'display',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'display'),
    },

    alignment: {
        property: (value: any) => {
            if (value && typeof value === 'object') {
                return generateAlignment(value);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId: string) => getBlockSelector(blockId, 'formAlignment'),
    },

    innerDisplay: {
        property: 'display',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'innerDisplay'),
    },

    gridColStart: {
        property: 'grid-column-start',
        valueType: 'number',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridColStart'),
    },

    gridColEnd: {
        property: 'grid-column-end',
        valueType: 'number',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridColEnd'),
    },

    gridRowStart: {
        property: 'grid-row-start',
        valueType: 'number',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridRowStart'),
    },

    gridRowEnd: {
        property: 'grid-row-end',
        valueType: 'number',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridRowEnd'),
    },

    justifySelf: {
        property: (property) => {
            if (property !== 'none') return `justify-self: ${property};`;
            return null;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'justifySelf'),
    },

    alignSelf: {
        property: (property) => {
            if (property !== 'none') return `align-self: ${property};`;
            return null;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'alignSelf'),
    },

    zIndex: {
        property: 'z-index',
        valueType: 'number',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'zIndex'),
    },

    position: {
        property: (value) => {
            if (value && typeof value === 'string') {
                if (value === 'default') {
                    return `position: static;`;
                }
                return `position: ${value};`;
            }
            return null;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'position'),
    },

    background: {
        property: (value) => {
            if (value && typeof value === 'object') {
                return generateBackground(value as Background);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'background'),
    },

    color: {
        property: 'color',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'color'),
    },

    fontSize: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `font-size: auto;`;
                } else if (typedValue.val !== '0') {
                    return `font-size: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'fontSize'),
    },
    fontWeight: {
        property: 'font-weight',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'fontWeight'),
    },
    fontStyle: {
        property: (value) => {
            if (value === 'default') return null;
            return `font-style: ${value};`;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'fontStyle'),
    },
    textAlign: {
        property: 'text-align',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'textAlign'),
    },
    textDecoration: {
        property: 'text-decoration',
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'textDecoration'),
    },
    fontFamily: {
        property: (value) => {
            if (!value) return null;
            return `font-family: ${value};`;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'fontFamily'),
    },
    textTransform: {
        property: (value) => {
            if (value === 'default') return null;
            return `text-transform: ${value};`;
        },
        valueType: 'string',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'textTransform'),
    },

    width: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `width: ${typedValue.val}${typedValue.unit};`;
            }
            if (typeof value === 'string') return `width: ${value};`;
            return null;
        },
        valueType: 'object',
        inheritable: true,
    },
    textShadow: {
        property: (value) => {
            if (value) {
                return `text-shadow: ${generateTextShadow(value as SettingsShadowData)};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'textShadow'),
    },
    boxShadow: {
        property: (value) => {
            if (value) {
                return `box-shadow: ${generateBoxShadow(value as SettingsShadowData)};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'boxShadow'),
    },

    maxWidth: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `max-width: ${typedValue.val}${typedValue.unit};`;
            }
            if (typeof value === 'string') return `max-width: ${value};`;
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'maxWidth'),
    },

    height: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `height: ${typedValue.val}${typedValue.unit};`;
            }
            if (typeof value === 'string') return `height: ${value};`;
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'height'),
    },

    gridTemplateRows: {
        property: (value) => {
            if (value && Array.isArray(value)) {
                return `grid-template-rows: ${value.map((item) => `${item.val}${item.unit}`).join(' ')};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridTemplateRows'),
    },

    gridTemplateColumns: {
        property: (value) => {
            if (value && Array.isArray(value)) {
                return `grid-template-columns: ${value.map((item) => `${item.val}${item.unit}`).join(' ')};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'gridTemplateColumns'),
    },

    mt: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `margin-top: auto;`;
                } else if (typedValue.val !== '0') {
                    return `margin-top: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'mt'),
    },

    mb: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `margin-bottom: auto;`;
                } else if (typedValue.val !== '0') {
                    return `margin-bottom: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'mb'),
    },

    ml: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `margin-left: auto;`;
                } else if (typedValue.val !== '0') {
                    return `margin-left: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'ml'),
    },

    mr: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `margin-right: auto;`;
                } else if (typedValue.val !== '0') {
                    return `margin-right: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'mr'),
    },

    pt: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `padding-top: auto;`;
                } else if (typedValue.val !== '0') {
                    return `padding-top: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'pt'),
    },

    pb: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `padding-bottom: auto;`;
                } else if (typedValue.val !== '0') {
                    return `padding-bottom: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'pb'),
    },

    pl: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                if (typedValue.val === 'auto') {
                    return `padding-left: auto;`;
                } else if (typedValue.val !== '0') {
                    return `padding-left: ${typedValue.val}${typedValue.unit};`;
                }
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'pl'),
    },

    pr: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `padding-right: ${typedValue.val}${typedValue.unit};`;
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (blockId) => getBlockSelector(blockId, 'pr'),
    },
    border: {
        property: (value) => {
            if (value && typeof value === 'object') {
                return generateBorder(value as Border);
            }
            return null;
        },
        valueType: 'object',
        inheritable: true,
        selector: (id) => getBlockSelector(id, 'border'),
    },

    lineHeight: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `line-height: ${typedValue.val}${typedValue.unit};`;
            }
            if (typeof value === 'string') return `line-height: ${value};`;
            return null;
        },
        valueType: 'object',
        inheritable: true,
    },
    letterSpacing: {
        property: (value) => {
            if (value && typeof value === 'object' && 'val' in value && 'unit' in value) {
                const typedValue = value as { val: string; unit: string };
                return `letter-spacing: ${typedValue.val}${typedValue.unit};`;
            }
            if (typeof value === 'string') return `letter-spacing: ${value};`;
            return null;
        },
        valueType: 'object',
        inheritable: true,
    },
    ...formCssPropertyDefinitions,
    ...buttonCssPropertyDefinitions,
    ...breadcrumbCssPropertyDefinitions,
    ...tabsCssPropertyDefinitions,
};
