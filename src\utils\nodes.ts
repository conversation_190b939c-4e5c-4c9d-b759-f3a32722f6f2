/* eslint-disable @typescript-eslint/no-explicit-any */
import { createElement, ComponentType, ElementType, ReactNode, ComponentProps } from 'react';

export type NodeItem<T = any> = {
    component: ComponentType<T> | ElementType;
    props: T;
    _id: string;
};

export function createNode<C extends ComponentType<any> | ElementType>(
    component: C,
    props: ComponentProps<C>,
): NodeItem<ComponentProps<C>> {
    const id = Math.random().toString(36).substring(2, 9);
    return {
        component,
        props,
        _id: id,
    };
}

export const renderNodes = (items: NodeItem[]): ReactNode[] => {
    return items.map((item, index) => {
        const Component = item.component;
        const key = `${item._id}-${index}`;
        const props = { key, ...item.props };
        return createElement(Component, props);
    });
};
