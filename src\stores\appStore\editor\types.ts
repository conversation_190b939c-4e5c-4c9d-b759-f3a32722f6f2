import { FormField } from '@/components/builder/blocks/form/types';

export interface EditorState {
    zoomLevel: number;
    screenWidth: number;
    currentDevice: 'desktop' | 'mobile';
    isOpenModalShape: {
        isOpen: boolean;
        tabs?: number;
    };
    selectedFormField: FormField | null;
    isAddNewFormField: boolean;
    tempTab: {
        [blockId: string]: {
            activeId: string;
        };
    };
}

export interface EditorActions {
    setZoomLevel: (level: number) => void;
    setScreenWidth: (width: number) => void;
    setDeviceType: (deviceType: 'desktop' | 'mobile') => void;
    zoomIn: () => void;
    zoomOut: () => void;
    resetZoom: () => void;
    setIsOpenModalShape: (isOpen: boolean, tabs?: number) => void;
    setSelectedFormField: (formField: FormField) => void;
    setIsAddNewFormField: (isAddNewFormField: boolean) => void;
    setTempTab: (blockId: string, activeId: string) => void;
}
