import React, { useEffect, useState } from 'react';
import { useAppStore } from '@/stores/appStore';
import { apiAddress } from '@/configs/apiAddress';

interface AuthProviderProps {
    children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [isInitialized, setIsInitialized] = useState(false);
    const { setToken, setIsAuthenticated } = useAppStore();

    useEffect(() => {
        const initializeAuth = async () => {
            try {
                const response = await fetch(apiAddress.login, {
                    method: 'POST',
                });
                const data = await response.json();

                setToken(data.data);
                setIsAuthenticated(true);
            } catch (error) {
                console.error('Authentication failed:', error);
                setIsAuthenticated(false);
            } finally {
                setIsInitialized(true);
            }
        };

        initializeAuth();
    }, [setToken, setIsAuthenticated]);

    if (!isInitialized) {
        return null;
    }

    return <>{children}</>;
};
