.DirectionButton {
    /* stylelint-disable-next-line -- This mixin cannot be replaced with a single token */
    @mixin focus-ring wide;
    border-radius: var(--p-border-radius-200);
    padding: var(--p-space-100) var(--p-space-300) var(--p-space-100) var(--p-space-200);
    display: grid;
    align-items: center;
    grid-template-columns: auto 1fr;
    gap: var(--p-space-050);
    cursor: pointer;
    width: 100%;
    border: none;
    background: none;
    text-align: left;

    &:hover {
        background-color: var(--p-color-bg-fill-transparent-hover);
    }

    + .DirectionButton {
        margin-top: var(--p-space-100);
    }

    &:focus-visible {
        outline: 0;
        /* stylelint-disable-next-line -- generated by polaris-migrator DO NOT COPY */
        @mixin focus-ring wide, 0, focused;
    }
}

.DirectionButton-active {
    color: var(--p-color-text);
    background: var(--p-color-bg-fill-transparent-active);
}
