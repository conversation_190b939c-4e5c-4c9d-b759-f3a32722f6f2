import { SkeletonBodyText, Card, SkeletonPage, BlockStack, SkeletonDisplayText } from '@shopify/polaris';
import { FC } from 'react';

export const SkeletonAccountSetting: FC = () => {
    return (
        <SkeletonPage>
            <BlockStack gap="500">
                <Card>
                    <BlockStack gap="400">
                        <SkeletonDisplayText size="small" />
                        <SkeletonBodyText />
                    </BlockStack>
                </Card>
                <Card>
                    <BlockStack gap="400">
                        <SkeletonDisplayText size="small" />
                        <SkeletonBodyText />
                    </BlockStack>
                </Card>
                <Card>
                    <BlockStack gap="400">
                        <SkeletonDisplayText size="small" />
                        <SkeletonBodyText />
                    </BlockStack>
                </Card>
                <Card>
                    <BlockStack gap="400">
                        <SkeletonDisplayText size="small" />
                        <SkeletonBodyText />
                    </BlockStack>
                </Card>
                <Card>
                    <BlockStack gap="400">
                        <SkeletonDisplayText size="small" />
                        <SkeletonBodyText />
                    </BlockStack>
                </Card>
            </BlockStack>
        </SkeletonPage>
    );
};
