import { FC } from 'react';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { useAppStore } from '@/stores/appStore';
import { CreatePageModal } from './CreatePageModal';
import { useRequestStatus } from '@/pages/BuilderPage/hooks/editor/useRequestStatus';

export const BuilderModal: FC = () => {
    const setPageCreationStatus = useAppStore((state) => state.setPageCreationStatus);
    const pageCreationStatus = useAppStore((state) => state.pageCreationStatus);
    const hasEdits = useAppStore((state) => state.hasEdits);
    const setHasEdits = useAppStore((state) => state.setHasEdits);
    const modalChannel = new BroadcastChannel('builder-modal');
    const { isLoading } = useRequestStatus();

    const handleCloseBuilder = () => {
        localStorage.removeItem('templateId');
        setPageCreationStatus('idle');
        setHasEdits(false);
        shopify.modal.hide('builder-modal');
    };

    const handleOpenCreateModal = () => {
        shopify.modal.show('builder-modal-create');
    };

    const handleSave = () => {
        modalChannel.postMessage('Save');
    };

    const handlePublish = () => {
        modalChannel.postMessage('Publish');
    };

    const handleDiscardChanges = () => {
        modalChannel.postMessage('Discard');
    };

    const handleActionBuilder = () => {
        if (pageCreationStatus === 'starting') return handleOpenCreateModal();
        if (hasEdits) return handleSave();
        return handlePublish();
    };
    const buttonLabel = pageCreationStatus === 'starting' ? 'Create' : hasEdits ? 'Save' : 'Publish';
    return (
        <>
            <Modal id="builder-modal" variant="max" src="/builder" onHide={handleCloseBuilder}>
                <TitleBar title="Autoketing Builder">
                    {pageCreationStatus === 'starting' && <button onClick={handleCloseBuilder}>Discard</button>}
                    {hasEdits && <button onClick={handleDiscardChanges}>Discard</button>}
                    <button variant="primary" onClick={handleActionBuilder}>
                        {buttonLabel}
                    </button>
                </TitleBar>
            </Modal>
            <CreatePageModal modalChannel={modalChannel} isLoading={isLoading} />
        </>
    );
};
