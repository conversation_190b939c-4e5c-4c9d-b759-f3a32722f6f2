/* eslint-disable react-hooks/exhaustive-deps */
import { useMemo, useState } from 'react';
import clsx from 'clsx';
import { useMutation, useQuery, useScrollToLoadmore } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
import { useMediaManager } from '../../useMediaManager';
import { EmptyState, MediaGrid } from '../../components';
import { MediaType, SystemMeidaResponse } from '../../types';

export const Upload = () => {
    const [items, setItems] = useState<MediaType[]>([]);
    const page = useMediaManager((state) => state.page);
    const [hasMore, setHasMore] = useState(true);
    const sortBy = useMediaManager((state) => state.sortBy);
    const endCursor = useMediaManager((state) => state.endCursor);
    const forceUpdate = useMediaManager((state) => state.forceUpdate);
    const setPage = useMediaManager((state) => state.setPage);
    const setEndCursor = useMediaManager((state) => state.setEndCursor);

    const limit = 20;

    const url = useMemo(() => {
        return clsx(
            apiAddress.media.index,
            !endCursor && `?currentPage=${page}`,
            endCursor && `?nextPage=${endCursor}`,
            `&perPage=${limit}`,
            sortBy && sortBy !== 'all' && `&mediaType=${sortBy}`,
        ).replace(/\s+/g, '');
    }, [page, limit, sortBy]);

    const { isLoading: loading, refetch } = useQuery<SystemMeidaResponse>({
        url,
        method: 'GET',
        onSuccess(data) {
            const { edges, pageInfo } = data.result.data;
            setItems(page === 1 ? edges : (prev) => [...prev, ...edges]);
            setHasMore(pageInfo.hasNextPage);
            setEndCursor(pageInfo.endCursor || '');
        },
        extraDeps: forceUpdate,
    });

    const mutation = useMutation({
        url: apiAddress.media.index,
        method: 'POST',
        timeout: 50000,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        onMutate: () => {
            shopify.toast.show('Uploading...');
        },
        onSuccess: () => {
            shopify.toast.show('Uploaded successfully');
            refetch?.();
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
    });

    const loadMoreItems = () => {
        if (loading || !hasMore) return;
        setPage(page + 1);
    };

    const { containerRef } = useScrollToLoadmore({
        hasMore,
        loading,
        loadmore: loadMoreItems,
        resetDependencies: [sortBy],
    });

    return (
        <MediaGrid
            items={items}
            loading={loading}
            containerRef={containerRef}
            renderEmptyState={
                <EmptyState
                    onDrop={(acceptedFiles) => {
                        const formData = new FormData();
                        formData.append('mediaType', sortBy === 'all' ? 'image' : sortBy);
                        acceptedFiles.forEach((file) => {
                            formData.append('files[]', file);
                        });
                        mutation.mutate(formData);
                    }}
                    accept={sortBy === 'all' || sortBy === 'image' ? 'image/*' : 'video/*'}
                />
            }
        />
    );
};
