import styled from '@emotion/styled';

export const InteragratedContainer = styled.div<{ isHidden: boolean }>`
    display: ${(props) => (props.isHidden ? 'none' : 'block')};
    .Polaris-ShadowBevel > .Polaris-Box {
        display: flex;
        padding: 20px 16px;
        justify-content: space-between;
        column-gap: 12px;
        @media screen and (max-width: 679px) {
            flex-direction: column;
        }
    }
`;
export const InteragratedLeft = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 20px;
    .Polaris-ButtonGroup {
        column-gap: 12px;
        margin-left: 0;
        .Polaris-ButtonGroup__Item {
            margin-left: 0;
            .Polaris-Button {
                min-width: 92px;
                padding-inline: 11px;
            }
        }

        @media screen and (max-width: 679px) {
            position: absolute;
            bottom: 16px;
            z-index: 1;
        }
    }
`;
export const InteragratedLeftHeader = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    .Polaris-Text--headingSm {
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
    }
    .Polaris-Text--subdued {
        font-size: 13px;
        font-weight: 450;
        line-height: 20px;
    }
`;
export const InteragratedLeftContent = styled.div`
    display: flex;
    align-items: center;
    column-gap: 16px;
    margin-bottom: 4px;
    img {
        width: 80px;
        height: 80px;
    }
    .Polaris-Box {
        max-width: 333px;
        .Polaris-Text--headingSm {
            font-size: 12px;
            font-weight: 700;
            line-height: 16px;
            margin-bottom: 12px;
        }
    }
`;
export const Rating = styled.div`
    .Polaris-Text--subdued {
        font-size: 12px;
        font-weight: 450;
        line-height: 16px;
        float: left;
        .Polaris-Icon {
            margin: inherit;
            min-width: 16px;
            height: 16px;
            color: #257e3f;
            float: left;
        }
    }
`;
export const InteragratedRight = styled.div`
    .Polaris-Pagination {
        visibility: hidden;
        position: absolute;
        right: 40px;
        bottom: 37px;
        z-index: 1;
        transition: all 0.3s;
        opacity: 0;
    }
    &:hover .Polaris-Pagination {
        visibility: visible;
        opacity: 1;
        @media screen and (max-width: 679px) {
            display: none;
        }
    }
    @media screen and (max-width: 679px) {
        margin-bottom: 45px;
    }
`;
export const InteragratedRightSlider = styled.div`
    .Polaris-Button {
        position: absolute;
        min-width: 20px;
        min-height: 20px;
        width: 20px;
        top: 16px;
        right: 16px;
        z-index: 1;
        padding: 0;
        border-radius: 6px;
    }
    .keen-slider {
        margin-top: 32px;
        margin-right: 16px;
        width: 280px;
        height: 155px;
        border-radius: 8px;
        @media screen and (max-width: 679px) {
            width: 100%;
            margin-top: 10px;
            height: auto;
            margin-right: 0;
        }
        .keen-slider__slide {
            img {
                width: 100%;
                height: 100%;
            }
        }
    }
`;
