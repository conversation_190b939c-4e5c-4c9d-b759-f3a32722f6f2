import { FC, useMemo } from 'react';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { getBlockProperty } from '@/utils/shared';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { SettingsToggle } from '@/components/builder/settings/SettingsToggle';
interface DisplayOnProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
}

const DEVICES = ['Desktop', 'Mobile'] as const;

export const DisplayOn: FC<DisplayOnProps> = ({ path, blockId, isUpdateConfigs, label }) => {
    const deviceSettings = useMemo(() => {
        return DEVICES.map((device) => {
            const value = getBlockProperty(`configs.${path}${device}`, blockId) || false;
            return {
                device,
                isActive: value,
                pathKey: `${path}${device}`,
                id: `display-${blockId}-on-${device.toLowerCase()}`,
            };
        });
    }, [path, blockId]);

    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BlockStack gap="300">
                    {deviceSettings.map(({ device, pathKey, id }) => (
                        <SettingsToggle
                            key={device}
                            isUpdateConfigs={isUpdateConfigs}
                            label={device}
                            path={pathKey}
                            blockId={blockId}
                            toggleProps={{
                                id: id,
                            }}
                        />
                    ))}
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
