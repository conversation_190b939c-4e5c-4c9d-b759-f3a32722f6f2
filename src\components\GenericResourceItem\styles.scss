.rsItem-wrapper {
    position: relative;
    & + div {
        border-top: var(--pc-resource-list-separator-border);
    }
    &:hover {
        .more-action-wrapper {
            display: block;
        }
    }
    .Polaris-ResourceItem > .Polaris-Box {
        padding: 17px 16px;
    }
    .Polaris-ResourceItem__CheckboxWrapper {
        overflow: hidden;
    }
}

.more-action-wrapper {
    display: none;
    position: absolute;
    top: 50%;
    right: 0;
    z-index: 50;
    transform: translate(-16px, -50%);

    &.show {
        display: block;
    }
}

.pane-wrapper {
    width: 210px;
    padding: 6px;
    .Polaris-ActionList__Item.Polaris-ActionList--default {
        padding: 6px 8px;
    }
}

.text-field-wrapper {
    .Polaris-TextField__Input {
        font-size: 13px;
        font-weight: 450;
        line-height: 20px;
    }
}
