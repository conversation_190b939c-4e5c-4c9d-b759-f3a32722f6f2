export enum VideoPlatform {
    YOUTUBE = 'youtube',
    VIMEO = 'vimeo',
    DAILYMOTION = 'dailymotion',
    FACEBOOK = 'facebook',
    DIRECT = 'direct',
    UNKNOWN = 'unknown',
}

export interface VideoSettings {
    showControls?: boolean;
    loop?: boolean;
    autoplay?: boolean;
    muted?: boolean;
}

export const checkVideoPlatform = (
    url: string,
): {
    platform: VideoPlatform;
    videoId: string | null;
    embedUrl: string | null;
} => {
    if (!url) {
        return { platform: VideoPlatform.UNKNOWN, videoId: null, embedUrl: null };
    }

    let urlObj: URL;
    try {
        urlObj = new URL(url);
    } catch {
        return { platform: VideoPlatform.UNKNOWN, videoId: null, embedUrl: null };
    }

    if (urlObj.hostname === 'youtu.be' || urlObj.hostname === 'www.youtube.com' || urlObj.hostname === 'youtube.com') {
        let videoId: string | null = null;

        if (urlObj.hostname === 'youtu.be') {
            videoId = urlObj.pathname.slice(1);
        } else {
            const searchParams = new URLSearchParams(urlObj.search);
            videoId = searchParams.get('v');
        }

        if (videoId) {
            return {
                platform: VideoPlatform.YOUTUBE,
                videoId,
                embedUrl: `https://www.youtube.com/embed/${videoId}`,
            };
        }
    }

    if (urlObj.hostname === 'vimeo.com' || urlObj.hostname === 'www.vimeo.com') {
        const videoId = urlObj.pathname.slice(1);
        if (videoId) {
            return {
                platform: VideoPlatform.VIMEO,
                videoId,
                embedUrl: `https://player.vimeo.com/video/${videoId}`,
            };
        }
    }

    if (
        urlObj.hostname === 'dailymotion.com' ||
        urlObj.hostname === 'www.dailymotion.com' ||
        urlObj.hostname === 'dai.ly'
    ) {
        let videoId: string | null = null;

        if (urlObj.hostname === 'dai.ly') {
            videoId = urlObj.pathname.slice(1);
        } else {
            const match = urlObj.pathname.match(/\/video\/([a-zA-Z0-9]+)/);
            if (match && match[1]) {
                videoId = match[1];
            }
        }

        if (videoId) {
            return {
                platform: VideoPlatform.DAILYMOTION,
                videoId,
                embedUrl: `https://www.dailymotion.com/embed/video/${videoId}`,
            };
        }
    }

    if (
        urlObj.hostname === 'facebook.com' ||
        urlObj.hostname === 'www.facebook.com' ||
        urlObj.hostname === 'fb.watch'
    ) {
        return {
            platform: VideoPlatform.FACEBOOK,
            videoId: null,
            embedUrl: null,
        };
    }

    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv'];
    if (videoExtensions.some((ext) => url.toLowerCase().endsWith(ext))) {
        return {
            platform: VideoPlatform.DIRECT,
            videoId: null,
            embedUrl: null,
        };
    }

    return { platform: VideoPlatform.UNKNOWN, videoId: null, embedUrl: null };
};

export const getVideoEmbedCode = (url: string, settings: VideoSettings = {}): string => {
    const { platform, embedUrl } = checkVideoPlatform(url);
    const { showControls = true, loop = false, autoplay = false, muted = false } = settings;

    if (!embedUrl && platform !== VideoPlatform.DIRECT && platform !== VideoPlatform.FACEBOOK) {
        return '';
    }

    const fbAttributes: string[] = [];
    if (platform === VideoPlatform.FACEBOOK) {
        if (autoplay) fbAttributes.push('data-autoplay="true"');
        if (showControls) fbAttributes.push('data-controls="true"');
    }

    switch (platform) {
        case VideoPlatform.YOUTUBE: {
            const youtubeUrl = new URL(embedUrl as string);
            if (autoplay) youtubeUrl.searchParams.append('autoplay', '1');
            if (loop) youtubeUrl.searchParams.append('loop', '1');
            if (muted) youtubeUrl.searchParams.append('mute', '1');
            if (!showControls) youtubeUrl.searchParams.append('controls', '0');

            return `<iframe width="100%" height="100%" src="${youtubeUrl.toString()}" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
        }

        case VideoPlatform.VIMEO: {
            const vimeoUrl = new URL(embedUrl as string);
            if (autoplay) vimeoUrl.searchParams.append('autoplay', '1');
            if (loop) vimeoUrl.searchParams.append('loop', '1');
            if (muted) vimeoUrl.searchParams.append('muted', '1');
            if (!showControls) vimeoUrl.searchParams.append('controls', '0');

            return `<iframe width="100%" height="100%" src="${vimeoUrl.toString()}" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>`;
        }

        case VideoPlatform.DAILYMOTION: {
            const dailymotionUrl = new URL(embedUrl as string);
            if (autoplay) dailymotionUrl.searchParams.append('autoplay', '1');
            if (muted) dailymotionUrl.searchParams.append('mute', '1');
            if (!showControls) dailymotionUrl.searchParams.append('controls', '0');

            return `<iframe width="100%" height="100%" src="${dailymotionUrl.toString()}" frameborder="0" allow="autoplay; fullscreen" allowfullscreen></iframe>`;
        }

        case VideoPlatform.FACEBOOK: {
            return `<div class="fb-video" data-href="${url}" data-width="100%" ${fbAttributes.join(
                ' ',
            )} data-show-text="false"></div>`;
        }

        case VideoPlatform.DIRECT: {
            const controlsAttr = showControls ? 'controls' : '';
            const loopAttr = loop ? 'loop' : '';
            const autoplayAttr = autoplay ? 'autoplay' : '';
            const mutedAttr = muted ? 'muted' : '';

            return `<video width="100%" height="100%" style="object-fit: cover;" ${controlsAttr} ${loopAttr} ${autoplayAttr} ${mutedAttr}><source src="${url}"></video>`;
        }

        default:
            return '';
    }
};
