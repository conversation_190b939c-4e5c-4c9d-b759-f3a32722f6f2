import { Auto_BlockToolbar, genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const image2: Auto_BlockToolbar = {
    id: genRandomBlockId(),
    cname: 'image',
    label: 'Image',
    type: 'image',
    configs: {
        displayOnDesktop: true,
        displayOnMobile: true,
        url: 'https://r2.nucuoimekong.com/wp-content/uploads/buc-anh-dep-can-bang-sang-tot-1.jpg',
        alt: 'Image',
    },
    bpConfigs: {
        desktop: {
            background: {
                type: 'color',
                color: '#6daeff',
                image: {
                    url: 'https://images.unsplash.com/photo-1746730341411-03b1f6b8f1f0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                    repeat: 'no',
                    position: 'center center',
                    attachment: 'fixed',
                    fill: 'cover',
                },
            },
            color: '#ffffff',
            width: { val: '300', unit: 'px' },
            height: { val: '180', unit: 'px' },
            border: {
                radius: {
                    'top-left': { val: '100', unit: 'px' },
                    'top-right': { val: '100', unit: 'px' },
                    'bottom-right': { val: '100', unit: 'px' },
                    'bottom-left': { val: '100', unit: 'px' },
                },
            },
        },
        mobile: {
            background: {
                type: 'color',
                color: '#6daeff',
                image: {
                    url: 'https://images.unsplash.com/photo-1746730341411-03b1f6b8f1f0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                    repeat: 'no',
                    position: 'center center',
                    attachment: 'fixed',
                    fill: 'cover',
                },
            },
            color: '#ffffff',
            width: { val: '300', unit: 'px' },
            height: { val: '180', unit: 'px' },
            border: {
                radius: {
                    'top-left': { val: '100', unit: 'px' },
                    'top-right': { val: '100', unit: 'px' },
                    'bottom-right': { val: '100', unit: 'px' },
                    'bottom-left': { val: '100', unit: 'px' },
                },
            },
        },
    },
    overlay: {
        desktop: {
            width: 300,
            height: 180,
        },
        mobile: {
            width: 300,
            height: 180,
        },
    },
};
