import { FC, useState } from 'react';
import { BlockStack, Box, InlineStack } from '@shopify/polaris';
import { ReactComponent as Lock } from '@/assets/svgs/Border/lock.svg';
import { ReactComponent as Unlock } from '@/assets/svgs/Border/unlock.svg';
import './style.scss';
import { BorderInput } from '../shared/BorderInput';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
interface BorderRadiusProps {
    isUpdateConfigs?: boolean;
    path: string;
    blockId: string;
}

export const BorderRadius: FC<BorderRadiusProps> = ({ isUpdateConfigs, path, blockId }) => {
    const [isRadiusLock, setIsRadiusLock] = useState(true);

    const getRadiusValues = (radius: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right') => {
        return isUpdateConfigs
            ? getBlockProperty(`configs.${path}.${radius}.val`, blockId)
            : getBlockBPProperty(`${path}.${radius}.val`, blockId);
    };
    const [activeSide, setActiveSide] = useState<'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | null>(
        null,
    );
    const handleFocus = (side: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right') => {
        setActiveSide(side);
    };
    const handleBlur = () => {
        setActiveSide(null);
    };
    return (
        <Box
            paddingBlock="400"
            paddingInline="200"
            borderRadius="200"
            borderColor="border"
            borderStyle="solid"
            borderWidth="025"
        >
            <div className="radius-setting">
                <BlockStack gap="300">
                    <InlineStack align="space-between" blockAlign="center">
                        <BorderInput
                            id="radius-top-left"
                            isActive={activeSide === 'top-left'}
                            path={`${path}.top-left`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isRadiusLock}
                            onFocus={() => handleFocus('top-left')}
                            onBlur={handleBlur}
                            type="radius"
                        />
                        <BorderInput
                            id="radius-top-right"
                            isActive={activeSide === 'top-right'}
                            path={`${path}.top-right`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isRadiusLock}
                            onFocus={() => handleFocus('top-right')}
                            onBlur={handleBlur}
                            type="radius"
                        />
                    </InlineStack>

                    <div className="radius-setting__diagram">
                        <div className="radius-setting__diagram-row">
                            <div
                                className="radius-setting__diagram-row--top-left"
                                style={{ borderTopLeftRadius: `${getRadiusValues('top-left')}px` }}
                            />
                            <div
                                className="radius-setting__diagram-row--top-right"
                                style={{ borderTopRightRadius: `${getRadiusValues('top-right')}px` }}
                            />
                        </div>
                        <div className="radius-setting__diagram-row radius-setting__diagram-row--center">
                            <button
                                className="radius-setting__button radius-setting__button--lock"
                                onClick={() => setIsRadiusLock(!isRadiusLock)}
                            >
                                {isRadiusLock ? <Lock /> : <Unlock />}
                            </button>
                        </div>
                        <div className="radius-setting__diagram-row">
                            <div
                                className="radius-setting__diagram-row--bottom-left"
                                style={{ borderBottomLeftRadius: `${getRadiusValues('bottom-left')}px` }}
                            />
                            <div
                                className="radius-setting__diagram-row--bottom-right"
                                style={{ borderBottomRightRadius: `${getRadiusValues('bottom-right')}px` }}
                            />
                        </div>
                    </div>

                    <InlineStack align="space-between" blockAlign="center">
                        <BorderInput
                            id="radius-bottom-left"
                            isActive={activeSide === 'bottom-left'}
                            path={`${path}.bottom-left`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isRadiusLock}
                            onFocus={() => handleFocus('bottom-left')}
                            onBlur={handleBlur}
                            type="radius"
                        />
                        <BorderInput
                            id="radius-bottom-right"
                            isActive={activeSide === 'bottom-right'}
                            path={`${path}.bottom-right`}
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            isLock={isRadiusLock}
                            onFocus={() => handleFocus('bottom-right')}
                            onBlur={handleBlur}
                            type="radius"
                        />
                    </InlineStack>
                </BlockStack>
            </div>
        </Box>
    );
};
