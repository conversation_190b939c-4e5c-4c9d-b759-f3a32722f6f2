import { FC } from 'react';
import { BreadcrumbItemProps, SeparatorIconConfig } from '../types';
import {
    DATA_SET_AUTO_ID_BREADCRUMB_SPACING,
    DATA_SET_AUTO_ID_SPACE_BETWEEN_TEXT_AND_ICON,
    SEPARATOR_STYLES,
} from '../constants';
import { BreadcrumbItem as BreadcrumbItemType } from '../types';
import { Tooltip } from '@shopify/polaris';
import { DATA_SET_AUTO_ID_INNER, useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseIcon } from '@/components/builder/base/BaseIcon';

interface BreadcrumbListProps {
    items: BreadcrumbItemType[];
    showHome: boolean;
    showSeparator: boolean;
    showLastItem: boolean;
    isCollapse: boolean;
    separatorIcon?: SeparatorIconConfig;
    autoId: string;
    beforeEllipsis: number;
    afterEllipsis: number;
}

interface BreadcrumbItemComponentProps extends BreadcrumbItemProps {
    shouldShowMiddleEllipsis: boolean;
    autoId: string;
    beforeEllipsis: number;
    afterEllipsis: number;
    isCollapse: boolean;
}

const filterVisibleItems = (
    arr: BreadcrumbItemType[],
    before: number = 0,
    after: number = 0,
    isCollapse: boolean = false,
): BreadcrumbItemType[] => {
    if (!arr.length) return [];

    if (!isCollapse) {
        return [...new Set(arr)];
    }

    if (before > 0 && !after) {
        return [...new Set([...arr.slice(0, before), arr[arr.length - 1]])];
    }

    if (after > 0 && !before) {
        return [...new Set([arr[0], ...arr.slice(-after)])];
    }

    if (before > 0 && after > 0) {
        return [...new Set([...arr.slice(0, before), ...arr.slice(-after)])];
    }

    return [...new Set(arr)];
};

const Separator: FC<{ icon: SeparatorIconConfig; autoId: string }> = ({ icon, autoId }) => (
    <div css={SEPARATOR_STYLES.container} {...{ [DATA_SET_AUTO_ID_SPACE_BETWEEN_TEXT_AND_ICON]: autoId }}>
        <BaseIcon source={icon.icon} size={icon.iconSize.val} color={icon.color} />
    </div>
);

const EllipsisWithTooltip: FC<{ tooltip?: string; handleClickEllipsis: () => void }> = ({
    tooltip,
    handleClickEllipsis,
}) => (
    <span style={SEPARATOR_STYLES.ellipsis} css={{ cursor: 'pointer' }} onClick={handleClickEllipsis}>
        <Tooltip content={tooltip}>
            <span>...</span>
        </Tooltip>
    </span>
);

export const BreadcrumbItem: FC<BreadcrumbItemComponentProps> = ({
    item,
    showSeparator,
    isLast,
    isFirst,
    separatorIcon,
    hiddenItemsTooltip,
    shouldShowMiddleEllipsis,
    isCollapse,
    autoId,
}) => {
    const isHome = isFirst && item.label === 'Home';
    const { updateBlockConfigsProperty, updateBlockProperty } = useBlockStore();
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    return (
        <li style={{ display: 'flex', alignItems: 'center' }}>
            <div>
                <span
                    css={{
                        textDecoration: isHome ? 'underline' : 'none',
                        opacity: isHome ? 0.5 : 1,
                    }}
                >
                    {item.label}
                </span>
            </div>

            {showSeparator && !isLast && (
                <>
                    {separatorIcon && <Separator icon={separatorIcon} autoId={autoId} />}
                    {shouldShowMiddleEllipsis && isCollapse && (
                        <>
                            <EllipsisWithTooltip
                                tooltip={hiddenItemsTooltip}
                                handleClickEllipsis={() => {
                                    if (isCollapse) {
                                        updateBlockConfigsProperty(autoId, 'content.isCollapse', false);
                                    } else {
                                        updateBlockProperty(autoId, currentDevice, 'content.isCollapse', false);
                                    }
                                }}
                            />
                            {separatorIcon && <Separator icon={separatorIcon} autoId={autoId} />}
                        </>
                    )}
                </>
            )}
            {!showSeparator && <span>&nbsp;&nbsp;</span>}
        </li>
    );
};

export const BreadcrumbList: FC<BreadcrumbListProps> = ({
    items,
    showHome,
    showSeparator,
    showLastItem,
    isCollapse,
    separatorIcon,
    autoId,
    beforeEllipsis = 0,
    afterEllipsis = 0,
}) => {
    const visibleItems = filterVisibleItems(items, Number(beforeEllipsis), Number(afterEllipsis), isCollapse).filter(
        (_, index, arr) => {
            if (index === 0) return showHome;
            if (index === arr.length - 1) return showLastItem;
            if (isCollapse) {
                return index < Number(beforeEllipsis) || index >= arr.length - Number(afterEllipsis);
            }
            return true;
        },
    );

    const hiddenItemsTooltip = items
        .filter((item) => !visibleItems.includes(item))
        .map((item) => item.label)
        .join(' > ');

    const shouldShowMiddleEllipsis = (index: number) => {
        if (Number(beforeEllipsis) + Number(afterEllipsis) >= items.length) {
            return false;
        }

        if (Number(beforeEllipsis) + Number(afterEllipsis) === 0) {
            return true;
        }

        if (beforeEllipsis > 0) {
            return index === beforeEllipsis - 1;
        }

        if (afterEllipsis > 0) {
            return index === visibleItems.length - afterEllipsis - 1;
        }

        return false;
    };
    return (
        <ol
            css={{
                listStyle: 'none',
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                margin: 0,
                padding: 0,
            }}
            {...{ [DATA_SET_AUTO_ID_BREADCRUMB_SPACING]: autoId }}
            {...{ [DATA_SET_AUTO_ID_INNER]: autoId }}
        >
            {visibleItems.map((item, index) => (
                <BreadcrumbItem
                    key={item.url}
                    item={item}
                    showSeparator={showSeparator}
                    isLast={index === visibleItems.length - 1}
                    isFirst={index === 0}
                    separatorIcon={separatorIcon}
                    hiddenItemsTooltip={hiddenItemsTooltip}
                    shouldShowMiddleEllipsis={shouldShowMiddleEllipsis(index)}
                    autoId={autoId}
                    beforeEllipsis={beforeEllipsis}
                    afterEllipsis={afterEllipsis}
                    isCollapse={isCollapse}
                />
            ))}
        </ol>
    );
};
