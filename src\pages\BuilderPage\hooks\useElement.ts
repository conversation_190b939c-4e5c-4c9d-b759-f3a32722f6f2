import { useState, useCallback } from 'react';
import { TabItemProps } from '../types/tabs';
import { ConfigTabs } from '../configs/tabs';

interface ElementsState {
    activeTab: string;
    searchTerm: string;
    isSearchOpen: boolean;
}

const defaultElementsState: ElementsState = {
    activeTab: ConfigTabs[0].id,
    searchTerm: '',
    isSearchOpen: false,
};

export const useElement = () => {
    const [selectedElementItem, setSelectedElementItem] = useState<TabItemProps | null>(null);
    const [elementsState, setElementsState] = useState<ElementsState>(defaultElementsState);

    const handleSelectItemElement = (item: TabItemProps) => {
        setSelectedElementItem(item);
    };

    const resetElementsState = useCallback(() => {
        setSelectedElementItem(null);
        setElementsState(defaultElementsState);
    }, []);

    return {
        selectedElementItem,
        elementsState,
        handleSelectItemElement,
        resetElementsState,
    };
};
