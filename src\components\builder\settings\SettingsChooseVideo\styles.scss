.settings-choose-video {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .Polaris-Button.Polaris-Button svg {
        fill: var(--pc-button-icon-fill_hover);
    }

    .loading-container {
        position: relative;
        width: 100%;
        height: 180px;

        .spinner-container {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .video-container {
        width: 100%;
        height: 180px;
        border-radius: 8px;
        overflow: hidden;

        &.hidden {
            display: none;
        }
    }

    .empty-video {
        width: 100%;
        height: 180px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px dashed rgba(138, 138, 138, 1);
        cursor: pointer;

        .empty-content {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            height: 100%;
            padding-inline: 32px;

            .icon-container {
                margin-bottom: 8px;
            }
        }
    }
}
