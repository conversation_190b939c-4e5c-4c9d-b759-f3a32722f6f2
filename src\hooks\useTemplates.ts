import { useState, useMemo } from 'react';
import clsx from 'clsx';
import { SortButtonChoice } from '@shopify/polaris';
import { apiAddress } from '@/configs/apiAddress';
import { capitalizeFirstLetter } from '@/utils';
import { TemplateListResponse, IndustryCategoryResponse, PageTypeResponse } from '@/pages/TemplatesPage/types';
import { QueryResult, useQueries } from './useQueries';
import { useDebounce } from './useDebounce';

interface TemplatesResultsMap {
    templates: QueryResult<TemplateListResponse>;
    pageTypes: QueryResult<PageTypeResponse>;
    industries: QueryResult<IndustryCategoryResponse>;
}

export function useTemplates() {
    const [industry, setIndustry] = useState<string[]>([]);
    const [queryValue, setQueryValue] = useState<string>('');
    const [pageType, setPageType] = useState<string[]>([]);
    const [sortSelected, setSortSelected] = useState<string[]>(['dateModified desc']);
    const [page, setPage] = useState<number>(1);
    const [isIndustryPopoverOpen, setIsIndustryPopoverOpen] = useState<boolean>(false);
    const LIMIT = 9;

    const searchValue = useDebounce(queryValue, 500, () => setPage(1));

    const templatesUrl = useMemo(() => {
        return clsx(
            apiAddress.templates,
            `?currentPage=${page}`,
            `&perPage=${LIMIT}`,
            `&sortName=${sortSelected[0].split(' ')[0]}`,
            `&order=${sortSelected[0].split(' ')[1]}`,
            searchValue ? `&keyword=${searchValue}` : '',
            pageType.length > 0 && `&pageType=${pageType}`,
            industry.length > 0 && `&industryCategory=${industry}`,
        ).replace(/\s+/g, '');
    }, [page, LIMIT, sortSelected, pageType, searchValue, industry]);

    const { results, isLoading, isError, refetchAll } = useQueries<TemplatesResultsMap>([
        {
            key: 'industries',
            url: apiAddress.industryCategory,
            method: 'GET',
            sleepTime: 300,
        },
        {
            key: 'pageTypes',
            url: apiAddress.templateType,
            method: 'GET',
            sleepTime: 300,
        },
        {
            key: 'templates',
            url: templatesUrl,
            method: 'GET',
            sleepTime: 300,
            dependencies: [templatesUrl],
        },
    ]);

    const industries = useMemo(() => results.industries?.data?.result?.data || [], [results.industries]);
    const pageTypes = useMemo(() => results.pageTypes?.data?.result?.data || [], [results.pageTypes]);
    const templates = useMemo(() => results.templates?.data?.result?.data?.list || [], [results.templates]);
    const totalTemplates = useMemo(() => results.templates?.data?.result?.data?.total || 0, [results.templates]);

    const isLoadingIndustries = results.industries?.isLoading || false;
    const isLoadingPageTypes = results.pageTypes?.isLoading || false;
    const isLoadingTemplates = results.templates?.isLoading || false;

    const industriesError = results.industries?.error || null;
    const pageTypesError = results.pageTypes?.error || null;
    const templatesError = results.templates?.error || null;

    const sortChoices: SortButtonChoice[] = [
        {
            label: 'Updated',
            value: 'dateModified asc',
            directionLabel: 'Oldest First',
        },
        {
            label: 'Updated',
            value: 'dateModified desc',
            directionLabel: 'Newest First',
        },
    ];

    const industryChoices = useMemo(() => {
        return industries.map((item) => ({
            label: item.name,
            value: item.id,
        }));
    }, [industries]);

    const pageTypeChoices = useMemo(() => {
        return pageTypes.map((item) => ({
            label: item.typeName,
            value: item.id,
        }));
    }, [pageTypes]);

    const appliedPageTypeLabel =
        'Type is ' +
        pageType
            .map((id) => capitalizeFirstLetter(pageTypeChoices.find((item) => item.value === id)?.label || ''))
            .join(', ');

    const appliedIndustryLabel =
        'Industry is ' +
        industry
            .map((id) => capitalizeFirstLetter(industryChoices.find((item) => item.value === id)?.label || ''))
            .join(', ');

    const selectedIndustryName = useMemo(() => {
        if (industry.length === 0) return 'Industry category';
        const found = industries.find((item) => item.id === industry[0]);
        return found?.name || 'Industry category';
    }, [industry, industries]);

    const clearIndustrySelection = () => {
        setIndustry([]);
        setIsIndustryPopoverOpen(false);
    };

    return {
        // Data
        templates,
        industries,
        pageTypes,
        totalTemplates,
        industryChoices,
        pageTypeChoices,
        sortChoices,
        appliedPageTypeLabel,
        appliedIndustryLabel,

        // State
        page,
        industry,
        pageType,
        queryValue,
        sortSelected,
        searchValue,
        selectedIndustryName,
        isIndustryPopoverOpen,

        // Action handlers
        setPage,
        setIndustry,
        setPageType,
        setQueryValue,
        setSortSelected,
        setIsIndustryPopoverOpen,
        clearIndustrySelection,

        // Query state
        isLoading,
        isLoadingIndustries,
        isLoadingPageTypes,
        isLoadingTemplates,
        isError,
        industriesError,
        pageTypesError,
        templatesError,

        // Constants
        limit: LIMIT,

        // Actions
        refetch: refetchAll,
    };
}
