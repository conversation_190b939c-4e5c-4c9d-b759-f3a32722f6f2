import { useEffect } from 'react';
import { useEditor } from '@tiptap/react';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import Superscript from '@tiptap/extension-superscript';
import Subscript from '@tiptap/extension-subscript';
import Highlight from '@tiptap/extension-highlight';
import Color from '@tiptap/extension-color';
import { extensions as customExtensions } from '@/components/builder/base/BaseTextEditor';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';

export const extensions = [
    StarterKit,
    TextStyle,
    Underline,
    Superscript,
    Subscript,
    Color,
    Highlight.configure({ multicolor: true }),

    ...Object.values(customExtensions),
];

interface UseTextEditorProps {
    blockId: string;
    path: string;
    editable?: boolean;
    isUpdateConfigs?: boolean;
}

export const useTextEditor = ({ blockId, path, editable = true, isUpdateConfigs }: UseTextEditorProps) => {
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const device = useBuilderStore((state) => state.currentDevice);

    const content = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path, blockId) || '';

    const editor = useEditor({
        extensions,
        content,
        editable,
        onUpdate: ({ editor }) => {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, path, editor.getHTML());
            } else {
                updateBlockProperty(blockId, device, path, editor.getHTML());
            }
        },
    });

    useEffect(() => {
        if (editor && content && editor.getHTML() !== content) {
            editor.commands.setContent(content);
        }
    }, [content, editor]);

    return { editor, content };
};
