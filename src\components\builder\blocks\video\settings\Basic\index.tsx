import { BlockStack, Box, Divider } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingElementID, SettingsChooseVideo, SettingsInput, SettingsToggle } from '@/components/builder/settings';

export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <Box paddingInline="200" paddingBlockStart="400">
            <BlockStack gap="300">
                <SettingElementID value={selectedBlockId} />
                <SettingsChooseVideo blockId={selectedBlockId} path="url" isUpdateConfigs />
                <Divider />
                <SettingsInput
                    label="URL"
                    path="url"
                    direction="column"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    inputProps={{ type: 'text', placeholder: 'Enter video url' }}
                />
                <Divider />
                <SettingsToggle
                    label="Show controls"
                    path="showControls"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    toggleProps={{ id: 'w-video-show-controls' }}
                />
                <SettingsToggle
                    label="Loop"
                    path="loop"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    toggleProps={{ id: 'w-video-loop' }}
                />
                <SettingsToggle
                    label="Autoplay"
                    path="autoplay"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    toggleProps={{ id: 'w-video-autoplay' }}
                />
                <SettingsToggle
                    label="Play audio"
                    path="playAudio"
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    toggleProps={{ id: 'w-video-play-audio' }}
                />
            </BlockStack>
        </Box>
    );
};
