import { FC, useState } from 'react';
import { BlockStack, Box, Card, ChoiceList, Divider, Filters, InlineStack, Page, Text } from '@shopify/polaris';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/stores';
import { Pagination, SortButton } from '@/components';
import { useMutation, useTemplateSections } from '@/hooks';
import { pathnames } from '@/configs';
import { apiAddress } from '@/configs/apiAddress';
import { TemplateCard, TemplateList } from '@/pages/TemplatesPage/TemplateList';
import styles from './styles.module.scss';

export const CreatePage: FC = () => {
    const navigate = useNavigate();
    const setTemplatePreview = useAppStore((state) => state.setTemplatePreview);
    const [treeLoading, setTreeLoading] = useState<{ [key: string]: boolean }>({});
    const {
        page,
        category,
        queryValue,
        sortChoices,
        categoryChoices,
        sortSelected,
        appliedFilters,
        templates,
        isLoading,
        totalTemplates,
        limit,
        setCategory,
        setSortSelected,
        setQueryValue,
        setPage,
    } = useTemplateSections();

    const createMutation = useMutation({
        url: apiAddress.shopSections.index,
        method: 'POST',
        onSuccess: () => {
            shopify.toast.show('Section created successfully');
        },
        onError: () => {
            shopify.toast.show('Failed to create section', { isError: true });
        },
    });

    const filters = [
        {
            key: 'category',
            label: 'Category',
            filter: (
                <div className={styles.customChoiceList}>
                    <ChoiceList
                        title=""
                        titleHidden
                        choices={categoryChoices}
                        allowMultiple
                        selected={category}
                        onChange={(selected) => {
                            setCategory(selected);
                            setPage(1);
                        }}
                    />
                </div>
            ),
            pinned: true,
        },
    ];

    return (
        <Page title="Create from template" backAction={{ content: 'Back', onAction: () => navigate(-1) }}>
            <div css={{ paddingBottom: '60px' }}>
                <Card padding="0">
                    <Box paddingInline="400" paddingBlockStart="400" paddingBlockEnd="0">
                        <BlockStack gap="300">
                            <Text as="p" variant="headingMd">
                                Section templates
                            </Text>
                            <div className={styles.filtersContainer}>
                                <Filters
                                    queryPlaceholder="Search templates  "
                                    filters={filters}
                                    appliedFilters={appliedFilters}
                                    queryValue={queryValue}
                                    onQueryChange={setQueryValue}
                                    onQueryClear={() => setQueryValue('')}
                                    onClearAll={() => {
                                        setCategory([]);
                                        setPage(1);
                                    }}
                                >
                                    <SortButton
                                        choices={sortChoices}
                                        selected={sortSelected}
                                        onChange={setSortSelected}
                                    />
                                </Filters>
                            </div>
                        </BlockStack>
                    </Box>
                    <Divider />
                    <Box paddingInline="400" paddingBlockStart="800" paddingBlockEnd="1000">
                        <BlockStack gap="800">
                            <TemplateList
                                data={templates}
                                isLoading={isLoading}
                                emptyTitle="No template found"
                                emptyDescription="Lorem ipsum dolor sit amet consectetur scelerisque."
                                renderItem={(data, index) => (
                                    <TemplateCard
                                        key={index}
                                        data={data}
                                        onPreview={() => {
                                            setTemplatePreview({
                                                name: data.title,
                                                url: 'https://seal-commerce-asia.myshopify.com/pages/socks-monthly-subscription',
                                            });
                                            shopify.modal.show('modal-preview-section-template');
                                        }}
                                        onSelect={async () => {
                                            setTreeLoading((prev) => ({ ...prev, [data.id]: true }));
                                            await createMutation.mutate({
                                                title: `Example section ${Math.ceil(Math.random() * 1000000)}`,
                                                thumbnail: '',
                                                htmlData: '<h1>{{ section.settings.title }}</h1>',
                                                jsonData: '<h1>title</h1>',
                                                status: 1,
                                            });
                                            setTreeLoading((prev) => ({ ...prev, [data.id]: false }));
                                            navigate(pathnames.sections);
                                        }}
                                        btnSelectLoading={treeLoading[data.id]}
                                    />
                                )}
                            />
                            {!isLoading && templates && templates.length > 0 && (
                                <InlineStack align="center">
                                    <Pagination
                                        value={page}
                                        onChange={setPage}
                                        total={Math.ceil(totalTemplates / limit)}
                                        size="large"
                                    />
                                </InlineStack>
                            )}
                        </BlockStack>
                    </Box>
                </Card>
            </div>
        </Page>
    );
};
