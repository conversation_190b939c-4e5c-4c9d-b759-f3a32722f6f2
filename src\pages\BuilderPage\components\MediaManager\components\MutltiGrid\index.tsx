import { ReactNode } from 'react';
import { Spinner } from '@shopify/polaris';
import { checkMimeType } from '@/utils';
import { ReactComponent as CropOutline } from '@/assets/svgs/crop-outline.svg';
import { ReactComponent as PlayIcon } from '@/assets/svgs/play-btn.svg';
import { useMediaManager } from '../../useMediaManager';
import { MediaType } from '../../types';

interface MediaGridProps {
    items: MediaType[];
    loading: boolean;
    containerRef: React.RefObject<HTMLDivElement | null>;
    renderEmptyState?: ReactNode;
}

export const MediaGrid = ({ items, loading, containerRef, renderEmptyState }: MediaGridProps) => {
    const multiple = useMediaManager((state) => state.multiple);
    const selected = useMediaManager((state) => state.selected);
    const allows = useMediaManager((state) => state.allows);
    const setOpen = useMediaManager((state) => state.setOpen);
    const setSelected = useMediaManager((state) => state.setSelected);

    const hasItems = items.length > 0;

    return (
        <div
            ref={containerRef}
            css={{
                position: 'relative',
                maxHeight: '100%',
                height: '100%',
                overflow: hasItems ? 'auto' : 'hidden',
            }}
        >
            {loading && containerRef.current && containerRef.current.scrollTop === 0 ? (
                <div
                    css={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: '100%',
                        height: '100%',
                    }}
                >
                    <Spinner />
                </div>
            ) : hasItems ? (
                <div
                    css={{
                        position: 'relative',
                        display: 'grid',
                        gridTemplateColumns: 'repeat(5, 1fr)',
                        gap: '12px',
                        width: '100%',
                        height: '100%',
                    }}
                >
                    {items.map((item, idx) => {
                        const { isVideo, isImage } = checkMimeType(item?.mimeType);
                        const isAllowed =
                            allows === 'all' || (allows === 'image' && isImage) || (allows === 'video' && isVideo);
                        return (
                            <div
                                key={`${item.id}-${idx}`}
                                css={{
                                    position: 'relative',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    height: '160px',
                                    backgroundColor: '#fff',
                                    border: '2px solid ',
                                    borderColor: selected.map(({ id }) => id).includes(item.id)
                                        ? '#007AFF'
                                        : 'transparent',
                                    '&:hover': {
                                        cursor: isAllowed ? 'pointer' : 'not-allowed',
                                        button: { display: 'flex' },
                                    },
                                }}
                                onClick={() => {
                                    if (!isAllowed) return;
                                    if (multiple) {
                                        setSelected(
                                            selected.map(({ id }) => id).includes(item.id)
                                                ? selected.filter(({ id }) => id !== item.id)
                                                : [...selected, item],
                                        );
                                    } else {
                                        setSelected([item]);
                                    }
                                }}
                            >
                                {isImage && (
                                    <>
                                        <img
                                            src={item.url}
                                            css={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                        />
                                        <button
                                            css={{
                                                display: 'none',
                                                position: 'absolute',
                                                top: '8px',
                                                right: '8px',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                width: '24px',
                                                height: '24px',
                                                padding: 0,
                                                backgroundColor: 'rgba(63, 63, 63, 0.8)',
                                                borderRadius: '50%',
                                                border: 'none',
                                                color: '#fff',
                                                '&:hover': {
                                                    cursor: 'pointer',
                                                    backgroundColor: '#6086F2',
                                                    transition: 'background-color 0.2s ease',
                                                },
                                            }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setOpen({ mode: 'editor', url: item.url });
                                            }}
                                        >
                                            <CropOutline width={16} height={16} />
                                        </button>
                                    </>
                                )}
                                {isVideo && (
                                    <>
                                        <video
                                            src={item.url}
                                            css={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                        />
                                        <PlayIcon
                                            css={{
                                                position: 'absolute',
                                                bottom: '2px',
                                                left: '4px',
                                                color: '#fff',
                                            }}
                                        />
                                    </>
                                )}
                            </div>
                        );
                    })}
                </div>
            ) : (
                renderEmptyState
            )}

            {loading && containerRef.current && containerRef.current.scrollTop > 0 && (
                <div
                    css={{
                        position: 'fixed',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        textAlign: 'center',
                        padding: '20px 0',
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        zIndex: 1,
                    }}
                >
                    <Spinner size="small" />
                </div>
            )}
        </div>
    );
};
