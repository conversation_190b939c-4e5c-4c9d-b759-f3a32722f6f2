import { FC, useState } from 'react';
import { Icon, BlockStack, Box } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import {
    SettingsBackgroundColor,
    SettingsColorPicker,
    SettingsFontFamily,
    SettingsInput,
    SettingsSelect,
    SettingsSliderInput,
    SettingsSwitchTab,
} from '@/components/builder/settings';

import { ChooseIcon } from './ChooseIcon';
import { BaseSwitchTab } from '@/components/builder/base/BaseSwitchTab';
import { iconPositionOptions, iconTypeOptions } from './configs';

import { generalOptions } from './configs';
import { textDecorationOptions, textFontWeightOptions, textTransformOptions } from '@/components/builder/data/options';
import { SettingsSelectSliderInput } from '@/components/builder/settings/SettingsSelectSliderInput';
import { letterSpacingOptions, lineHeightOptions } from '../../configs';
const DEFAULT_PATH_CONFIGS_CONTENT_HEADER = 'content.accordionHeader';
const DEFAULT_PATH_CONFIGS_CONTENT_COLLAPSE = 'content.accordionCollapse';

interface GeneralProps {
    id: string;
    isUpdateConfigs?: boolean;
    label: string;
}

export const General: FC<GeneralProps> = ({ id, isUpdateConfigs, label }) => {
    const [general, setGeneral] = useState('header');

    const handleChangeGeneral = (value: string) => {
        setGeneral(value);
    };

    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <BaseSwitchTab
                            options={generalOptions}
                            valueData={general}
                            onChange={handleChangeGeneral}
                            fullWidth
                            noTooltip
                        />
                        <SettingsBackgroundColor
                            path={`${
                                general === 'header'
                                    ? DEFAULT_PATH_CONFIGS_CONTENT_HEADER
                                    : DEFAULT_PATH_CONFIGS_CONTENT_COLLAPSE
                            }.background`}
                            blockId={id}
                            isUpdateConfigs={isUpdateConfigs}
                            label="Background color"
                        />
                        {general === 'header' && (
                            <>
                                <ChooseIcon
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.icon.typeIcon`}
                                    blockId={id || ''}
                                    label="Choose an icon"
                                    isUpdateConfigs={isUpdateConfigs}
                                    options={iconTypeOptions}
                                />
                                <SettingsSwitchTab
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.icon.pos`}
                                    blockId={id || ''}
                                    label="Icon position"
                                    isUpdateConfigs={isUpdateConfigs}
                                    options={iconPositionOptions}
                                />
                                <SettingsColorPicker
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.icon.color`}
                                    blockId={id || ''}
                                    label="Icon color"
                                    isUpdateConfigs={isUpdateConfigs}
                                />
                                <SettingsSliderInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.icon.size`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    title="Icon size"
                                    direction="column"
                                    max={100}
                                    step={1}
                                    min={0}
                                    inputProps={{
                                        align: 'center',
                                    }}
                                />
                                <SettingsSliderInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.horizontalPadding`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    title="Horizontal padding"
                                    direction="column"
                                    max={100}
                                    step={1}
                                    min={0}
                                    inputProps={{
                                        align: 'center',
                                    }}
                                />
                                <SettingsSliderInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.verticalPadding`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    title="Vertical padding"
                                    direction="column"
                                    max={100}
                                    step={1}
                                    min={0}
                                    inputProps={{
                                        align: 'center',
                                    }}
                                />
                                <SettingsFontFamily
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.fontFamily`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                />
                                <SettingsSelect
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.textTransform`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    label="Text transform"
                                    options={textTransformOptions}
                                />
                                <SettingsSelect
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.fontWeight`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    label="Font weight"
                                    options={textFontWeightOptions}
                                />
                                <SettingsInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.fontSize`}
                                    blockId={id || ''}
                                    isUpdateConfigs={isUpdateConfigs}
                                    label="Font size"
                                    inputProps={{
                                        suffix: 'px',
                                        min: 4,
                                    }}
                                />
                                <SettingsSwitchTab
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.textDecoration`}
                                    blockId={id}
                                    label="Text decoration"
                                    isUpdateConfigs={isUpdateConfigs}
                                    options={textDecorationOptions}
                                />
                                <SettingsColorPicker
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.color`}
                                    blockId={id}
                                    label="Text color"
                                    isUpdateConfigs={isUpdateConfigs}
                                />
                                <SettingsSelectSliderInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.lineHeight`}
                                    blockId={id}
                                    isUpdateConfigs={isUpdateConfigs}
                                    title="Line height"
                                    max={100}
                                    step={1}
                                    min={0}
                                    options={lineHeightOptions}
                                />
                                <SettingsSelectSliderInput
                                    path={`${DEFAULT_PATH_CONFIGS_CONTENT_HEADER}.text.letterSpacing`}
                                    blockId={id}
                                    isUpdateConfigs={isUpdateConfigs}
                                    title="Letter spacing"
                                    max={10}
                                    step={0.25}
                                    min={0}
                                    options={letterSpacingOptions}
                                />
                            </>
                        )}
                        <SettingsInput
                            path={`gap`}
                            blockId={id || ''}
                            isUpdateConfigs={isUpdateConfigs}
                            label="Gap"
                            inputProps={{
                                suffix: 'px',
                                min: 0,
                            }}
                        />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
