import { FC, useState } from 'react';
import { BaseItemLayout } from '@/components/builder/base';
import { BaseSpacing } from '@/components/builder/base/BaseSpacing';
import { BaseModalShape, BaseModalShapeProps } from '@/components/builder/base/BaseSpacing/components/BaseModalShape';
import './styles.scss';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
interface SettingsSpaceProps {
    label?: string;
    path: string;
    blockId: string;
    isUpdateConfig: boolean;
    isMargin?: boolean;
    isPadding?: boolean;
    selectedBlockTarget?: HTMLElement;
    cssVariable?: string;
}

export const SettingsSpace: FC<SettingsSpaceProps> = ({
    isMargin = true,
    isPadding = true,
    label,
    isUpdateConfig,
    blockId,
    path,
    selectedBlockTarget,
    cssVariable,
}) => {
    const [isLockedData, setIsLockedData] = useState(true);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const [openPopover, setOpenPopover] = useState<string | null>(null);

    const handleOpenChange = (key: string, open: boolean) => {
        setOpenPopover(open ? key : null);
    };

    const currentSpacing = isUpdateConfig
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(`${path}`, blockId);

    const directionArray = ['top', 'right', 'bottom', 'left'];

    const itemRenderer = (apply: 'padding' | 'margin') => {
        return (props: BaseModalShapeProps) => {
            const direction = props.side;
            const valueData = currentSpacing?.[apply]?.[direction]?.val;
            const unitData = currentSpacing?.[apply]?.[direction]?.unit;
            return (
                <BaseModalShape
                    {...props}
                    valueData={valueData}
                    unitData={unitData}
                    isOpen={openPopover === `${apply}-${direction}`}
                    selectedBlockTarget={selectedBlockTarget}
                    cssVariable={isLockedData ? `${cssVariable}-${apply}-${direction}` : `${cssVariable}-${apply}`}
                    onOpenChange={(open) => handleOpenChange(`${apply}-${direction}`, open)}
                    onChangeValueData={(value) => {
                        if (isLockedData) {
                            if (isUpdateConfig) {
                                updateBlockConfigsProperty(blockId, `${path}.${apply}.${props.side}.val`, value);
                            } else {
                                updateBlockProperty(
                                    blockId,
                                    currentDevice,
                                    `${path}.${apply}.${props.side}.val`,
                                    value,
                                );
                            }
                        } else {
                            directionArray.forEach((dir) => {
                                if (isUpdateConfig) {
                                    updateBlockConfigsProperty(blockId, `${path}.${apply}.${dir}.val`, value);
                                } else {
                                    updateBlockProperty(blockId, currentDevice, `${path}.${apply}.${dir}.val`, value);
                                }
                            });
                        }
                    }}
                />
            );
        };
    };

    const handleLock = () => {
        setIsLockedData((prev) => !prev);
    };
    return (
        <BaseItemLayout direction="column" textProps={{ as: 'p', variant: 'bodyMd', children: label }}>
            <BaseSpacing
                isMargin={isMargin}
                isPadding={isPadding}
                {...(isMargin && { marginItemRenderer: itemRenderer('margin') })}
                {...(isPadding && { paddingItemRenderer: itemRenderer('padding') })}
                isLockedData={isLockedData}
                onLock={handleLock}
            ></BaseSpacing>
        </BaseItemLayout>
    );
};
