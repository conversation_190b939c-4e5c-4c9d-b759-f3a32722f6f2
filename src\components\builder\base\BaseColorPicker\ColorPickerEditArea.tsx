import { useState } from 'react';
import { useClick, useFloating, useInteractions, offset } from '@floating-ui/react';
import { Icon } from '@shopify/polaris';
import { CaretDownIcon } from '@shopify/polaris-icons';
import { ColorPickerChannelInput } from './ColorPickerChanelInput';
import { ColorPickerChannelSlider } from './ColorPickerChanelSlider';
import { useColorPickerContext } from './use-color-picker-context.ts';

export const ColorPickerEditArea = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [mode, setMode] = useState<'hex' | 'rgba'>('hex');

    const colorPicker = useColorPickerContext();

    const { refs, floatingStyles, context } = useFloating({
        open: isOpen,
        onOpenChange: setIsOpen,
        placement: 'bottom-start',
        middleware: [offset({ mainAxis: 2 })],
    });
    const click = useClick(context);
    const { getReferenceProps, getFloatingProps } = useInteractions([click]);

    const handleModeChange = (mode: 'hex' | 'rgba') => {
        return () => {
            setMode(mode);
            setIsOpen(false);
        };
    };

    return (
        <>
            <div css={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBlock: '1rem' }}>
                <div
                    css={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        width: '40px',
                        height: '40px',
                        borderRadius: '6px',
                        outline: '2px solid #eeeeee',
                        backgroundImage:
                            'conic-gradient(rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 25%, transparent 0deg, transparent 50%, rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 75%, transparent 0deg)',
                        backgroundSize: '8px 8px',
                        overflow: 'hidden',
                    }}
                >
                    <div css={{ background: colorPicker.valueAsString }}></div>
                    <div
                        css={{
                            backgroundColor: colorPicker.value.withChannelValue('alpha', 1).toString('rgba'),
                        }}
                    ></div>
                </div>
                <div css={{ display: 'flex', flexDirection: 'column', gap: '1rem', flexGrow: 1 }}>
                    <ColorPickerChannelSlider channel="hue" />
                    <ColorPickerChannelSlider channel="alpha" />
                </div>
            </div>
            <div css={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <div
                    ref={refs.setReference}
                    css={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingInlineStart: '8px',
                        paddingInlineEnd: '4px',
                        width: '64px',
                        height: '32px',
                        borderRadius: '8px',
                        border: '1px solid #ebebeb',
                        cursor: 'pointer',
                        '& .Polaris-Icon': {
                            transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                            transition: 'all 0.25s ease',
                        },
                    }}
                    {...getReferenceProps()}
                >
                    <span>{mode === 'hex' ? 'Hex' : 'RGB'}</span>
                    <Icon source={CaretDownIcon} />
                </div>
                {isOpen && (
                    <div
                        ref={refs.setFloating}
                        style={floatingStyles}
                        css={{
                            minWidth: '64px',
                            background: 'white',
                            borderRadius: '4px',
                            border: '1px solid #ebebeb',
                            boxShadow:
                                '0px 20px 32px 0px rgba(96, 97, 112, 0.24), 0px 2px 8px 0px rgba(40, 41, 61, 0.08)',
                            overflow: 'hidden',
                        }}
                        {...getFloatingProps()}
                    >
                        <div
                            css={{
                                padding: '4px 8px',
                                cursor: 'pointer',
                                '&:hover': { background: '#f5f5f5' },
                            }}
                            onClick={handleModeChange('hex')}
                        >
                            Hex
                        </div>
                        <div
                            css={{
                                padding: '4px 8px',
                                cursor: 'pointer',
                                '&:hover': { background: '#f5f5f5' },
                            }}
                            onClick={handleModeChange('rgba')}
                        >
                            RGB
                        </div>
                    </div>
                )}

                <div
                    css={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '162px',
                        border: '1px solid #ebebeb',
                        borderRadius: '8px',
                        overflow: 'hidden',
                    }}
                >
                    {mode === 'hex' && <ColorPickerChannelInput channel="hex" />}
                    {mode === 'rgba' && (
                        <>
                            <ColorPickerChannelInput channel="red" />
                            <ColorPickerChannelInput channel="green" />
                            <ColorPickerChannelInput channel="blue" />
                        </>
                    )}
                    <ColorPickerChannelInput channel="alpha" />
                </div>
            </div>
        </>
    );
};
