import { FC, useEffect, useState } from 'react';
import { ActionListItemDescriptor, TextProps } from '@shopify/polaris';
import { RangeSliderValue } from '@shopify/polaris/build/ts/src/components/RangeSlider/types';
import { useBuilderStore, useBlockStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseInput, BaseSelect, BaseSlider } from '../../base';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseSelectProps = Parameters<typeof BaseSelect>[0];
type BaseInputProps = Parameters<typeof BaseInput>[0];
type BaseSliderProps = Parameters<typeof BaseSlider>[0];

interface SettingsSelectSliderInputProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
    options: ActionListItemDescriptor[];
    isUpdateConfigs?: boolean;
    title?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    selectProps?: Omit<BaseSelectProps, 'options' | 'value' | 'onChange'>;
    min?: number;
    max?: number;
    step?: number;
    isShadow?: boolean;
    inputProps?: Omit<BaseInputProps, 'value' | 'onChange'>;
    sliderProps?: Omit<BaseSliderProps, 'value' | 'onChange'>;
}

export const SettingsSelectSliderInput: FC<SettingsSelectSliderInputProps> = ({
    path,
    blockId,
    options,
    isUpdateConfigs,
    textProps,
    selectProps,
    title,
    min,
    max,
    step,
    isShadow,
    inputProps,
    sliderProps,
    ...otherProps
}) => {
    const value = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.type`, blockId)
        : getBlockBPProperty(`${path}.type`, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChange = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.type`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.type`, value);
        }
    };

    const sliderInputValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.payload.val`, blockId)
        : getBlockBPProperty(`${path}.payload.val`, blockId);

    const sliderInputSuffix = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.payload.unit`, blockId)
        : getBlockBPProperty(`${path}.payload.unit`, blockId);

    const [localValue, setLocalValue] = useState(sliderInputValue);

    useEffect(() => {
        setLocalValue(sliderInputValue);
    }, [sliderInputValue]);

    const updateValue = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.payload.val`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.payload.val`, value);
        }
    };

    const handleSliderChange = (value: RangeSliderValue) => {
        setLocalValue(value.toString());
    };

    const handleInputChange = (value: string) => {
        if (Number(value) < Number(min)) return;
        setLocalValue(value);
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: title };

    const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
        const value = localValue.toString() === '' ? '0' : localValue.toString();
        updateValue(value);
        e.currentTarget.blur();
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            const value = (e.target as HTMLInputElement).value === '' ? '0' : (e.target as HTMLInputElement).value;
            updateValue(value);
        }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        const value = e.currentTarget.value === '' ? '0' : e.currentTarget.value;
        updateValue(value);
    };

    return (
        <>
            <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
                <BaseSelect options={options} value={value} onChange={handleChange} {...selectProps} />
            </BaseItemLayout>
            {value === 'custom' && (
                <div className="settings-slider-input__container__slider-input">
                    <div onMouseUp={handleMouseUp}>
                        <BaseSlider
                            max={max}
                            min={min}
                            value={Number(localValue)}
                            onChange={handleSliderChange}
                            step={step}
                            {...sliderProps}
                        />
                    </div>
                    <div onKeyDown={handleKeyDown}>
                        <BaseInput
                            suffix={sliderInputSuffix}
                            isShadow={isShadow}
                            type="number"
                            value={localValue}
                            onChange={handleInputChange}
                            max={max}
                            min={min}
                            {...inputProps}
                            onBlur={handleBlur}
                        />
                    </div>
                </div>
            )}
        </>
    );
};
