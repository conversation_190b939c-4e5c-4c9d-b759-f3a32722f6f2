/* eslint-disable @typescript-eslint/no-explicit-any */
import { createContext, useContext } from 'react';
import { nanoid } from 'nanoid';
import { SettingsShadowData } from '@/components/builder/settings/SettingsShadow/types';

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function capitalizeFirstLetter(str: string) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

export const checkMimeType = (mimeType: string) => {
    if (!mimeType) return { isImage: false, isVideo: false };
    return {
        isImage: mimeType.startsWith('image/'),
        isVideo: mimeType.startsWith('video/'),
    };
};

type EventHandler<Event> = ((event?: Event) => void) | undefined;

export function createEventHandler<Event>(...eventHandlers: EventHandler<Event>[]) {
    return (event?: Event) => {
        eventHandlers.forEach((eventHandler) => eventHandler?.(event));
    };
}

export function createSafeContext<ContextValue>(errorMessage: string) {
    const Context = createContext<ContextValue | null>(null);

    const useSafeContext = () => {
        const ctx = useContext(Context);
        if (ctx === null) {
            throw new Error(errorMessage);
        }
        return ctx;
    };
    const Provider = ({ children, value }: { value: ContextValue; children: React.ReactNode }) => (
        <Context.Provider value={value}>{children}</Context.Provider>
    );

    return [Provider, useSafeContext] as const;
}

export function getShadowString(data: SettingsShadowData[]) {
    return data.map((item) => `${item.x.val}, ${item.y.val}, ${item.blur.val}`).join(', ');
}

export function genNewTextShadowLayer() {
    return {
        id: nanoid(),
        color: '#000000',
        x: { val: '0', unit: 'px' },
        y: { val: '2', unit: 'px' },
        blur: { val: '4', unit: 'px' },
    };
}

export function genNewBoxShadowLayer() {
    return {
        id: nanoid(),
        type: 'box',
        position: 'outside',
        color: '#000000',
        x: { val: '0', unit: 'px' },
        y: { val: '2', unit: 'px' },
        blur: { val: '4', unit: 'px' },
        spread: { val: '0', unit: 'px' },
        diffustion: { val: '0', unit: 'px' },
        focus: { val: '0', unit: 'px' },
    };
}

export function extractFontName(fontFamily: string) {
    const match = fontFamily.match(/"([^"]+)"/);
    if (match) {
        return match[1];
    } else {
        const parts = fontFamily.split(',').map((part) => part.trim());
        return parts[0].replace(/['"]/g, '').split('-').join(' ');
    }
}

export function generateTextShadow(shadows: SettingsShadowData) {
    if (!shadows) return null;
    const { color, x, y, blur } = shadows;

    return `${color} ${x.val}${x.unit} ${y.val}${y.unit} ${blur.val}${blur.unit}`;
}

export function generateBoxShadow(shadows: SettingsShadowData) {
    if (!shadows) {
        return null;
    }
    const { type, position, color, x, y, blur, spread, focus, diffustion } = shadows;

    const isInset = position === 'inside';
    const shadowType = isInset ? 'inset' : '';

    let blurValue = '0px';
    let spreadValue = '0px';

    if (type === 'realistic') {
        if (diffustion) {
            blurValue = `${diffustion.val}${diffustion.unit}`;
        }
        if (focus) {
            spreadValue = `${focus.val}${focus.unit}`;
        }
    } else {
        if (blur) {
            blurValue = `${blur.val}${blur.unit}`;
        }
        if (spread) {
            spreadValue = `${spread.val}${spread.unit}`;
        }
    }

    return `${shadowType} ${color} ${x.val}${x.unit} ${y.val}${y.unit} ${blurValue} ${spreadValue}`;
}

export function generateBorderWidth(
    top: { val: string; unit: string },
    right: { val: string; unit: string },
    bottom: { val: string; unit: string },
    left: { val: string; unit: string },
) {
    const topValue = top ? `${top.val}${top.unit}` : '0px';
    const rightValue = right ? `${right.val}${right.unit}` : '0px';
    const bottomValue = bottom ? `${bottom.val}${bottom.unit}` : '0px';
    const leftValue = left ? `${left.val}${left.unit}` : '0px';
    return `${topValue} ${rightValue} ${bottomValue} ${leftValue}`;
}

export function generateBorderRadius(radius: {
    'top-left': { val: string; unit: string };
    'top-right': { val: string; unit: string };
    'bottom-right': { val: string; unit: string };
    'bottom-left': { val: string; unit: string };
}) {
    const topLeftValue = radius['top-left'] ? `${radius['top-left'].val}${radius['top-left'].unit}` : '0px';
    const topRightValue = radius['top-right'] ? `${radius['top-right'].val}${radius['top-right'].unit}` : '0px';
    const bottomRightValue = radius['bottom-right']
        ? `${radius['bottom-right'].val}${radius['bottom-right'].unit}`
        : '0px';
    const bottomLeftValue = radius['bottom-left'] ? `${radius['bottom-left'].val}${radius['bottom-left'].unit}` : '0px';
    return `${topLeftValue} ${topRightValue} ${bottomRightValue} ${bottomLeftValue}`;
}

export function generateBackgroundImage(image: {
    url: string;
    repeat: string;
    fill: string;
    position: string;
    attachment: string;
}) {
    if (image) {
        return `background-image: url(${image.url});
                background-repeat: ${image.repeat == 'yes' ? 'repeat' : 'no-repeat'};
                background-size: ${image.fill};
                background-position: ${image.position};
                background-attachment: ${image.attachment};`;
    }
    return null;
}

export function generateAnimation(animation: {
    type: string;
    duration: { val: string; unit: string };
    delay: { val: string; unit: string };
    loop: string;
}) {
    return `animation-name: ${animation.type};
            animation-duration: ${animation.duration.val}${animation.duration.unit};
            animation-delay: ${animation.delay.val}${animation.delay.unit};
            animation-iteration-count: ${animation.loop};`;
}

export const getDataFromValueUnit = (valueUnit: any) => {
    return `${valueUnit.val}${valueUnit.unit}`;
};

export const generateValue = (valueUnit: any) => {
    if (valueUnit.val !== 'none') {
        if (valueUnit.val === 'auto') return 'auto';
        else return getDataFromValueUnit(valueUnit);
    }
};

export const getLetterSpacing = (letterSpacing: any) => {
    switch (letterSpacing.type) {
        case 'default':
            return '';
        case 'auto':
            return 'normal';
        case 'narrow':
            return '-0.05em';
        case 'wide':
            return '0.2em';
        case 'custom':
            return generateValue(letterSpacing.payload as any);
        default:
            return 'normal';
    }
};

export const getLineHeight = (lineHeight: any) => {
    switch (lineHeight.type) {
        case 'default':
            return '';
        case 'single':
            return 'normal';
        case '1.5':
            return '1.5';
        case 'double':
            return '2';
        case 'custom':
            return generateValue(lineHeight.payload as any);
        default:
            return 'normal';
    }
};
