/* eslint-disable react-hooks/exhaustive-deps */
import { FC, ReactNode, useState, useCallback } from 'react';
import AceEditor from 'react-ace';
import { InlineStack } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import { useAppStore } from '@/stores/appStore';
import { BaseModal } from '@/components/builder/base/BaseModal';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/ext-language_tools';

interface EditHtmlProps {
    elementTriggerProps?: { children: ReactNode } & Partial<typeof InlineStack>;
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const EditHtml: FC<EditHtmlProps> = ({ elementTriggerProps, blockId, path }) => {
    const isOpenModalShape = useAppStore((state) => state.isOpenModalShape);
    const setIsOpenModalShape = useAppStore((state) => state.setIsOpenModalShape);
    const htmlCode = getBlockProperty(`configs.${path}`, blockId);

    const [value, setValue] = useState(htmlCode);

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const onChange = useCallback((newValue: string) => setValue(newValue), []);
    const onCancel = useCallback(() => setValue(''), []);
    const onOpenModalShape = useCallback((isOpen: boolean) => setIsOpenModalShape(isOpen), []);
    const onClickAdd = useCallback(() => {
        updateBlockConfigsProperty(blockId, `${path}`, value);
        setIsOpenModalShape(false);
    }, [blockId, currentDevice, path, updateBlockConfigsProperty, updateBlockProperty, value]);

    return (
        <BaseModal
            isOpen={isOpenModalShape.isOpen}
            onOpenChange={onOpenModalShape}
            onOk={onClickAdd}
            isDisabled={!value}
            onCancel={onCancel}
            okTitle={'Change'}
            cancelTitle={'Cancel'}
            elementContentProps={{}}
            elementTriggerProps={elementTriggerProps}
            textTooltip="Help"
            elementModalContentProps={{
                title: 'Edit HTML',
                children: (
                    <AceEditor
                        value={value}
                        fontSize={12}
                        placeholder="Html code"
                        mode="html"
                        theme="chrome"
                        onChange={onChange}
                        name="Id"
                        width="100%"
                        height="100%"
                        setOptions={{
                            enableBasicAutocompletion: true,
                            enableLiveAutocompletion: true,
                            enableSnippets: true,
                            showLineNumbers: true,
                            tabSize: 2,
                            wrap: true,
                            useWorker: false,
                            showPrintMargin: false,
                            highlightActiveLine: true,
                            enableEmmet: true,
                        }}
                        editorProps={{ $blockScrolling: true }}
                    />
                ),
            }}
        />
    );
};
