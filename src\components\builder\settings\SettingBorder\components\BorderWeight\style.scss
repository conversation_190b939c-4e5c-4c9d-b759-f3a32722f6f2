.border-setting {
    display: flex;
    flex-direction: column;
    gap: 12px;
    &__input--left,
    &__input--right {
        min-width: 3.625rem;
        position: relative;
    }

    &__diagram {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 7.125rem;
        height: 100px;

        &-row {
            display: flex;
            justify-content: center;
            align-items: center;

            &--space-between {
                justify-content: space-between;
                width: 100%;
            }
        }
    }

    &__button {
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        cursor: pointer;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        &--lock {
            width: 1.5rem;
            height: 1.5rem;
            background-color: #8a8a8a;
            border-radius: 5px;
        }
    }

    &__line {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        overflow: hidden;

        &--top,
        &--bottom {
            width: 100%;
            max-width: 70%;
            height: 100%;
            max-height: 0.1875rem;
        }

        &--left,
        &--right {
            width: 100%;
            max-width: 0.1875rem;
            height: 100%;
            max-height: 70%;
        }
    }

    &__border-type-color {
        display: flex;
        justify-content: center;
        gap: 12px;
        align-items: center;
        width: 100%;
        .base-select-container {
            flex-grow: 1;
            width: 98px;
            min-width: 98px;
            button {
                height: 24px;
                min-height: 24px !important;
            }
        }
    }
}
