import { forwardRef, HTMLAttributes } from 'react';
import { mergeProps, Portal } from '@zag-js/react';
import { useColorPickerContext } from './use-color-picker-context.ts';

export type ColorPickerContentProps = HTMLAttributes<HTMLDivElement> & {
    hasPortal?: boolean;
};

export const ColorPickerContent = forwardRef<HTMLDivElement, ColorPickerContentProps>((props, ref) => {
    const colorPicker = useColorPickerContext();
    const { hasPortal = true, ...restProps } = props;
    const mergedProps = mergeProps(colorPicker.getContentProps(), restProps);

    return (
        <>
            {hasPortal ? (
                <Portal>
                    <div {...colorPicker.getPositionerProps()}>
                        <div {...mergedProps} ref={ref} />
                    </div>
                </Portal>
            ) : (
                <div {...mergedProps} data-content-background ref={ref} />
            )}
        </>
    );
});

ColorPickerContent.displayName = 'ColorPickerContent';
