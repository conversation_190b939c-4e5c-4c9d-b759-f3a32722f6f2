export type AcceptedFileTypeProps = {
    type: FileTypeProps;
    pageName: string;
    pageType: string;
    pageId: string;
} & File;

export type FileTypeProps = '.atk';

export interface ImportModalProps {
    isImportModalOpen: boolean;
    handleCloseImportModal: () => void;
}

export interface FileContentProps {
    files: AcceptedFileTypeProps[];
    hasFiles: boolean;
    fileCountText: string;
    totalPages: number;
    currentPage: number;
    rowMarkup: React.ReactNode;
    handleDropZoneDrop: (_dropFiles: File[], acceptedFiles: File[], _rejectedFiles: File[]) => void;
    handleNextPage: () => void;
    handlePreviousPage: () => void;
}
