import { BlockStack, Box, Scrollable } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import {
    SettingsColorPicker,
    SettingsHeading,
    SettingsRTE,
    SettingsSelect,
    SettingsFontFamily,
    SettingsInput,
    SettingsSliderInput,
    SettingsShadow,
    SettingsSwitchTab,
} from '@/components/builder/settings';
import {
    textAlignOptions,
    textDecorationOptions,
    textFontStyleOptions,
    textFontWeightOptions,
    textTransformOptions,
} from '@/components/builder/data/options';

export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    if (!selectedBlockId) return null;

    return (
        <Scrollable
            style={{ position: 'absolute', inset: 0, top: '53px', maxHeight: 'calc(100% - 53px)', overflowX: 'hidden' }}
            scrollbarWidth="none"
        >
            <Box paddingInline="200" paddingBlockStart="400">
                <BlockStack gap="400">
                    <SettingsRTE blockId={selectedBlockId} path="content" isUpdateConfigs />
                    <SettingsHeading label="Style" blockId={selectedBlockId} path="content" isUpdateConfigs />
                    <SettingsFontFamily blockId={selectedBlockId} path="fontFamily" />
                    <SettingsSelect
                        label="Font weight"
                        path="fontWeight"
                        blockId={selectedBlockId}
                        options={textFontWeightOptions}
                    />
                    <SettingsInput
                        path="fontSize"
                        blockId={selectedBlockId}
                        label="Font size"
                        inputProps={{
                            suffix: 'px',
                            min: 4,
                        }}
                    />
                    <SettingsSelect
                        label="Font style"
                        path="fontStyle"
                        blockId={selectedBlockId}
                        options={textFontStyleOptions}
                    />
                    <SettingsSwitchTab
                        label="Text align"
                        path="textAlign"
                        blockId={selectedBlockId}
                        options={textAlignOptions}
                    />
                    <SettingsSwitchTab
                        label="Text decoration"
                        path="textDecoration"
                        blockId={selectedBlockId}
                        options={textDecorationOptions}
                    />
                    <SettingsSelect
                        label="Text transform"
                        path="textTransform"
                        blockId={selectedBlockId}
                        options={textTransformOptions}
                    />
                    <SettingsShadow
                        type="text-shadow"
                        blockId={selectedBlockId}
                        path="textShadow"
                        label="Text shadow"
                    />
                    <SettingsColorPicker path="color" blockId={selectedBlockId} label="Text color" />
                    <SettingsSliderInput
                        title="Line height"
                        path="lineHeight"
                        blockId={selectedBlockId}
                        min={0}
                        max={5}
                        step={0.1}
                        direction="column"
                    />
                    <SettingsSliderInput
                        title="Letter spacing"
                        path="letterSpacing"
                        blockId={selectedBlockId}
                        min={-20}
                        max={20}
                        step={1}
                        direction="column"
                        inputProps={{ suffix: 'px' }}
                    />
                </BlockStack>
            </Box>
        </Scrollable>
    );
};
