import { FC } from 'react';
import { Link } from 'react-router-dom';
import { NavMenu as PolarisNavMenu } from '@shopify/app-bridge-react';
import { menu } from '@/configs';

export const NavMenu: FC = () => {
    return (
        <PolarisNavMenu>
            {Object.entries(menu).map(([key, value]) => (
                <Link key={key} to={value.path} rel={value.rel}>
                    {value.label}
                </Link>
            ))}
        </PolarisNavMenu>
    );
};
