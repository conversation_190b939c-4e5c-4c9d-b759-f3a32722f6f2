/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react';
import {
    DATA_SET_FORM_TEXTAREA_ELEMENT,
    DATA_SET_FORM_INPUT,
    DATA_SET_FORM_FIELD_ID,
} from '@/components/builder/blocks/form/constants';

interface FormTextareaProps {
    field: any;
    index: number;
    autoId: string;
}

export const FormTextarea: FC<FormTextareaProps> = ({ field, index, autoId }) => (
    <textarea
        {...{ [`${DATA_SET_FORM_TEXTAREA_ELEMENT}-${index}`]: autoId }}
        {...{ [`${DATA_SET_FORM_INPUT}`]: autoId }}
        {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
        value={field.initial_text}
    />
);
