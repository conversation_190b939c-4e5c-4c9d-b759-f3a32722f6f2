import { FC } from 'react';
import { Icon } from '@shopify/polaris';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { SettingsSelect, SettingsToggle, SettingsCheckbox, SettingsSliderInput } from '@/components/builder/settings';
import { InfoIcon } from '@shopify/polaris-icons';

interface CommonInputFieldSettingsProps {
    path: string;
    blockId: string;
    showInitialText?: string;
    setCharLimit?: boolean;
    addPatternValidation?: boolean;
    disabled?: boolean;
    fieldKey?: string;
    selectedBlockTarget?: HTMLElement;
}

export const LabelSettings: FC<{ path: string; blockId: string }> = ({ path, blockId }) => (
    <SettingsInput
        path={`${path}.label`}
        blockId={blockId}
        isUpdateConfigs
        inputProps={{ type: 'text', placeholder: 'Enter label' }}
        direction="column"
        label="Title"
        textProps={{ fontWeight: 'medium' }}
        tooltipContent="Add field titles to help customers identify information more easily"
        hasTooltip
        tooltipChildren={<Icon source={InfoIcon} />}
    />
);

export const KeySettings: FC<{ path: string; blockId: string; disabled?: boolean }> = ({ path, blockId, disabled }) => (
    <SettingsInput
        path={`${path}.key`}
        blockId={blockId}
        isUpdateConfigs
        inputProps={{ type: 'text', disabled: disabled }}
        direction="column"
        label="Key"
        textProps={{ fontWeight: 'medium' }}
        tooltipContent="Key is only, required and cannot be duplicated"
        hasTooltip
        tooltipChildren={<Icon source={InfoIcon} />}
    />
);

export const InitialTextSettings: FC<CommonInputFieldSettingsProps> = ({ path, blockId, showInitialText }) => (
    <>
        <SettingsSelect
            path={`${path}.showInitialText`}
            blockId={blockId}
            isUpdateConfigs
            direction="column"
            label="Show initial text"
            options={[
                { id: 'none', content: 'None' },
                { id: 'placeholder', content: 'Placeholder' },
                { id: 'default', content: 'Default' },
                { id: 'default-and-placeholder', content: 'Default and placeholder' },
            ]}
            textProps={{ fontWeight: 'medium' }}
            tooltipContent="Display suggested content in fields to help customers enter correct content"
            hasTooltip
            tooltipChildren={<Icon source={InfoIcon} />}
        />

        {(showInitialText === 'default' || showInitialText === 'default-and-placeholder') && (
            <SettingsInput
                path={`${path}.initialText`}
                blockId={blockId}
                isUpdateConfigs
                inputProps={{ type: 'text', placeholder: 'Enter initial text' }}
                direction="column"
                label="Default"
                textProps={{ fontWeight: 'medium' }}
            />
        )}

        {(showInitialText === 'placeholder' || showInitialText === 'default-and-placeholder') && (
            <SettingsInput
                path={`${path}.placeholder`}
                blockId={blockId}
                isUpdateConfigs
                inputProps={{ type: 'text', placeholder: 'Enter placeholder' }}
                direction="column"
                label="Placeholder"
                textProps={{ fontWeight: 'medium' }}
                tooltipContent="Add hints like 'Enter information' or 'Fill in here' when fields are left blank"
                hasTooltip
                tooltipChildren={<Icon source={InfoIcon} />}
            />
        )}
    </>
);

export const ValidationSettings: FC<{ path: string; blockId: string }> = ({ path, blockId }) => (
    <>
        <SettingsCheckbox
            path={`${path}.validations.required`}
            blockId={blockId}
            isUpdateConfigs
            direction="column"
            label="Required"
            hideTitle
        />
        <SettingsCheckbox
            path={`${path}.validations.readOnly`}
            blockId={blockId}
            isUpdateConfigs
            direction="column"
            label="Read only"
            hideTitle
        />
    </>
);

export const CharacterLimitSettings: FC<CommonInputFieldSettingsProps> = ({ path, blockId, setCharLimit }) => (
    <>
        <SettingsToggle
            path={`${path}.characterLimit.setCharLimit`}
            blockId={blockId}
            isUpdateConfigs
            label="Set character limit"
            toggleProps={{
                id: `${path}.characterLimit.setCharLimit`,
            }}
            textProps={{ fontWeight: 'medium' }}
        />

        {setCharLimit && (
            <SettingsInput
                path={`${path}.characterLimit.charLimit`}
                blockId={blockId}
                isUpdateConfigs
                inputProps={{ type: 'text', id: `form_characterLimit`, min: 0, maxLength: 500 }}
                label="Character limit"
                textProps={{ as: 'p', variant: 'bodyMd' }}
                containerClassName="number-settings"
            />
        )}
    </>
);

export const PatternValidationSettings: FC<CommonInputFieldSettingsProps> = ({
    path,
    blockId,
    addPatternValidation,
}) => (
    <>
        <SettingsToggle
            path={`${path}.pattern.addPatternValidation`}
            blockId={blockId}
            isUpdateConfigs
            label="Add pattern validation"
            toggleProps={{
                id: `${path}.pattern.addPatternValidation`,
            }}
            textProps={{ fontWeight: 'medium' }}
        />

        {addPatternValidation && (
            <SettingsInput
                path={`${path}.pattern.pattern`}
                blockId={blockId}
                isUpdateConfigs
                inputProps={{ type: 'text', maxLength: 500, multiline: 4, placeholder: 'Enter pattern' }}
                direction="column"
            />
        )}
    </>
);

export const LayoutSettings: FC<CommonInputFieldSettingsProps> = ({ path, blockId, fieldKey, selectedBlockTarget }) => {
    return (
        <>
            <SettingsCheckbox
                path={`${path}.separateLine`}
                blockId={blockId}
                isUpdateConfigs
                direction="column"
                label="Start new line at this field"
            />

            <SettingsSliderInput
                path={`fieldSizes.${fieldKey}.fieldWidth`}
                selectedBlockTarget={selectedBlockTarget as HTMLElement}
                cssVariable={`--form-${fieldKey}-width`}
                blockId={blockId}
                isUpdateConfigs={false}
                direction="column"
                title="Field width"
                min={10}
                sliderProps={{
                    min: 10,
                    max: 100,
                    step: 1,
                }}
                inputProps={{
                    suffix: '%',
                    min: 10,
                    max: 100,
                    step: 1,
                }}
            />
            <SettingsSliderInput
                path={`fieldSizes.${fieldKey}.fieldHeight`}
                selectedBlockTarget={selectedBlockTarget as HTMLElement}
                cssVariable={`--form-${fieldKey}-height`}
                blockId={blockId}
                isUpdateConfigs={false}
                direction="column"
                title="Field height"
                min={10}
                max={100}
                step={1}
                inputProps={{
                    min: 10,
                    max: 100,
                    step: 1,
                    suffix: 'px',
                }}
            />
        </>
    );
};
