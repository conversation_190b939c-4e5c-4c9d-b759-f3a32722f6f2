import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsSpace } from '@/components/builder/settings/SettingsSpace';
interface SpaceProps {
    id: string;
}

export const Space: FC<SpaceProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Space"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <SettingsSpace path="breadcrumbSpacing" blockId={id} isUpdateConfig={false} label="Space" />
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
