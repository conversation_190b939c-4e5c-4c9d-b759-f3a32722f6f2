import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { FC } from 'react';
import { FormSettings } from '@/components/builder/blocks/form/types';
interface FormOutlinedProps {
    option: FormSettings;
}

export const FormOutlined: FC<FormOutlinedProps> = ({ option }) => {
    return (
        <BlockToolbar
            {...option}
            id={option.id}
            configs={option.configs as unknown as Record<string, unknown>}
            key={option.id}
            style={{
                width: '100%',
                height: '100%',
                background: '#FAFBFB',
                borderRadius: '8px',
                border: '1px solid #EBEBEB',
                cursor: 'move',
                display: 'flex',
                padding: '9px 13px',
                justifyContent: 'center',
                flexDirection: 'column',
                alignItems: 'center',
            }}
        >
            <div
                css={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    justifyContent: 'center',
                    gap: '10px',
                    pointerEvents: 'none',
                }}
            >
                <input
                    type="text"
                    css={{
                        width: '100%',
                        height: '32px',
                        border: '1px solid #616161',
                        borderRadius: '8px',
                    }}
                />
                <input
                    type="text"
                    css={{
                        width: '100%',
                        height: '32px',
                        border: '1px solid #616161',
                        borderRadius: '8px',
                    }}
                />
                <div
                    css={{
                        height: '32px',
                        background: '#616161',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '8px',
                        color: 'white',
                        padding: '0 10px',
                    }}
                >
                    Submit
                </div>
            </div>
        </BlockToolbar>
    );
};
