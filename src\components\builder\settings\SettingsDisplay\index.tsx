import { FC, useMemo } from 'react';
import { BlockStack, Box, BoxProps } from '@shopify/polaris';
import { SettingsToggle } from '@/components/builder/settings';
import { getBlockProperty } from '@/utils/shared';

interface SettingsDisplayProps {
    blockId: string;
    boxProps?: BoxProps;
}

const DEVICES = ['Desktop', 'Mobile'] as const;

export const SettingsDisplay: FC<SettingsDisplayProps> = ({ blockId, boxProps }) => {
    const path = 'displayOn';
    const deviceSettings = useMemo(() => {
        return DEVICES.map((device) => {
            const value = getBlockProperty(`configs.${path}${device}`, blockId) || false;
            return {
                device,
                isActive: value,
                pathKey: `${path}${device}`,
                id: `display-${blockId}-on-${device.toLowerCase()}`,
            };
        });
    }, [path, blockId]);

    return (
        <Box {...boxProps}>
            <BlockStack gap="300">
                {deviceSettings.map(({ device, pathKey, id }) => (
                    <SettingsToggle
                        key={device}
                        label={device}
                        path={pathKey}
                        blockId={blockId}
                        isUpdateConfigs
                        toggleProps={{ id }}
                    />
                ))}
            </BlockStack>
        </Box>
    );
};
