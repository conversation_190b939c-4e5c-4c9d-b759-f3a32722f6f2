import { Auto_BlockToolbar, genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const tab2: Auto_BlockToolbar = {
    id: genRandomBlockId(),
    cname: 'tabs',
    label: 'Tabs',
    type: 'tabs',
    configs: {
        displayOnDesktop: true,
        displayOnMobile: true,
        animation: {
            type: 'none',
            duration: { val: '0', unit: 's' },
            loop: '1',
            delay: { val: '0', unit: 's' },
        },
        items: [
            {
                id: genRandomBlockId(),
                name: 'Tab 1',
            },
            {
                id: genRandomBlockId(),
                name: 'Tab 2',
            },
            {
                id: genRandomBlockId(),
                name: 'Tab 3',
            },
        ],
    },
    bpConfigs: {
        desktop: {
            width: { val: '550', unit: 'px' },
            height: { val: '300', unit: 'px' },
            basic: {
                tabContainer: {
                    align: 'start',
                    gap: { val: '2', unit: 'px' },
                    direction: 'column',
                    background: {
                        type: 'color',
                        color: '#ffffff',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    boxShadow: undefined,
                    border: {
                        color: '#000000',
                        top: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        type: 'default',
                        radius: {
                            'top-left': { val: '0', unit: 'px' },
                            'top-right': { val: '0', unit: 'px' },
                            'bottom-right': { val: '0', unit: 'px' },
                            'bottom-left': { val: '0', unit: 'px' },
                        },
                    },
                },
                tabNormal: {
                    icon: {
                        source: '<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1664 1896.0833"> <path d="M1664 647q0 22-26 48l-363 354 86 500q1 7 1 20 0 21-10.5 35.5T1321 1619q-19 0-40-12l-449-236-449 236q-22 12-40 12-21 0-31.5-14.5T301 1569q0-6 2-20l86-500L25 695Q0 668 0 647q0-37 56-46l502-73L783 73q19-41 49-41t49 41l225 455 502 73q56 9 56 46z"></path> </svg>',
                        color: '#4A4A4A',
                        size: {
                            val: '20',
                            unit: 'px',
                        },
                    },
                    background: {
                        type: 'color',
                        color: '#ffffff',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    direction: 'row',
                    fitted: false,
                    spacing: {
                        val: '4',
                        unit: 'px',
                    },
                    border: {
                        color: '#000000',
                        top: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        type: 'default',
                        radius: {
                            'top-left': { val: '4', unit: 'px' },
                            'top-right': { val: '4', unit: 'px' },
                            'bottom-right': { val: '0', unit: 'px' },
                            'bottom-left': { val: '0', unit: 'px' },
                        },
                    },
                    text: {
                        fontFamily: 'Poppins, sans-serif',
                        fontWeight: '400',
                        fontSize: { val: '14', unit: 'px' },
                        fontStyle: 'default',
                        textAlign: 'center',
                        textDecoration: 'default',
                        textTransform: 'default',
                        textShadow: undefined,
                        color: '#000000',
                        lineHeight: { val: '1.5' },
                        letterSpacing: { val: '0', unit: 'px' },
                    },
                },
                tabHover: {
                    icon: {
                        color: '#ffffff',
                    },
                    background: {
                        type: 'color',
                        color: '#000000',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    border: undefined,
                    text: {
                        textDecoration: 'default',
                        color: '#ffffff',
                        textShadow: undefined,
                    },
                },
                tabSelected: {
                    icon: {
                        size: {
                            val: '20',
                            unit: 'px',
                        },
                        source: '<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1792 1896.0833"> <path d="M896 1664q-26 0-44-18l-624-602q-10-8-27.5-26T145 952.5 77 855 23.5 734 0 596q0-220 127-344t351-124q62 0 126.5 21.5t120 58T820 276t76 68q36-36 76-68t95.5-68.5 120-58T1314 128q224 0 351 124t127 344q0 221-229 450l-623 600q-18 18-44 18z"></path> </svg>',
                        color: '#ffffff',
                    },
                    background: {
                        type: 'color',
                        color: '#000000',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    border: undefined,
                    text: {
                        textDecoration: 'default',
                        color: '#ffffff',
                        textShadow: undefined,
                    },
                },
                containers: {
                    background: {
                        type: 'image',
                        color: '#ffffff',
                        image: {
                            url: 'https://cdn-manage.shopone.io/master-freemedia/freeMedia/Rectangle%2040050-min-908-437.jpg',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    border: {
                        color: '#000000',
                        top: { val: '0', unit: 'px' },
                        right: { val: '0', unit: 'px' },
                        bottom: { val: '0', unit: 'px' },
                        left: { val: '0', unit: 'px' },
                        type: 'default',
                        radius: {
                            'top-left': { val: '0', unit: 'px' },
                            'top-right': { val: '0', unit: 'px' },
                            'bottom-right': { val: '0', unit: 'px' },
                            'bottom-left': { val: '0', unit: 'px' },
                        },
                    },
                },
            },
        },
        mobile: {
            width: { val: '300', unit: 'px' },
            height: { val: '180', unit: 'px' },
        },
    },
    overlay: {
        desktop: {
            width: 550,
            height: 300,
        },
        mobile: {
            width: 300,
            height: 180,
        },
    },
};
