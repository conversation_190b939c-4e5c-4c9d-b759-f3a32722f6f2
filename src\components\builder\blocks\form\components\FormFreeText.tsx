import { FC } from 'react';
import { useTextEditor } from '@/pages/BuilderPage/hooks';
import { EditorContent } from '@tiptap/react';
import { DATA_SET_FORM_FIELD_ID } from '@/components/builder/blocks/form/constants';
import { FormField } from '../types';
interface FormFreeTextProps {
    autoId: string;
    field: FormField;
    index: number;
}

export const FormFreeText: FC<FormFreeTextProps> = ({ field, index, autoId }) => {
    const { editor } = useTextEditor({
        blockId: autoId,
        path: `form.${index}.description`,
        editable: false,
        isUpdateConfigs: true,
    });

    return (
        <EditorContent
            {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
            editor={editor}
            onDoubleClick={() => {
                editor?.setEditable(true);
                editor?.chain().focus().selectAll().run();
            }}
            onBlur={() => editor?.setEditable(false)}
            spellCheck={false}
        />
    );
};
