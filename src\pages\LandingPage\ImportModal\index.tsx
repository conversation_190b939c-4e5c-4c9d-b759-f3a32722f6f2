import { FC, useCallback, useMemo, useState, useEffect } from 'react';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { Text, IndexTable } from '@shopify/polaris';
import JSZip from 'jszip';
import * as sc from './styled';
import { AcceptedFileTypeProps, ImportModalProps } from './types';
import { ErrorMessage } from './ErrorMessage';
import { FileContent } from './FileContent';
import { useAppStore } from '@/stores/appStore/useAppStore';

export const ImportModal: FC<ImportModalProps> = ({ isImportModalOpen, handleCloseImportModal }) => {
    const [files, setFiles] = useState<Array<AcceptedFileTypeProps>>([]);
    const [uploadedFileName, setUploadedFileName] = useState<File>();
    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [isModalReady, setIsModalReady] = useState(false);
    const itemsPerPage = 5;
    const importPage = useAppStore((state) => state.importPage);
    const currentParams = useAppStore((state) => state.currentParams);

    useEffect(() => {
        if (isImportModalOpen) {
            setIsModalReady(true);
        } else {
            setIsModalReady(false);
        }
    }, [isImportModalOpen]);

    const paginatedFiles = useMemo(
        () => files.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage),
        [files, currentPage],
    );

    const rowMarkup = useMemo(
        () =>
            paginatedFiles.map(({ name, type }, index) => (
                <IndexTable.Row id={name} key={`${name}-${index}`} position={index}>
                    <IndexTable.Cell>
                        <Text variant="bodyMd" as="span">
                            {name}
                        </Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>{type}</IndexTable.Cell>
                </IndexTable.Row>
            )),
        [paginatedFiles],
    );

    const handleClose = useCallback(() => {
        setIsError(false);
        setFiles([]);
        setCurrentPage(1);
        handleCloseImportModal();
    }, [handleCloseImportModal]);

    const handleDropZoneDrop = useCallback((_dropFiles: File[], acceptedFiles: File[], _rejectedFiles: File[]) => {
        const validFiles = acceptedFiles.filter((file): file is AcceptedFileTypeProps => {
            const extension = file.name.toLowerCase().split('.').pop();
            return extension === 'atk';
        });
        setUploadedFileName(validFiles[0]);
        validFiles.forEach(async (file) => {
            try {
                const zip = new JSZip();
                const zipContent = await zip.loadAsync(file);
                for (const [fileName, zipEntry] of Object.entries(zipContent.files)) {
                    if (!zipEntry.dir && fileName === 'pages-info.json') {
                        const content = await zipEntry.async('text');
                        try {
                            const jsonContent = JSON.parse(content);
                            setFiles(
                                jsonContent.map((page: { title: string; type: string; id: string }) => ({
                                    name: page.title,
                                    type: page.type,
                                    id: page.id,
                                })),
                            );
                            setCurrentPage(1);
                        } catch (jsonError) {
                            console.error(jsonError);
                            setIsError(true);
                        }
                    }
                }
            } catch (error) {
                console.error(error);
                setIsError(true);
            }
        });

        setIsError(validFiles.length === 0 || _rejectedFiles.length > 0);
    }, []);

    const handleImport = useCallback(async () => {
        if (!uploadedFileName) {
            setIsError(true);
            shopify.toast.show('Please select a file to import', {
                isError: true,
                duration: 1500,
            });
            return;
        }

        const formData = new FormData();
        formData.append('uploadFile', uploadedFileName);

        try {
            setIsLoading(true);
            await importPage(formData, currentParams);
            handleCloseImportModal();
            shopify.toast.show('Imported successfully', {
                isError: false,
                duration: 1500,
            });
        } catch (error) {
            console.error('Import failed:', error);
            setIsError(true);
            shopify.toast.show('Import failed', {
                isError: true,
                duration: 1500,
            });
        } finally {
            setIsLoading(false);
        }
    }, [uploadedFileName, importPage, handleCloseImportModal, currentParams]);

    //

    const handleNextPage = useCallback(() => {
        setCurrentPage((prev) => prev + 1);
    }, []);

    const handlePreviousPage = useCallback(() => {
        setCurrentPage((prev) => Math.max(1, prev - 1));
    }, []);

    const fileCountText = useMemo(() => `${files.length} ${files.length > 1 ? 'Pages' : 'Page'}`, [files.length]);
    const hasFiles = useMemo(() => files.length > 0, [files.length]);
    const totalPages = useMemo(() => Math.ceil(files.length / itemsPerPage), [files.length]);
    return (
        <Modal open={isImportModalOpen} onHide={handleClose}>
            <TitleBar title="Import pages/sections">
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleClose();
                    }}
                >
                    Cancel
                </button>
                <button
                    variant="primary"
                    onClick={handleImport}
                    disabled={!hasFiles}
                    loading={isLoading ? '' : undefined}
                >
                    Import
                </button>
            </TitleBar>
            <ErrorMessage isError={isError} />
            {isModalReady && (
                <sc.BoxStyled>
                    <FileContent
                        files={files}
                        hasFiles={hasFiles}
                        fileCountText={fileCountText}
                        totalPages={totalPages}
                        currentPage={currentPage}
                        rowMarkup={rowMarkup}
                        handleDropZoneDrop={handleDropZoneDrop}
                        handleNextPage={handleNextPage}
                        handlePreviousPage={handlePreviousPage}
                    />
                </sc.BoxStyled>
            )}
        </Modal>
    );
};
