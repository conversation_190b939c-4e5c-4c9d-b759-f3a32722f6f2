/* eslint-disable @typescript-eslint/no-explicit-any */

export const isValidCssValue = (value: any): boolean => {
    return value !== undefined && value !== null && value !== '';
};

export const isValidUnitValue = (unitObj: any): boolean => {
    return unitObj && isValidCssValue(unitObj.val);
};

export const formatCss = (css: string): string => {
    let formatted = css
        .replace(/\s+/g, ' ')
        .replace(/\{\s+/g, '{\n  ')
        .replace(/;\s+/g, ';\n  ')
        .replace(/\s*}\s*/g, '\n}\n')
        .replace(/\n\s*\n/g, '\n')
        .replace(/}\n([^@\n])/g, '}\n\n$1')
        .replace(/;(?=[^;\n]*})/g, ';')
        .replace(/{\n\s*}/g, '{}')
        .replace(/\n\s*{\n\s*([^}]+);\n\s*}/g, ' { $1; }');

    formatted = formatted.replace(/@media[^{]+{/g, (match) => {
        return match.replace(/{\s*$/, ' {\n');
    });

    let depth = 0;
    const lines = formatted.split('\n');

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.includes('}') && !line.includes('{')) {
            depth = Math.max(0, depth - 1);
        }

        if (depth > 0 && !line.trim().startsWith('}')) {
            lines[i] = '  '.repeat(depth) + line.trim();
        } else {
            lines[i] = line.trim();
        }

        if (line.includes('{') && !line.includes('}')) {
            depth++;
        }
    }

    return lines.filter((line) => line.trim()).join('\n');
};
