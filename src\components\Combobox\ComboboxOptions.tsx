import { useCallback, useEffect } from 'react';
import { ComboboxOptionsProps } from './types';
import { useComboboxContext } from './context';

export const ComboboxOptions = ({ options, onSelect, selectedValue, emptyState }: ComboboxOptionsProps) => {
    const { setIsOpen, setSelectedValue, highlightedIndex, setHighlightedIndex, uniqueId, listRef } =
        useComboboxContext();

    useEffect(() => {
        setHighlightedIndex(-1);
    }, [options, setHighlightedIndex]);

    const handleSelect = useCallback(
        (value: string) => {
            setSelectedValue(value);
            onSelect(value);
            setIsOpen(false);
            setHighlightedIndex(-1);
        },
        [onSelect, setIsOpen, setSelectedValue, setHighlightedIndex],
    );

    if (options.length === 0 && emptyState) {
        return <>{emptyState}</>;
    }

    if (options.length === 0) {
        return (
            <div data-scope="combobox" data-part="empty">
                <span>No results</span>
            </div>
        );
    }

    return (
        <ul
            ref={listRef}
            id={`combobox-listbox-${uniqueId}`}
            data-scope="combobox"
            data-part="options"
            role="listbox"
            aria-labelledby={`combobox-input-${uniqueId}`}
        >
            {options.map((option, index) => {
                const isSelected = selectedValue === option.value;
                const isHighlighted = highlightedIndex === index;

                return (
                    <li
                        key={option.value}
                        id={`option-${option.value}-${uniqueId}`}
                        data-scope="combobox"
                        data-part="option"
                        data-highlighted={isHighlighted || undefined}
                        data-selected={isSelected || undefined}
                        data-disabled={option.disabled || undefined}
                        role="option"
                        aria-selected={isSelected}
                        aria-disabled={option.disabled}
                        onClick={() => {
                            if (!option.disabled) {
                                handleSelect(option.value);
                            }
                        }}
                        onMouseEnter={() => {
                            if (!option.disabled) {
                                setHighlightedIndex(index);
                            }
                        }}
                    >
                        <span data-scope="combobox" data-part="option-text">
                            {option.label}
                        </span>

                        {isSelected && (
                            <svg
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                focusable="false"
                                data-scope="combobox"
                                data-part="option-check"
                            >
                                <path
                                    d="M20.25 6.75L9.75 17.2495L4.5 12"
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                ></path>
                            </svg>
                        )}
                    </li>
                );
            })}
        </ul>
    );
};
