import { FC, useState } from 'react';
import { Button } from '@shopify/polaris';
import { PlusIcon, DeleteIcon } from '@shopify/polaris-icons';
import { useBuilderStore, useBlockStore } from '@giaminhautoketing/auto-builder';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { ButtonTrigger } from './components/ButtonTrigger';
import { TabsHeader } from './components/TabsHeader';
import { BackgroundImageSettings } from './components/BackgroundImageSettings';
import { BaseColorPickerV2 } from '../../base/BaseColorPickerV2';
import './styles.scss';
interface SettingsBackgroundColorProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
}
export const SettingsBackgroundColor: FC<SettingsBackgroundColorProps> = ({
    blockId,
    path,
    isUpdateConfigs,
    label,
}) => {
    const [active, setActive] = useState<boolean>(false);
    const [isDelete, setIsDelete] = useState<boolean>(false);

    const getBackgroundColorValue = (key: string) => {
        const fullPath = isUpdateConfigs ? `configs.${path}.${key}` : `${path}.${key}`;
        return isUpdateConfigs ? getBlockProperty(fullPath, blockId) : getBlockBPProperty(fullPath, blockId);
    };

    const typeValue = getBackgroundColorValue('type') as 'color' | 'image';
    const colorValue = getBackgroundColorValue('color') as string;
    const imageValue = getBackgroundColorValue('image') as { url: string | null } | undefined;

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChangeColor = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.color`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.color`, value);
        }
    };

    const handleChangeType = (type: 'color' | 'image') => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.type`, type);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.type`, type);
        }
    };

    const handleDeleteImage = () => {
        setIsDelete(true);
        setActive(false);
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.image.url`, '');
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.image.url`, '');
        }
    };

    const handleDeleteColor = () => {
        setIsDelete(true);
        setActive(false);
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.color`, 'rgba(255, 255, 255, 0)');
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.color`, 'rgba(255, 255, 255, 0)');
        }
    };

    const handleOpenPopover = () => {
        setActive(!active);
        if (isDelete || colorValue === 'rgba(255, 255, 255, 0)' || imageValue?.url === '') {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, `${path}.color`, 'rgba(0, 0, 0, 1)');
            } else {
                updateBlockProperty(blockId, currentDevice, `${path}.color`, 'rgba(0, 0, 0, 1)');
            }
            setIsDelete(false);
        }
    };

    const activator = (
        <ButtonTrigger
            type={typeValue}
            colorValue={colorValue}
            imageUrl={imageValue?.url ?? ''}
            blockId={blockId}
            isUpdateConfigs={isUpdateConfigs}
            path={path}
            onClick={handleOpenPopover}
        >
            {(typeValue === 'color' && colorValue !== 'rgba(255, 255, 255, 0)') ||
            (typeValue === 'image' && imageValue?.url !== '') ? (
                <div
                    onClick={(e) => e.stopPropagation()}
                    css={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                >
                    {typeValue === 'image' && <Button icon={DeleteIcon} variant="plain" onClick={handleDeleteImage} />}
                    {typeValue === 'color' && <Button icon={DeleteIcon} variant="plain" onClick={handleDeleteColor} />}
                </div>
            ) : (
                <Button icon={PlusIcon} variant="plain" onClick={handleOpenPopover} />
            )}
        </ButtonTrigger>
    );

    return (
        <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: label }}>
            <BaseColorPickerV2
                open={active}
                onOpenChange={setActive}
                color={colorValue ?? '#000000'}
                onChange={handleChangeColor}
                triggerRender={activator}
                extraHeaderRender={<TabsHeader type={typeValue} setType={handleChangeType} />}
                contentRender={
                    typeValue === 'image' ? (
                        <BackgroundImageSettings
                            imageUrl={imageValue?.url ?? ''}
                            blockId={blockId}
                            path={path}
                            isUpdateConfigs={isUpdateConfigs}
                            setActive={setActive}
                        />
                    ) : null
                }
            />
        </BaseItemLayout>
    );
};
