/* eslint-disable @typescript-eslint/no-explicit-any */

import { FC } from 'react';
import {
    DATA_SET_FORM_RADIO_ELEMENT,
    DATA_SET_FORM_RADIO_MULTI_ELEMENT,
    DATA_SET_FORM_RADIO_ELEMENT_WRAP,
    DATA_SET_FORM_RADIO_ELEMENT_INNER,
    DATA_SET_FORM_RADIO_LABEL,
    DATA_SET_FORM_FIELD_ID,
} from '@/components/builder/blocks/form/constants';

interface FormRadioProps {
    field: any;
    index: number;
    autoId: string;
}

export const FormRadio: FC<FormRadioProps> = ({ field, index, autoId }) => (
    <div
        {...{ [`${DATA_SET_FORM_RADIO_MULTI_ELEMENT}-${index}`]: autoId }}
        {...{ [`${DATA_SET_FORM_FIELD_ID}`]: field.id }}
    >
        {field.options?.map((option: any, optionIndex: number) => (
            <div key={`${field.key}-radio-${optionIndex}`} {...{ [`${DATA_SET_FORM_RADIO_ELEMENT}`]: autoId }}>
                <div {...{ [`${DATA_SET_FORM_RADIO_ELEMENT_WRAP}`]: autoId }}>
                    <div {...{ [`${DATA_SET_FORM_RADIO_ELEMENT_INNER}`]: autoId }}>
                        <input type="radio" defaultChecked={field.initialText === option.value} />
                    </div>
                    <label {...{ [`${DATA_SET_FORM_RADIO_LABEL}`]: autoId }}>{option.label}</label>
                </div>
            </div>
        ))}
    </div>
);
