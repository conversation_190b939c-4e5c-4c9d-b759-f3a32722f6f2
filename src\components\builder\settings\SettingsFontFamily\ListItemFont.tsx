import { FC } from 'react';
import { CheckIcon, DeleteIcon } from '@shopify/polaris-icons';
import { Divider, Icon, Text, Tooltip, InlineStack } from '@shopify/polaris';

interface ListItemFontProps {
    fonts: {
        id: string;
        label: string;
        shopId: string;
        type: string[];
        url: string;
        value: string;
    };
    selectedFont: string;
    handleFontClick: (font: string) => void;
    tabSelected: string;
    handleDeleteFont: (id: string) => void;
}

export const ListItemFont: FC<ListItemFontProps> = ({
    fonts,
    selectedFont,
    handleFontClick,
    tabSelected,
    handleDeleteFont,
}) => {
    return (
        <div className="list-item-font">
            <div
                className="list-item-font__item"
                style={{ fontFamily: fonts.value }}
                onClick={() => handleFontClick(fonts.value)}
            >
                <InlineStack blockAlign="center" align="space-between">
                    <Text as="span" variant="bodyMd" fontWeight="medium">
                        {fonts.label}
                    </Text>
                    <InlineStack gap="200" align="center">
                        {selectedFont === fonts.value && <Icon source={CheckIcon} />}
                    </InlineStack>
                </InlineStack>
            </div>
            <Divider />
            {tabSelected === 'uploaded' && selectedFont !== fonts.value && (
                <div className="list-item-font__delete" onClick={() => handleDeleteFont(fonts.id)}>
                    <Tooltip content="Delete font">
                        <Icon tone="critical" source={DeleteIcon} />
                    </Tooltip>
                </div>
            )}
        </div>
    );
};
