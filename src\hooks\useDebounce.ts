import { useEffect, useState } from "react";

export const useDebounce = <T>(value: T, delay: number, callbackfn?: (value: T) => void) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      callbackfn?.(value);
    }, delay);
    return () => {
      clearTimeout(handler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, delay]);
  return debouncedValue;
};
