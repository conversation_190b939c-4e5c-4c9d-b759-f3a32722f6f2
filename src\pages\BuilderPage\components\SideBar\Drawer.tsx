import { <PERSON><PERSON>tack, <PERSON>, Button, ButtonGroup, InlineStack, Scrollable, Text, Tooltip } from '@shopify/polaris';
import { QuestionCircleIcon, XIcon, ChevronLeftIcon } from '@shopify/polaris-icons';
import { FC, useEffect, useMemo, useRef } from 'react';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SideBarItemProps } from '@/pages/BuilderPage/types/sidebar';
import { useElement } from '@/pages/BuilderPage/hooks/useElement';
import { Preview } from '@/pages/BuilderPage/Previews/Preview';
import { ButtonSideBars } from '@/pages/BuilderPage/configs/sidebar';
import { BlockSettingsPanels } from '../BlockSettingsPanels';
import { useAppStore } from '@/stores/appStore';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { AddFieldPanel } from '@/components/builder/blocks/form/settings/Contents/AddFieldPanel';
import { FieldSettingsRenderer } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/FieldSettingsRenderer';
interface DrawerProps {
    activeItem: SideBarItemProps | null;
    onClose: () => void;
    setIsDrawerOpen: (isDrawerOpen: boolean) => void;
    handleSelectMenu: (id: string, items: SideBarItemProps[]) => void;
}

export const Drawer: FC<DrawerProps> = ({ activeItem, onClose, setIsDrawerOpen, handleSelectMenu }) => {
    const drawerRef = useRef<HTMLDivElement>(null);
    const { selectedElementItem, handleSelectItemElement, resetElementsState } = useElement();
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const selectBlock = useBuilderStore((state) => state.selectBlock);
    const blocks = useBlockStore((state) => state.blocks);
    const selectedFormField = useAppStore((state) => state.selectedFormField);
    const isAddNewFormField = useAppStore((state) => state.isAddNewFormField);
    const setSelectedFormField = useAppStore((state) => state.setSelectedFormField);
    const setIsAddNewFormField = useAppStore((state) => state.setIsAddNewFormField);

    const cName = useMemo(() => (selectedBlockId ? blocks[selectedBlockId]?.cname : null), [selectedBlockId, blocks]);

    const handleClose = () => {
        resetElementsState();
        onClose();
        selectBlock(null);
        setSelectedFormField(null as unknown as FormFieldType);
        setIsAddNewFormField(false);
    };

    useEffect(() => {
        if (selectedBlockId) {
            setIsDrawerOpen(true);
            setSelectedFormField(null as unknown as FormFieldType);
            setIsAddNewFormField(false);
            handleSelectMenu('elements', ButtonSideBars.items);
        }
    }, [selectedBlockId, setIsDrawerOpen, handleSelectMenu, setSelectedFormField, setIsAddNewFormField]);

    const renderSettingsPanel = () => {
        if (cName === 'page') return <div>Page settings</div>;
        if (cName === 'section') return <div>Section settings</div>;

        if (isAddNewFormField) {
            return <AddFieldPanel />;
        }

        if (selectedFormField) {
            return <FieldSettingsRenderer formField={selectedFormField} selectedBlockId={selectedBlockId} />;
        }
        if (selectedBlockId)
            return <BlockSettingsPanels selectedBlock={blocks[selectedBlockId]} id={selectedBlockId} />;

        if (activeItem?.drawerContent) {
            return (
                <activeItem.drawerContent
                    onItemClick={handleSelectItemElement}
                    selectedItem={selectedElementItem || undefined}
                />
            );
        }
        return null;
    };

    const title = useMemo(() => {
        if (isAddNewFormField) return 'Add field';
        if (selectedFormField) return selectedFormField.label;
        if (selectedBlockId) return blocks[selectedBlockId]?.label;
        return activeItem?.title;
    }, [selectedFormField, selectedBlockId, activeItem, blocks, isAddNewFormField]);

    return (
        <Box borderColor="border" borderStyle="solid" borderInlineEndWidth="025" position="relative" zIndex="3">
            <div className="builder-container__content__sidebar-drawer__content">
                <div className="builder-container__content__sidebar-drawer__content__header">
                    <InlineStack gap="200" align="space-between" blockAlign="center">
                        {selectedBlockId && !selectedFormField && !isAddNewFormField && (
                            <Button icon={ChevronLeftIcon} variant="tertiary" onClick={() => selectBlock(null)} />
                        )}
                        {selectedFormField && (
                            <Button
                                icon={ChevronLeftIcon}
                                variant="tertiary"
                                onClick={() => {
                                    setSelectedFormField(null as unknown as FormFieldType);
                                }}
                            />
                        )}
                        {isAddNewFormField && (
                            <Button
                                icon={ChevronLeftIcon}
                                variant="tertiary"
                                onClick={() => setIsAddNewFormField(false)}
                            />
                        )}
                        <Text as="h3" variant="headingMd">
                            {title}
                        </Text>
                    </InlineStack>
                    <InlineStack gap="200" align="space-between" blockAlign="center">
                        <ButtonGroup>
                            <Tooltip content="Help">
                                <Button icon={QuestionCircleIcon} variant="tertiary" />
                            </Tooltip>
                            <Button icon={XIcon} variant="tertiary" onClick={handleClose} />
                        </ButtonGroup>
                    </InlineStack>
                </div>
                <div className="builder-container__content__sidebar-drawer__content__body">
                    <div
                        className="builder-container__content__sidebar-drawer__content__body__elements"
                        ref={drawerRef}
                    >
                        {renderSettingsPanel()}
                    </div>
                    <div
                        className={`builder-container__content__sidebar-drawer__content__body__elements--preview ${
                            selectedElementItem ? 'visible' : ''
                        }`}
                    >
                        <Scrollable style={{ flex: 1, height: '100%' }}>
                            <Box
                                background="bg-surface"
                                borderColor="border"
                                borderStyle="solid"
                                borderInlineEndWidth="025"
                                minHeight="100%"
                                padding="200"
                                paddingBlockStart="300"
                                width="251px"
                                position="relative"
                            >
                                <BlockStack gap="400">
                                    <Text as="p" variant="headingMd">
                                        Content
                                    </Text>
                                    <Preview selectedElementItem={selectedElementItem} />
                                </BlockStack>
                            </Box>
                        </Scrollable>
                    </div>
                </div>
            </div>
        </Box>
    );
};
