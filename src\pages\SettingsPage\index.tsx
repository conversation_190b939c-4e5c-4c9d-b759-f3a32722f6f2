import { FC, useCallback, useEffect, useState } from 'react';
import { Page, Card, Layout, Listbox, Icon, InlineStack, AutoSelection, Text } from '@shopify/polaris';
import { HomeIcon, ResetIcon, ArchiveIcon } from '@shopify/polaris-icons';
import { AccountSettings } from './AccountSettings';
import { ResetSettings } from './ResetSettings';
import { ActivityLog } from './ActivityLog';

import { SkeletonPageSettings } from './skeleton/SkeletonPageSettings';
import { useAppBridge } from '@shopify/app-bridge-react';
export const SettingsPage: FC = () => {
    const [selected, setSelected] = useState('settings');

    const handleSelect = useCallback(
        (value: string) => {
            if (value === selected) return;
            setSelected(value);
        },
        [selected],
    );
    const [isLoading, setIsLoading] = useState(true);
    const shopify = useAppBridge();
    useEffect(() => {
        shopify.loading(true);
        setIsLoading(true);
        setTimeout(() => {
            shopify.loading(false);
            setIsLoading(false);
        }, 2000);
    }, [shopify]);

    return isLoading ? (
        <SkeletonPageSettings />
    ) : (
        <Page title="Settings">
            <Layout>
                <Layout.Section variant="oneThird">
                    <Card>
                        <Listbox onSelect={handleSelect} autoSelection={AutoSelection.FirstSelected}>
                            <Listbox.Action value="settings">
                                <InlineStack gap="200">
                                    <Icon source={HomeIcon} tone="base" />
                                    <Text as="h2" variant="headingSm">
                                        Account settings
                                    </Text>
                                </InlineStack>
                            </Listbox.Action>
                            <Listbox.Action value="reset">
                                <InlineStack gap="200">
                                    <Icon source={ResetIcon} />
                                    <Text as="h2" variant="headingSm">
                                        Reset
                                    </Text>
                                </InlineStack>
                            </Listbox.Action>
                            <Listbox.Action value="activity-log">
                                <InlineStack gap="200">
                                    <Icon source={ArchiveIcon} />
                                    <Text as="h2" variant="headingSm">
                                        Activity log
                                    </Text>
                                </InlineStack>
                            </Listbox.Action>
                        </Listbox>
                    </Card>
                </Layout.Section>
                {selected === 'settings' && <Layout.Section>{<AccountSettings />}</Layout.Section>}
                {selected === 'reset' && <Layout.Section>{<ResetSettings />}</Layout.Section>}
                {selected === 'activity-log' && <Layout.Section>{<ActivityLog />}</Layout.Section>}
            </Layout>
        </Page>
    );
};
