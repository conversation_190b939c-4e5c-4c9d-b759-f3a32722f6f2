.tabs-nav-item-icon[data-role='normal-icon'] {
    display: inline-flex;
    width: var(--atk-tabs-normal-icon-size);
    height: var(--atk-tabs-normal-icon-size);
    svg {
        width: 100%;
        height: 100%;
        fill: var(--atk-tabs-hover-icon-color, var(--atk-tabs-normal-icon-color));
    }
}

.tabs-nav-item-icon[data-role='selected-icon'] {
    display: inline-flex;
    width: var(--atk-tabs-selected-icon-size);
    height: var(--atk-tabs-selected-icon-size);
    svg {
        width: 100%;
        height: 100%;
        fill: var(--atk-tabs-hover-icon-color, var(--atk-tabs-selected-icon-color));
    }
}

.tabs-nav-item:not(.selected) {
    .tabs-nav-item-icon[data-role='selected-icon'] {
        display: none !important;
    }
}
.tabs-nav-item.selected {
    .tabs-nav-item-icon[data-role='normal-icon'] {
        display: none !important;
    }
}
