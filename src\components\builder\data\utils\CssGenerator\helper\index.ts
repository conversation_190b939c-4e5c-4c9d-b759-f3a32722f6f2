/* eslint-disable @typescript-eslint/no-explicit-any */
import { Typography, UnitValue } from '@/components/builder/data/utils/CssGenerator/types';

export const generateCssProperty = (property: string, value: string | number, unit: string = ''): string => {
    if (!value) return '';
    return `${property}: ${value}${unit};`;
};

export const generateCssWithVariable = (property: string, variables: string[] | string, fallback: string): string => {
    const varList = Array.isArray(variables) ? variables : [variables];
    const nestedVar = varList.reduceRight((acc, v) => `var(${v}, ${acc})`, fallback);
    return `${property}: ${nestedVar};`;
};

export const createTypographyTransformer =
    (prefix: string) =>
    (data: Record<string, any>): Typography => {
        const typography: any = {};
        const mappings = {
            fontSize: `${prefix}FontSize`,
            fontWeight: `${prefix}FontWeight`,
            fontFamily: `${prefix}FontFamily`,
            color: `${prefix}Color`,
            textTransform: `${prefix}TextTransform`,
            textDecoration: `${prefix}TextDecoration`,
            textAlign: `${prefix}TextAlign`,
            fontStyle: `${prefix}FontStyle`,
            textDirection: `${prefix}TextDirection`,
        };

        Object.entries(mappings).forEach(([key, value]) => {
            if (data[value]) {
                typography[key as keyof Typography] = data[value];
            }
        });

        return typography;
    };

export const generateUnitValue = (value: UnitValue): string => {
    if (!value) return '0px';
    if (value.val === 'normal') return 'normal';
    return `${value.val}${value.unit || ''}`;
};

export const generateSpacingValue = (spacing: UnitValue | { type: string; value: UnitValue }): string => {
    if (!spacing) return '';
    if ('type' in spacing) {
        return spacing.type === 'default' ? '' : generateUnitValue(spacing.value);
    }
    return generateUnitValue(spacing);
};
