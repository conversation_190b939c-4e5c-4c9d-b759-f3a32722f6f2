import { FC } from 'react';
import { InlineStack, Button, TextProps } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { genNewBoxShadowLayer, genNewTextShadowLayer } from '@/utils';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { SettingsShadowItem } from './ShadowItem';
import './styles.scss';
import { BaseItemLayout } from '../../base/BaseItemLayout';
type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];

interface SettingsShadowProps {
    type: 'text-shadow' | 'box-shadow';
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
}

export const SettingsShadow: FC<
    SettingsShadowProps & Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'>
> = ({ type, blockId, isUpdateConfigs, path, label, textProps, ...otherProps }) => {
    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const dataShadow = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const handleAddShadow = () => {
        const isTextShadow = type === 'text-shadow';
        const newShadowLayer = isTextShadow ? genNewTextShadowLayer() : genNewBoxShadowLayer();
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, newShadowLayer as unknown as Record<string, unknown>);
        } else {
            updateBlockProperty(blockId, currentDevice, path, newShadowLayer as unknown as Record<string, unknown>);
        }
    };

    const handleDeleteShadow = () => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, undefined as unknown as Record<string, unknown>);
        } else {
            updateBlockProperty(blockId, currentDevice, path, undefined as unknown as Record<string, unknown>);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <div className="settings-shadow">
                <InlineStack
                    align="space-between"
                    wrap={false}
                    blockAlign={type === 'text-shadow' ? 'center' : 'start'}
                >
                    <div className="settings-shadow__content">
                        {!dataShadow && (
                            <>
                                <Button size="large" onClick={handleAddShadow} variant="plain">
                                    + Add
                                </Button>
                            </>
                        )}
                        {dataShadow && Object.keys(dataShadow).length > 0 && (
                            <>
                                <SettingsShadowItem
                                    isUpdateConfigs={isUpdateConfigs}
                                    type={type}
                                    key={dataShadow.id}
                                    dataShadow={dataShadow}
                                    onDelete={handleDeleteShadow}
                                    blockId={blockId ?? ''}
                                    path={path}
                                />
                            </>
                        )}
                    </div>
                </InlineStack>
            </div>
        </BaseItemLayout>
    );
};
