import { useRTE } from '../context';
import { BaseTool } from './BaseTool';

export function ClearFormat() {
    const { editor } = useRTE();

    const customIcon = (
        <svg width="14" height="22" viewBox="0 0 14 22" fill="currentColor">
            <g clipPath="url(#clip0)">
                <path d="M0.4375 17.5625H13.5625" stroke="black" strokeLinecap="round" strokeLinejoin="round"></path>
                <path
                    d="M3.49999 15.375L1.05611 12.9311C0.892078 12.767 0.799927 12.5445 0.799927 12.3125C0.799927 12.0805 0.892078 11.858 1.05611 11.6939L7.69387 5.05611C7.85795 4.89208 8.08047 4.79993 8.31249 4.79993C8.54451 4.79993 8.76703 4.89208 8.93112 5.05611L12.9439 9.06887C13.1079 9.23295 13.2001 9.45547 13.2001 9.68749C13.2001 9.91951 13.1079 10.142 12.9439 10.3061L7.87499 15.375"
                    stroke="black"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0">
                    <rect width="14" height="14" transform="translate(0 4)"></rect>
                </clipPath>
            </defs>
        </svg>
    );

    return (
        <BaseTool
            tooltip="Clear Format"
            customIcon={customIcon}
            onClick={() => {
                editor?.commands.selectAll();
                editor?.chain().focus().unsetAllMarks().clearNodes().run();
            }}
        />
    );
}
