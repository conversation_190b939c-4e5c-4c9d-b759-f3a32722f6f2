export interface ActivityLogProps {
    id: number;
    shopId: number;
    action: {
        key: string;
        value: string | string[];
    };
    ip: {
        key: string;
        value: string;
    };
    description: {
        key: string;
        value: string | string[];
    };
    created_at: string;
}

export interface ActivityLogParams {
    currentPage: number;
    perPage: number;
}

export type PlanStatus = 'free' | 'pro';
