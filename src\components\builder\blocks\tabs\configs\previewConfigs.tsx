import { ReactNode } from 'react';
import { Auto_BlockToolbar } from '@giaminhautoketing/auto-builder';
import { ReactComponent as Tab1Icon } from '@/assets/svgs/w-tabs-1.svg';
import { ReactComponent as Tab2Icon } from '@/assets/svgs/w-tabs-2.svg';
import * as settings from './data';

export const previewConfigs: {
    label: string;
    icon: ReactNode;
    settings: Auto_BlockToolbar;
}[] = [
    {
        label: 'Horizontal',
        icon: <Tab1Icon />,
        settings: settings.tab1,
    },
    {
        label: 'Vertical',
        icon: <Tab2Icon />,
        settings: settings.tab2,
    },
];
