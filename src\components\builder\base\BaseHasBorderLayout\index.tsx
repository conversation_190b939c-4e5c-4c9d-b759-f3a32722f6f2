import { BlockStack, Box, Scrollable } from '@shopify/polaris';
import React, { FC } from 'react';
import clsx from 'clsx';
import './style.scss';

interface BaseHasBorderLayoutProps {
    children: React.ReactNode;
    hasElementID?: boolean;
    containerClassName?: string;
}

export const BaseHasBorderLayout: FC<BaseHasBorderLayoutProps> = ({
    children,
    containerClassName,
    hasElementID = false,
}) => {
    const childrenArray = React.Children.toArray(children);
    const lastIndex = childrenArray.length - 1;

    return (
        <div className={clsx('base-has-border-layout', containerClassName)}>
            <Scrollable
                style={{ maxHeight: 'calc(100% - 53px)', position: 'absolute', inset: 0, overflowX: 'hidden' }}
                scrollbarWidth="none"
            >
                <Box paddingBlockStart="100" paddingBlockEnd="400" paddingInline="200">
                    <BlockStack gap="0">
                        {childrenArray.map((child, index) => (
                            <Box
                                key={index}
                                paddingBlock="300"
                                borderBlockEndWidth={
                                    hasElementID
                                        ? index === 0 || index === lastIndex
                                            ? undefined
                                            : '025'
                                        : index === lastIndex
                                        ? undefined
                                        : '025'
                                }
                                borderColor="border"
                                borderStyle="solid"
                            >
                                {child}
                            </Box>
                        ))}
                    </BlockStack>
                </Box>
            </Scrollable>
        </div>
    );
};
