import { ColorPickerProvider } from './use-color-picker-context.ts';
import { useColorPicker, UseColorPickerProps } from './use-color-picker.ts';
import { ColorPickerTrigger } from './ColorPickerTrigger';
import { ColorPickerContent } from './ColorPickerContent';
import { ColorPickerHeader } from './ColorPickerHeader';
import { ColorPickerArea } from './ColorPickerArea';
import { ColorPickerEditArea } from './ColorPickerEditArea';
import './style.scss';

interface ColorPickerRootProps extends UseColorPickerProps {
    children?: React.ReactNode;
}

function ColorPickerRoot(props: ColorPickerRootProps) {
    const api = useColorPicker(props);
    return <ColorPickerProvider value={api}>{props.children}</ColorPickerProvider>;
}

ColorPickerRoot.Trigger = ColorPickerTrigger;
ColorPickerRoot.Content = ColorPickerContent;
ColorPickerRoot.Header = ColorPickerHeader;
ColorPickerRoot.Area = ColorPickerArea;
ColorPickerRoot.EditArea = ColorPickerEditArea;

export { ColorPickerRoot as BaseColorPicker };
