import { FC, memo, useCallback, useState, useMemo } from 'react';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { AppProvider, Box, Button, InlineGrid, Text, Tooltip } from '@shopify/polaris';
import { HeartIcon } from '@shopify/polaris-icons';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { ReactComponent as HeartFill } from '@/assets/svgs/heart-fill.svg';
import { ReactComponent as DesktopIcon } from '@/assets/svgs/desktop.svg';
import { ReactComponent as MobileIcon } from '@/assets/svgs/mobile.svg';
import { ItemTemplateProps, ScreenType } from './types';
import { dataConfigs, screenConfigs } from './configs';
import './styles.scss';

export const Preview: FC = memo(() => {
    const templatePreview = useAppStore((state) => state.templatePreview);
    const [data, setData] = useState<ItemTemplateProps>(dataConfigs);
    const [screen, setScreen] = useState<ScreenType>('desktop');

    const handleLike = useCallback(() => setData((prev) => ({ ...prev, favourite: !prev.favourite })), []);
    const handleScreen = useCallback((value: ScreenType) => setScreen(value), []);

    const screenConfig = useMemo(() => screenConfigs[screen], [screen]);

    const containerStyle = useMemo(
        () => ({
            overflow: screen === 'desktop' ? 'hidden' : 'auto',
            paddingTop: screen === 'desktop' ? '54px' : '34px',
            height: screenConfig.containerHeight,
            backgroundImage: screenConfig.backgroundImage,
        }),
        [screen, screenConfig],
    );

    const iframeStyle = useMemo(
        () => ({
            width: screenConfig.width,
            height: screenConfig.height,
            marginTop: screenConfig.marginTop,
            borderRadius: screen === 'desktop' ? 0 : '12px',
        }),
        [screen, screenConfig],
    );

    return (
        <Modal id="modal-preview-template" variant="max">
            <TitleBar title="Autoketing Builder">
                <button variant="primary">Use template</button>
            </TitleBar>
            <AppProvider i18n={{}}>
                <div className="preview-container">
                    <InlineGrid columns={3}>
                        <Text as="span">{templatePreview.name}</Text>
                        <InlineGrid gap="100" columns={3}>
                            <Button
                                variant={screen === 'desktop' ? undefined : 'tertiary'}
                                onClick={() => handleScreen('desktop')}
                                icon={<DesktopIcon />}
                            />
                            <Button
                                variant={screen === 'mobile' ? undefined : 'tertiary'}
                                onClick={() => handleScreen('mobile')}
                                icon={<MobileIcon />}
                            />
                        </InlineGrid>
                        <Box>
                            <Tooltip content="Favourite">
                                <Button onClick={handleLike} icon={data.favourite ? <HeartFill /> : HeartIcon} />
                            </Tooltip>
                        </Box>
                    </InlineGrid>
                    <div className="preview-content" style={containerStyle}>
                        <iframe style={iframeStyle} src={templatePreview.url} />
                    </div>
                </div>
            </AppProvider>
        </Modal>
    );
});
