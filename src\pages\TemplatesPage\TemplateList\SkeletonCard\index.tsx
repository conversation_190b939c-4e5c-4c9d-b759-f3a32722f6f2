import { FC } from 'react';
import { Box, Card, InlineStack, SkeletonBodyText, SkeletonDisplayText } from '@shopify/polaris';
import * as sc from '../styled';

export const SkeletonCard: FC = () => {
    return (
        <Card padding="0" roundedAbove="xs">
            <sc.CardContent>
                <sc.CardImageSkeleton />
                <Box paddingInline="400" paddingBlockStart="400" paddingBlockEnd="500">
                    <InlineStack align="space-between" blockAlign="center" wrap>
                        <Box width="50%">
                            <SkeletonBodyText lines={2} />
                        </Box>
                        <Box width="50%">
                            <InlineStack gap="100">
                                <Box width="20%" />
                                <Box width="20%">
                                    <SkeletonDisplayText size="small" />
                                </Box>
                                <Box width="50%">
                                    <SkeletonDisplayText size="small" />
                                </Box>
                            </InlineStack>
                        </Box>
                    </InlineStack>
                </Box>
            </sc.CardContent>
        </Card>
    );
};
