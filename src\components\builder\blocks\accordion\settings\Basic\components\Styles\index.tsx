import { FC } from 'react';
import { Icon, BlockStack, Box } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { SettingsShadow } from '@/components/builder/settings/SettingsShadow';
import { SettingsBackgroundColor } from '@/components/builder/settings';

interface StylesProps {
    id: string;
    label: string;
}

export const Styles: FC<StylesProps> = ({ id, label }) => {
    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <SettingsShadow type="box-shadow" blockId={id} path="boxShadow" label="Box shadow" />
                        <SettingsBackgroundColor path="background" blockId={id} label="Background color" />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
