.page-manager-activator {
    .Polaris-Text--root {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        max-width: 240px;
    }
    .Polaris-Button {
        .Polaris-Button__Icon {
            height: 20px;
        }
        &[data-state='open'] {
            .Polaris-Button__Icon svg[aria-hidden='true'] {
                transform: rotate(180deg);
            }
        }
    }
}
.page-manager {
    width: 350px;
    max-height: 420px;
    overflow: hidden;
    padding: 0.75rem;
    border-radius: var(--p-border-radius-200);
    > .Polaris-Labelled--hidden .Polaris-Connected {
        margin-bottom: 0.5rem;
        .Polaris-TextField__Backdrop {
            outline: none;
        }
    }
    .Polaris-ActionList__Item.Polaris-ActionList--active {
        background-color: #ebebeb;
        svg {
            fill: #4a4a4a;
        }
    }
    > .Polaris-Box {
        overflow-y: auto;
        max-height: 360px;
        .Polaris-Box {
            .Polaris-BlockStack--listReset {
                gap: 0.25rem;
            }
        }
    }
    .submenu-actions {
        display: none;
        gap: 0.5rem;
        span {
            display: grid;
            width: 20px;
            height: 20px;
            align-items: center;
            justify-content: center;
        }
        .<PERSON>is-<PERSON><PERSON> {
            padding: 0;
            min-width: 20px;
            min-height: 20px;
            width: 20px;
            height: 20px;
            border-radius: 6px;
            &:hover {
                background-color: #fff;
            }
        }
    }
    .submenu-container {
        border-radius: var(--p-border-radius-200);
        background-color: var(--p-color-bg-secondary);
        gap: 1rem;
        display: grid;

        &__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.5rem;
            .Polaris-Button {
                padding: 0;
                min-width: 20px;
                min-height: 20px;
                background-color: transparent;
            }
        }
        &__body {
            overflow-y: auto;
            max-height: 247px;
            .Polaris-Box {
                padding: 0 1px;
                ul {
                    gap: 0.25rem !important;
                    padding: 3px 0;
                    li {
                        &:hover {
                            .submenu-actions {
                                display: flex;
                            }
                        }
                    }
                }
            }

            .Polaris-Connected__Item.Polaris-Connected__Item--primary {
                max-width: 240px;
            }
            &__title {
                color: #303030;
                font-size: 13px;
                font-weight: 450;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
            }
        }
        &__footer {
            .Polaris-Button {
                margin: 0;
                padding: 6px;
                font-weight: 450;
            }
        }
        #renaming {
            padding: 0;
            background: #fff;
            max-height: 2rem;
            .Polaris-Connected {
                gap: 1rem;
            }
        }
    }
}
