import { SettingsShadowData } from '@/components/builder/settings/SettingsShadow/types';

export interface UnitValue {
    val: string;
    unit: string;
}

export interface Spacing {
    top: UnitValue;
    right: UnitValue;
    bottom: UnitValue;
    left: UnitValue;
}

export interface BorderRadius {
    'top-left': UnitValue;
    'top-right': UnitValue;
    'bottom-right': UnitValue;
    'bottom-left': UnitValue;
}

export interface Border {
    type: string;
    color: string;
    top: UnitValue;
    right: UnitValue;
    bottom: UnitValue;
    left: UnitValue;
    radius: BorderRadius;
}

export interface Background {
    type: string;
    color: string;
    image?: {
        url: string;
        repeat: string;
        position: string;
        attachment: string;
        fill: string;
    };
}

export interface Typography {
    fontSize: UnitValue;
    fontWeight: string;
    fontFamily: string;
    color: string;
    textTransform?: string;
    textDecoration?: string;
    textAlign?: string;
    fontStyle?: string;
    textDirection?: string;
    letterSpacing?: UnitValue | { type: string; value: UnitValue };
    lineHeight?: UnitValue | { type: string; value: UnitValue };
    textShadow?: SettingsShadowData;
    boxShadow?: SettingsShadowData;
    justifyContent?: string;
}

export interface FormSubmitStyle extends Typography {
    background: Background;
    fontSize: UnitValue;
    fontWeight: string;
    fontFamily: string;
    color: string;
    buttonLetterSpacing: { type: string; value: UnitValue };
    buttonLineSpacing: { type: string; value: UnitValue };
    buttonWidth: UnitValue;
    buttonHeight: UnitValue;
    buttonSpacing: {
        margin: Spacing;
        padding: Spacing;
    };
    buttonBorder: Border;
    textTransform: string;
    textDecoration: string;
    textAlign: string;
    fontStyle: string;
    buttonAlign: string;
}
export interface GeneralForm {
    spaceTitleField: UnitValue;
    space: {
        padding: {
            top: UnitValue;
            bottom: UnitValue;
            left: UnitValue;
            right: UnitValue;
        };
    };
    vertical: UnitValue;
    horizontal: UnitValue;
    align: string;
}
