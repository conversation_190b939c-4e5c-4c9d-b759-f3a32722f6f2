import { FC } from 'react';
import { TextF<PERSON>, Card, BlockStack, Text, Box } from '@shopify/polaris';
import { type FormField } from '../hooks/useFormField';
interface StorefrontPasswordCardProps {
    storefrontPasswordField: FormField<string>;
}
export const StorefrontPasswordCard: FC<StorefrontPasswordCardProps> = ({ storefrontPasswordField }) => {
    return (
        <Card padding="0">
            <Box paddingBlock="500" paddingInline="400">
                <BlockStack gap="400">
                    <Text as="h2" variant="headingSm">
                        Storefront password
                    </Text>
                    <TextField
                        label=""
                        value={storefrontPasswordField.value || ''}
                        onChange={storefrontPasswordField.handleChange}
                        autoComplete="storefront-password"
                        helpText="If your storefront has password protected, enter it here to allow us to show your theme in Autoketing Builder editor"
                        maxLength={100}
                        showCharacterCount={true}
                    />
                </BlockStack>
            </Box>
        </Card>
    );
};
