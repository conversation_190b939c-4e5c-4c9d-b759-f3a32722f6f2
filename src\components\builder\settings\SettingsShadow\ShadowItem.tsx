import { FC, useState } from 'react';
import { Popover, Button, Text, InlineStack } from '@shopify/polaris';
import { DeleteIcon, XIcon } from '@shopify/polaris-icons';
import { SettingsSliderInput } from '../SettingsSliderInput';
import { SettingsShadowData } from './types';
import { ReactComponent as Shadow } from '@/assets/svgs/shadow.svg';
import { textShadowPositionOptions, textShadowTypeOptions } from '@/components/builder/data/options';
import { SettingsColorPicker } from '../SettingsColorPicker';
import { SettingsSwitchTab } from '../SettingsSwitchTab';
interface SettingsShadowItemProps {
    type: 'text-shadow' | 'box-shadow';
    dataShadow: SettingsShadowData;
    onDelete: () => void;
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const SettingsShadowItem: FC<SettingsShadowItemProps> = ({
    dataShadow,
    onDelete,
    type,
    blockId,
    path,
    isUpdateConfigs,
}) => {
    const [active, setActive] = useState(false);
    const togglePopover = () => setActive((active) => !active);

    return (
        <div
            className={
                dataShadow.type === 'realistic'
                    ? 'settings-shadow__item settings-shadow__item--realistic'
                    : 'settings-shadow__item'
            }
        >
            <Popover
                active={active}
                preferredAlignment="right"
                activator={
                    <Button
                        onClick={togglePopover}
                        icon={<Shadow />}
                        accessibilityLabel="Other save actions"
                        size="large"
                        variant="tertiary"
                    >
                        {dataShadow.type === 'realistic'
                            ? 'Realistic'
                            : `${dataShadow.x.val},${dataShadow.y.val},${dataShadow.blur.val}${
                                  dataShadow.type === 'box' ? `,${dataShadow.spread?.val}` : ''
                              }`}
                    </Button>
                }
                autofocusTarget="first-node"
                onClose={togglePopover}
            >
                <div className="settings-shadow__item__popover">
                    <div className="settings-shadow__item__popover__title">
                        <Text as="span" variant="bodyMd">
                            {type === 'text-shadow' ? 'Text shadow' : 'Box shadow'}
                        </Text>
                        <Button
                            size="slim"
                            variant="tertiary"
                            icon={XIcon}
                            onClick={togglePopover}
                            accessibilityLabel="Close"
                        />
                    </div>

                    <div className="settings-shadow__item__popover__content">
                        {type === 'box-shadow' && (
                            <>
                                <SettingsSwitchTab
                                    label="Type"
                                    options={textShadowTypeOptions}
                                    path={`${path}.type`}
                                    blockId={blockId}
                                    isUpdateConfigs={isUpdateConfigs}
                                />

                                <SettingsSwitchTab
                                    label="Position"
                                    options={textShadowPositionOptions}
                                    path={`${path}.position`}
                                    blockId={blockId}
                                    isUpdateConfigs={isUpdateConfigs}
                                />
                            </>
                        )}

                        <InlineStack align="space-between" blockAlign="center" wrap={false}>
                            <Text as="span" variant="bodyMd">
                                Color
                            </Text>
                            <div onClick={(e) => e.stopPropagation()}>
                                <SettingsColorPicker
                                    path={`${path}.color`}
                                    blockId={blockId}
                                    isUpdateConfigs={isUpdateConfigs}
                                />
                            </div>
                        </InlineStack>
                        <SettingsSliderInput
                            min={-200}
                            path={`${path}.x`}
                            isShadow
                            blockId={blockId}
                            title="X"
                            isUpdateConfigs={isUpdateConfigs}
                        />
                        <SettingsSliderInput
                            min={-200}
                            path={`${path}.y`}
                            isShadow
                            blockId={blockId}
                            title="Y"
                            isUpdateConfigs={isUpdateConfigs}
                        />
                        {dataShadow.type !== 'realistic' && (
                            <SettingsSliderInput
                                max={100}
                                path={`${path}.blur`}
                                isShadow
                                blockId={blockId}
                                title="Blur"
                                isUpdateConfigs={isUpdateConfigs}
                            />
                        )}
                        {type === 'box-shadow' && dataShadow.type === 'box' && dataShadow.spread && (
                            <SettingsSliderInput
                                max={100}
                                path={`${path}.spread`}
                                isShadow
                                blockId={blockId}
                                isUpdateConfigs={isUpdateConfigs}
                                title="Spread"
                            />
                        )}
                        {dataShadow.type === 'realistic' && dataShadow.diffustion && (
                            <SettingsSliderInput
                                max={100}
                                path={`${path}.diffustion`}
                                isShadow
                                blockId={blockId}
                                isUpdateConfigs={isUpdateConfigs}
                                title="Diffusion"
                            />
                        )}
                        {dataShadow.type === 'realistic' && dataShadow.focus && (
                            <SettingsSliderInput
                                max={100}
                                path={`${path}.focus`}
                                isShadow
                                blockId={blockId}
                                isUpdateConfigs={isUpdateConfigs}
                                title="Focus"
                            />
                        )}
                    </div>
                </div>
            </Popover>
            <Button
                size="large"
                variant="tertiary"
                icon={DeleteIcon}
                onClick={onDelete}
                accessibilityLabel="Delete color"
            />
        </div>
    );
};
