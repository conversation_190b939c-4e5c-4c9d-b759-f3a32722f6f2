import { FC, useEffect } from 'react';
import { TextProps } from '@shopify/polaris';
import { BaseSlider, BaseInput, BaseItemLayout } from '@/components/builder/base';
import { getBlockProperty, getBlockBPProperty } from '@/utils/shared';
import { useSliderControl } from './hooks/useSliderControl';
import { useStoreUpdate } from './hooks/useStoreUpdate';
import './styles.scss';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseInputProps = Parameters<typeof BaseInput>[0];
type BaseSliderProps = Parameters<typeof BaseSlider>[0];

interface SettingsSliderInputProps {
    min?: number;
    max?: number;
    step?: number;
    isShadow?: boolean;
    isUpdateConfigs?: boolean;
    path: string;
    blockId: string;
    title?: string;
    isSeconds?: boolean;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    inputProps?: Omit<BaseInputProps, 'value' | 'onChange'>;
    sliderProps?: Omit<BaseSliderProps, 'value' | 'onChange'>;
    cssVariable?: string;
    selectedBlockTarget?: HTMLElement;
}

export const SettingsSliderInput: FC<
    SettingsSliderInputProps & Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'>
> = ({
    min = 0,
    max = 300,
    step = 1,
    isShadow = false,
    isUpdateConfigs = false,
    path,
    blockId,
    title,
    inputProps,
    sliderProps,
    textProps,

    cssVariable,
    selectedBlockTarget,
    ...otherProps
}) => {
    const { updateStoreData } = useStoreUpdate({ blockId, path, isUpdateConfigs });
    const sliderInputValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.val`, blockId)
        : getBlockBPProperty(`${path}.val`, blockId);

    const sliderInputSuffix = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.unit`, blockId)
        : getBlockBPProperty(`${path}.unit`, blockId);

    const {
        localValue,
        setLocalValue,
        handleSliderChange,
        handleInputChange,
        handleMouseUp,
        handleKeyDown,
        handleKeyUp,
        handleBlur,
    } = useSliderControl({
        min,
        max,
        initialValue: sliderInputValue,
        cssVariable: cssVariable as string,
        selectedBlockTarget: selectedBlockTarget as HTMLElement,
        sliderUnit: inputProps?.suffix as string | undefined,
        onValueChange: updateStoreData,
    });

    useEffect(() => {
        setLocalValue(sliderInputValue);
    }, [sliderInputValue, setLocalValue]);

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: title };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <div className="settings-slider-input__container__slider-input">
                <div onMouseUp={handleMouseUp}>
                    <BaseSlider
                        max={max}
                        min={min}
                        value={Number(localValue) || 0}
                        onChange={handleSliderChange}
                        step={step}
                        {...sliderProps}
                    />
                </div>
                <div onKeyDown={handleKeyDown} onKeyUp={handleKeyUp}>
                    <BaseInput
                        suffix={sliderInputSuffix}
                        isShadow={isShadow}
                        type="number"
                        value={localValue}
                        onChange={handleInputChange}
                        max={max}
                        min={min}
                        {...inputProps}
                        onBlur={handleBlur}
                    />
                </div>
            </div>
        </BaseItemLayout>
    );
};
