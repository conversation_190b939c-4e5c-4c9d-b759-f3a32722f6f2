import { FC } from 'react';
import ApexChart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';

interface TrafficChartProps {
    valueSessionData: { dimensionValues: string; metricValues: string }[];
}

export const TrafficChart: FC<TrafficChartProps> = ({ valueSessionData }) => {
    const monthMap = {
        '01': 'January',
        '02': 'February',
        '03': 'March',
        '04': 'April',
        '05': 'May',
        '06': 'June',
        '07': 'July',
        '08': 'August',
        '09': 'September',
        '10': 'October',
        '11': 'November',
        '12': 'December',
    };

    const categories = valueSessionData
        .map((item) => item.dimensionValues)
        .map((date) => {
            const month = monthMap[date.slice(4, 6) as keyof typeof monthMap];
            const day = date.slice(6, 8);
            return ` ${day} ${month}`;
        });
    const data = valueSessionData.map((item) => +item.metricValues);

    const chartData: ApexOptions = {
        chart: {
            type: 'area',
            foreColor: '#616161',
            zoom: { enabled: true },
            toolbar: {
                show: true,
                autoSelected: 'pan',
                tools: { download: false },
                offsetX: -20,
                offsetY: 20,
            },
            sparkline: {
                enabled: false,
            },
        },

        xaxis: {
            categories: categories,
            labels: {
                style: {
                    fontSize: '16px',
                },
            },
            tooltip: {
                enabled: false,
            },
            axisTicks: {
                show: false,
            },
            offsetY: 20,
        },
        yaxis: {
            tickAmount: 10,
            decimalsInFloat: 0,
            labels: {
                offsetX: 25,
                style: {
                    fontSize: '16px',
                },
            },
        },
        legend: {
            show: true,
            position: 'top',
            horizontalAlign: 'right',
            markers: {
                size: 1,
                offsetX: -5,
                offsetY: 1,
            },
            offsetY: -14,
            fontSize: '14px',

            itemMargin: {
                horizontal: 25,
            },
        },
        title: {
            text: 'Overview',
            style: {
                fontWeight: '600',
                fontSize: '1.25rem',
                color: '#303030',
            },
            offsetY: 28,
            offsetX: 30,
        },
        subtitle: {
            text: 'Number of traffics & unique visitors on the page',
            style: {
                fontSize: '13px',
            },
            offsetY: 43,
            offsetX: 30,
        },
        colors: ['#1BB4C524'],
        stroke: {
            width: 2,
        },
        dataLabels: {
            enabled: false,
        },
        grid: {
            show: true,
            strokeDashArray: 5,
            padding: {
                top: 40,
                right: 30,
                bottom: 25,
                left: 30,
            },
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                gradientToColors: ['#1BB4C5'],
                stops: [0, 100],
                colorStops: [
                    { offset: 0, color: '#1BB4C5', opacity: 1 },
                    { offset: 100, color: '#FFFFFF00', opacity: 1 },
                ],
            },
        },
        tooltip: {
            x: {
                show: false,
            },
        },
    };

    const series = [
        {
            name: 'Lượt truy cập',
            data: data,
        },
    ];

    return <ApexChart type="area" options={chartData} series={series} height={503} />;
};
