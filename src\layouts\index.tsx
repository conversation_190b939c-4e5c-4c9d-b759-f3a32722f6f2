import { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { useAppStore } from '@/stores';
import { NavMenu } from '@/components';
import { apiAddress } from '@/configs/apiAddress';
import { Preview } from '@/pages/LandingPage/Preview';
import { Preview as PreviewSection } from '@/pages/SectionsPage/Preview';
import { css } from '@emotion/react';

export const Layout = () => {
    const setToken = useAppStore((state) => state.setToken);
    const setIsAuthenticated = useAppStore((state) => state.setIsAuthenticated);

    useEffect(() => {
        try {
            fetch(apiAddress.login, {
                method: 'POST',
            })
                .then((res) => res.json())
                .then((res) => {
                    setToken(res.data);
                    setIsAuthenticated(true);
                });
        } catch (error) {
            console.log(error);
            setIsAuthenticated(false);
        }
    }, [setIsAuthenticated, setToken]);

    return (
        <div
            css={css`
                @media (min-width: 30.625em) {
                    .Polaris-Page {
                        padding-inline: 19px;
                    }
                }
            `}
        >
            <NavMenu />
            <Outlet />
            <Preview />
            <PreviewSection />
        </div>
    );
};
