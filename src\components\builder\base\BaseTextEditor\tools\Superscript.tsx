import { BaseTool } from './BaseTool';
import { useRTE } from '../context';

export const Superscript = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('superscript');
    const toggleSuperscript = () => {
        editor?.chain().focus().toggleSuperscript().run();
    };
    const superscriptIcon = (
        <svg width="14" height="22" viewBox="0 0 14 22" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3998_90462)">
                <path
                    d="M0.470703 8.36849V6.66626H8.98187V8.36849"
                    stroke="black"
                    strokeWidth="1.07692"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                ></path>
                <path
                    d="M4.72656 6.66626V16.0286"
                    stroke="black"
                    strokeWidth="1.07692"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                ></path>
                <path
                    d="M3.02441 16.0284H6.42888"
                    stroke="black"
                    strokeWidth="1.07692"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                ></path>
                <path
                    d="M13.6637 8.21856H11.0312V7.57342L11.9164 6.67719C12.17 6.41335 12.3356 6.23291 12.4134 6.13588C12.4776 6.05969 12.5322 5.97593 12.576 5.88651C12.6057 5.81742 12.621 5.74298 12.621 5.66777C12.6231 5.6262 12.616 5.5847 12.6002 5.54618C12.5845 5.50766 12.5605 5.47305 12.53 5.44477C12.4582 5.38603 12.3673 5.35573 12.2747 5.35966C12.1529 5.36159 12.0338 5.39533 11.9291 5.45754C11.788 5.54079 11.6555 5.63772 11.5333 5.74693L10.9912 5.1188C11.1358 4.98379 11.2938 4.86374 11.4627 4.76048C11.5927 4.6876 11.7318 4.63236 11.8763 4.59621C12.0434 4.55508 12.215 4.53562 12.387 4.53833C12.603 4.53493 12.8175 4.57728 13.0159 4.6626C13.1888 4.73742 13.3365 4.86031 13.4415 5.01666C13.5424 5.16773 13.5955 5.34567 13.5939 5.52733C13.5952 5.66087 13.5771 5.79389 13.5403 5.92225C13.5026 6.04827 13.4472 6.16833 13.3761 6.27887C13.2888 6.41082 13.1894 6.53421 13.0789 6.64741C12.9553 6.77564 12.6929 7.02047 12.2917 7.38191V7.40659H13.6637V8.21856Z"
                    fill="black"
                    fillOpacity="0.85"
                ></path>
            </g>
            <defs>
                <clipPath id="clip0_3998_90462">
                    <rect width="14" height="22" fill="white"></rect>
                </clipPath>
            </defs>
        </svg>
    );

    return (
        <BaseTool onClick={toggleSuperscript} tooltip="Superscript" isActive={isActive} customIcon={superscriptIcon} />
    );
};
