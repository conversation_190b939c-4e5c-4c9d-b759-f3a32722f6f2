import { useState } from 'react';
import { httpRequest } from '@/configs';

interface UseMutationOptions<T, R> {
    url: string;
    method?: 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    timeout?: number;
    onMutate?: (data: T) => unknown;
    onSuccess?: (data: R, context?: unknown) => void;
    onError?: (error: Error, context?: unknown) => void;
    onSettled?: (data: R | null, error: Error | null, context?: unknown) => void;
}

interface UseMutationResult<T, R> {
    mutate: (data: T) => Promise<void>;
    isLoading: boolean;
    error: Error | null;
    data: R | null;
}

export function useMutation<T, R = unknown>({
    url,
    method = 'POST',
    headers,
    timeout,
    onMutate,
    onSuccess,
    onError,
    onSettled,
}: UseMutationOptions<T, R>): UseMutationResult<T, R> {
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const [data, setData] = useState<R | null>(null);

    const mutate = async (body: T) => {
        setIsLoading(true);
        setError(null);

        let context;

        if (onMutate) {
            context = onMutate(body);
        }

        try {
            const response = await httpRequest({
                url,
                method,
                data: body,
                timeout,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers,
                },
            });

            setData(response.data);
            onSuccess?.(response.data, context);
            return response.data;
        } catch (err) {
            const error = err instanceof Error ? err : new Error('An unknown error occurred');
            setError(error);
            onError?.(error, context);
            throw error;
        } finally {
            setIsLoading(false);
            onSettled?.(data, error, context);
        }
    };

    return { mutate, isLoading, error, data };
}
