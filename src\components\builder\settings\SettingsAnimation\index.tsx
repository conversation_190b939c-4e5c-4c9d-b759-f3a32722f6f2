import { BlockStack, Box, BoxProps } from '@shopify/polaris';
import { FC, useEffect, useRef } from 'react';
import { SettingsSliderInput } from '../SettingsSliderInput';
import { SettingsSelect } from '../SettingsSelect';
import { buttonAnimationLoopOptions, buttonAnimationTypeOptions } from '@/components/builder/data/options';
import { getBlockProperty } from '@/utils/shared';

interface SettingsAnimationProps {
    path: string;
    blockId: string;
    isUpdateConfigs: boolean;
    boxProps?: BoxProps;
}

export const SettingsAnimation: FC<SettingsAnimationProps> = ({ path, blockId, isUpdateConfigs, boxProps }) => {
    const animationType = getBlockProperty(`configs.${path}.type`, blockId);
    const prevAnimationTypeRef = useRef(animationType);
    const dom = document.querySelector(`[data-auto-id-inner="${blockId}"]`) as HTMLElement;

    useEffect(() => {
        // Only apply animation if the type has changed
        if (prevAnimationTypeRef.current !== animationType) {
            dom.onanimationend = () => {
                dom.classList.remove(`animate__${animationType}`, 'has-animate');
            };
            dom.classList.add(`animate__${animationType}`, 'has-animate');
            prevAnimationTypeRef.current = animationType;
        }
    }, [animationType, dom]);

    return (
        <Box paddingBlockStart="300" {...boxProps}>
            <BlockStack gap="300">
                <SettingsSelect
                    options={buttonAnimationTypeOptions}
                    path={`${path}.type`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Type"
                />

                <SettingsSelect
                    options={buttonAnimationLoopOptions}
                    path={`${path}.loop`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    label="Loop"
                />

                <SettingsSliderInput
                    path={`${path}.duration`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    isSeconds
                    title="Duration"
                    direction="column"
                    min={0}
                    max={10}
                    step={0.1}
                    inputProps={{
                        suffix: 's',
                        min: 0,
                        step: 0.1,
                    }}
                />

                <SettingsSliderInput
                    path={`${path}.delay`}
                    blockId={blockId}
                    isUpdateConfigs={isUpdateConfigs}
                    isSeconds
                    title="Delay"
                    direction="column"
                    min={0}
                    max={10}
                    step={0.1}
                    inputProps={{
                        suffix: 's',
                        min: 0,
                        step: 0.1,
                    }}
                />
            </BlockStack>
        </Box>
    );
};
