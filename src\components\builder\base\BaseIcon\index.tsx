import { FC, useEffect, useState } from 'react';
import { Icon as PolarisIcon } from '@shopify/polaris';
import { fetchSvgFromUrl } from '@/utils/fetchSvgFromUrl';
type Tone = 'success' | 'critical' | 'caution' | 'subdued' | 'emphasis' | 'magic' | 'info' | 'base';
type IconSource = React.ComponentType<React.SVGProps<SVGSVGElement>> | string;

export const DATA_SET_ICON = 'data-atk-id-icon';
interface BaseIconProps {
    source: IconSource;
    color?: string;
    size?: number | string;
    autoId?: string;
}

export const BaseIcon: FC<BaseIconProps> = ({ source, color = 'base', size = 20, autoId }) => {
    const [svgContent, setSvgContent] = useState<string | null>(null);

    useEffect(() => {
        if (typeof source === 'string' && source.trim().startsWith('http')) {
            fetchSvgFromUrl(source).then((content) => {
                setSvgContent(content);
            });
        }
    }, [source]);

    if (!source) {
        return null;
    }

    if (typeof source === 'string') {
        if (source.trim().includes('<svg')) {
            const modifiedSvg = source
                .replace(/width="[^"]*"/, `width="16"`)
                .replace(/height="[^"]*"/, `height="16"`)
                .replace(/<svg[^>]*>/, `<svg style="width: 100%; height: 100%;" $&>`)
                .replace(
                    /(<(path|rect|circle|polygon|ellipse|g|mask)[^>]*?)\s*(fill="[^"]*")?([^>]*>)/g,
                    (_, startTag, _tag, _fillAttr, endTag) => {
                        return `${startTag} fill="${color}"${endTag}`;
                    },
                );

            return (
                <span
                    {...{ [DATA_SET_ICON]: autoId }}
                    css={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: `var(--atk-button-icon-size, ${size}px) `,
                        height: `var(--atk-button-icon-size, ${size}px) `,
                        flexShrink: 0,
                    }}
                    dangerouslySetInnerHTML={{ __html: modifiedSvg }}
                />
            );
        }

        if (source.trim().startsWith('http') && svgContent) {
            const modifiedSvg = svgContent
                .replace(/width="[^"]*"/, `width="16"`)
                .replace(/height="[^"]*"/, `height="16"`)
                .replace(/<svg[^>]*>/, `<svg style="width: 100%; height: 100%;" $&>`)
                .replace(
                    /(<(path|rect|circle|polygon|ellipse|g|mask)[^>]*?)\s*(fill="[^"]*")?([^>]*>)/g,
                    (_, startTag, _tag, _fillAttr, endTag) => {
                        return `${startTag} fill="${color}"${endTag}`;
                    },
                );

            return (
                <span
                    {...{ [DATA_SET_ICON]: autoId }}
                    css={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: `var(--atk-button-icon-size, ${size}px) `,
                        height: `var(--atk-button-icon-size, ${size}px) `,
                    }}
                    dangerouslySetInnerHTML={{ __html: modifiedSvg }}
                />
            );
        }
        return <PolarisIcon source={source} tone={color as Tone} />;
    }

    const SvgComponent = source;
    return <SvgComponent width={size} height={size} color={color} />;
};
