.preview-container {
    > .Polaris-InlineGrid {
        display: flex;
        align-items: center;
        padding: 13px 16px;
        height: 53px;
        border-bottom: 1px solid #e3e3e3;
        position: fixed;
        width: 100%;
        background: #fff;
        span.Polaris-Text--root {
            font-size: 14px;
            font-weight: 550;
            color: #000;
            flex: 1;
        }

        .Polaris-InlineGrid {
            .Polaris-Button {
                width: 28px;
                height: 28px;
                margin: 0;
                .Polaris-Button__Icon {
                    height: 20px;
                }
            }
        }

        .Polaris-Button__Icon {
            height: 20px;
        }
        > .Polaris-Box {
            flex: 1;
            text-align: end;
            &.<PERSON>is-Text--root {
                text-align: start;
            }
        }
    }
    .preview-content {
        height: calc(100vh - 54px);
        overflow: hidden;
        transition: all 0.2s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center 68px;
        iframe {
            border: none;
            transition: all 0.2s ease-in-out;
        }
    }
}
