import { FC } from 'react';
import { Icon, Text, BlockStack, Link, Tooltip } from '@shopify/polaris';
import * as sc from './styled';
import { SupportChannelsProps } from './configs';

export const ItemChannel: FC<SupportChannelsProps> = ({ name, icon, description, link, tooltip }) => {
    return (
        <Link target="_blank" url={link} monochrome={true} removeUnderline={true}>
            <sc.ChannelItemWrapper
                backgroundColor="var(--p-color-bg-surface)"
                cornerRadius="12px"
                padding="22px 16px 14px"
                boxShadow="var(--p-shadow-button)"
            >
                <BlockStack align="start" gap="300">
                    <sc.ChannelLinkContainer maxHeight={24}>
                        <Link target="_blank" url={link}>
                            <sc.ChannelIconContainer iconSize={24} display="inline-block" fillColor="#6086F2">
                                <Tooltip content={tooltip}>
                                    <Icon source={icon} tone="info" />
                                </Tooltip>
                            </sc.ChannelIconContainer>
                        </Link>
                    </sc.ChannelLinkContainer>

                    <BlockStack gap="100">
                        <Text variant="headingMd" as="h3">
                            {name}
                        </Text>
                        <Text as="p" tone="subdued">
                            {description}
                        </Text>
                    </BlockStack>
                </BlockStack>
            </sc.ChannelItemWrapper>
        </Link>
    );
};
