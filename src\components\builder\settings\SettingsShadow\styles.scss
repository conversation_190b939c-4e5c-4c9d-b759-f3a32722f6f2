.settings-shadow {
    .Polaris-InlineStack > .<PERSON>is-<PERSON><PERSON> {
        margin: 0;
        padding: 0;
    }

    &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: 140px;
        border-radius: 0.5rem;
        border: 1px solid #8a8a8a;
        height: 2rem;
        > div {
            width: 100%;
            height: 2rem;
        }
        .<PERSON>is-<PERSON><PERSON> {
            justify-content: start;
            column-gap: 0.75rem;
            margin: 0;
            border-radius: 0;
            width: 100%;
            background-color: transparent;
            padding: 0.25rem;
            .Polaris-Button__Icon {
                width: 1.5rem;
                height: 1.5rem;
                .Polaris-Icon {
                    width: 1.5rem;
                    height: 1.5rem;
                }
            }
            .Polaris-Text--medium {
                font-size: 0.75rem;
            }
            &.<PERSON>is-<PERSON><PERSON>--iconOnly {
                border-left: 1px solid #8a8a8a;
                max-width: 2rem;
            }
        }
        &__popover {
            width: 16.6875rem;
            height: 100%;
            background-color: #fff;
            border-radius: 0.5rem;
            border: 1px solid #ebebeb;
            &__title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid #ebebeb;
                padding: 0.75rem;
            }
            &__content {
                padding: 1rem 0.75rem;
                display: flex;
                flex-direction: column;
                gap: 1rem;
                .switch-tabs__container--list {
                    max-width: 100%;
                    .switch-tabs__container--list--button {
                        padding: 7px 24px;
                        width: 70px;
                    }
                }
            }
        }
        &--realistic {
            min-width: 140px;
        }
    }
    &__content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: flex-end;
    }
}
