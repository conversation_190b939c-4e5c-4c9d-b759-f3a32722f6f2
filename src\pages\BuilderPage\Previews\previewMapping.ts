import { FC } from 'react';
import { Auto_BlockStructure, Auto_BlockToolbar } from '@giaminhautoketing/auto-builder';
import { TextPreview } from '@/components/builder/blocks/text/previews';
import { ShapePreview } from '@/components/builder/blocks/shape/previews';
import { ButtonPreview } from '@/components/builder/blocks/button/previews';
import { ImagePreview } from '@/components/builder/blocks/image/previews';
import { HtmlCodePreview } from '@/components/builder/blocks/htmlCode/previews';
import { VideoPreview } from '@/components/builder/blocks/video/previews';
import { FormPreview } from '@/components/builder/blocks/form/previews';
import { BreadcrumbPreview } from '@/components/builder/blocks/breadcrumb/previews';
import { TabsPreview } from '@/components/builder/blocks/tabs/previews';

import { AccordionPreview } from '@/components/builder/blocks/accordion/previews';
export const previewMapping: Record<string, FC<{ element: Auto_BlockStructure; settings: Auto_BlockToolbar }>> = {
    text: TextPreview,
    button: ButtonPreview,
    shape: ShapePreview,
    image: ImagePreview,
    'html-code': HtmlCodePreview,
    video: VideoPreview,
    form: FormPreview,
    accordion: AccordionPreview,
    breadcrumbs: BreadcrumbPreview,
    tabs: TabsPreview,
};
