import { useEffect } from 'react';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { CssSystem } from '@/stores/appStore/cssSystem';
import { buttonCssGenerators } from '@/components/builder/blocks/button/templates/generatorCss';
import { shapeCssGenerators } from '@/components/builder/blocks/shape/templates/generatorCss';
import { pageCssGenerators } from '@/components/builder/blocks/page/templates/generatorCss';
import { sectionCssGenerators } from '@/components/builder/blocks/section/templates/generatorCss';
import { getFormCssGenerators } from '@/components/builder/blocks/form/templates/generatorCss';
export const usePageStyles = () => {
    const cssSystem = CssSystem.getInstance();

    useEffect(() => {
        cssSystem.registerCSSGenerator({
            page: pageCssGenerators,
            section: sectionCssGenerators,
            button: buttonCssGenerators,
            shape: shapeCssGenerators,
            form: getFormCssGenerators,
        });
        cssSystem.rebuild();
    }, [cssSystem]);

    useEffect(() => {
        const unsubscribe = useBlockStore.subscribe(
            (state) => state.blocks,
            (blocks) => {
                Object.keys(blocks).forEach((blockId) => {
                    cssSystem.updateCssRules(blockId, blocks[blockId]);
                });
                cssSystem.applyCss();
            },
        );
        return () => unsubscribe();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
};
