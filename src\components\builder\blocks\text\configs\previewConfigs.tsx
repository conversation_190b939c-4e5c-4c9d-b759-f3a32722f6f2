import { ReactNode } from 'react';
import { Auto_BlockToolbar } from '@giaminhautoketing/auto-builder';
import { ReactComponent as HeadingIcon } from '@/assets/svgs/heading.svg';
import { ReactComponent as ParagraphIcon } from '@/assets/svgs/paragraph.svg';
import * as settings from './data';

export const previewConfigs: {
    label: string;
    icon: ReactNode;
    settings: Auto_BlockToolbar;
}[] = [
    {
        label: 'Heading',
        icon: <HeadingIcon />,
        settings: settings.heading,
    },
    {
        label: 'Paragraph',
        icon: <ParagraphIcon />,
        settings: settings.paragraph,
    },
];
