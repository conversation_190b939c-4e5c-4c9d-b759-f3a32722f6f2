import { FC } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';

interface EventsProps {
    path?: string;
    blockId?: string;
    isUpdateConfigs?: boolean;
    label: string;
}

export const Events: FC<EventsProps> = ({
    // path, blockId, isUpdateConfigs,
    label,
}) => {
    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300"></Box>
        </BaseCollapse>
    );
};
