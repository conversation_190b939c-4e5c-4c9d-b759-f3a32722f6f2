import { Route, Routes as ReactRouterRoutes } from 'react-router-dom';
import { pathnames } from '@/configs';
import { Layout } from '@/layouts';
import * as pages from '@/pages';
// import { Builder } from '@/pages/Builder';
// import { ErrorBoundary } from '@/components';
import BuilderPage from '@/pages/BuilderPage';
// export const router = createBrowserRouter([
//     {
//         path: pathnames.dashboard,
//         element: <Layout />,
//         ErrorBoundary,
//         children: [
//             {
//                 index: true,
//                 element: <pages.HomePage />,
//             },
//             {
//                 path: pathnames.pages,
//                 element: <pages.LandingPage />,
//             },
//             {
//                 path: pathnames.createPage,
//                 element: <pages.CreatePage />,
//             },
//             {
//                 path: pathnames.sections,
//                 element: <pages.SectionsPage />,
//             },
//             {
//                 path: pathnames.sectionsCreate,
//                 element: <pages.SectionsCreatePage />,
//             },
//             {
//                 path: pathnames.templates,
//                 element: <pages.TemplatesPage />,
//             },
//             {
//                 path: pathnames.optimization,
//                 element: <pages.OptimizationSEOPage />,
//             },
//             {
//                 path: pathnames.plans,
//                 element: <pages.PlansPage />,
//             },
//             {
//                 path: pathnames.settings,
//                 element: <pages.SettingsPage />,
//             },
//             {
//                 path: pathnames.analytics,
//                 element: <pages.Analytics />,
//             },
//         ],
//     },

//     { path: '*', element: <pages.NotFoundPage /> },
// ]);

export default function Routes() {
    const pageArr = [
        { path: pathnames.dashboard, index: true, element: <pages.HomePage /> },
        { path: pathnames.pages, element: <pages.LandingPage /> },
        { path: pathnames.createPage, element: <pages.CreatePage /> },
        { path: pathnames.sections, element: <pages.SectionsPage /> },
        { path: pathnames.sectionsCreate, element: <pages.SectionsCreatePage /> },
        { path: pathnames.templates, element: <pages.TemplatesPage /> },
        { path: pathnames.optimization, element: <pages.OptimizationSEOPage /> },
        { path: pathnames.plans, element: <pages.PlansPage /> },
        { path: pathnames.settings, element: <pages.SettingsPage /> },
        { path: pathnames.analytics, element: <pages.Analytics /> },
    ];
    return (
        <ReactRouterRoutes>
            <Route element={<Layout />}>
                {pageArr.map(({ path, index, element }) => (
                    <Route key={path} path={path} element={element} index={index} />
                ))}
                <Route path="*" element={<pages.NotFoundPage />} />
            </Route>
            <Route path="/builder" element={<BuilderPage />} />
        </ReactRouterRoutes>
    );
}
