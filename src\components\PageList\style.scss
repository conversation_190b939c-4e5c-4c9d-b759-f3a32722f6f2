.page-list {
    display: block;
    &__resource {
        &__empty--search {
            padding: 32px 0 200px;
            img {
                width: 226px;
                height: 226px;
            }
        }

        &__empty--data {
            padding: 12px 0 70px;
            img {
                width: 226px;
                height: 226px;
            }
        }

        &__pagination {
            display: flex;
            align-items: center;
            justify-content: end;
            flex-direction: row;
            padding: 16px 16px 20px;
            border-top: 0.0625rem solid rgba(227, 227, 227, 1);
            .Polaris-Button--disabled {
                background-color: rgba(0, 0, 0, 0.04);
            }
        }
    }

    &__error {
        padding: 12px 0 70px;
    }

    .Polaris-ResourceList {
        &__HeaderWrapper {
            padding-inline: 16px;

            @media (min-width: 48em) {
                min-height: 44px;
            }
        }

        &__HeaderContentWrapper {
            left: 16px;
        }
    }

    .Polaris-ResourceItem {
        // &__CheckboxWrapper {
        //     overflow: hidden;
        // }

        > .Polaris-Box {
            padding: 17px 16px;
        }
    }

    .Polaris-IndexFilters {
        &__IndexFiltersWrapper {
            .Polaris-Filters__FiltersWrapper {
                height: auto !important;
                overflow: visible !important;

                .Polaris-Filters-FilterPill__ToggleButton {
                    height: auto;
                    min-height: 22px;

                    .Polaris-Text--root {
                        text-align: start;
                        text-wrap: auto;
                    }
                }
            }

            .Polaris-TextField__Input {
                font-size: var(--p-font-size-325);
                line-height: var(--p-font-line-height-500);
            }
        }
    }

    .Polaris-Tabs__DisclosureActivator {
        padding: 0 var(--p-space-300);

        .Polaris-Text--root {
            display: none;
        }
    }
}
