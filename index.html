<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/vite.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Autoketing Landing Page Builder</title>
        <link rel="preconnect" href="https://cdn.shopify.com/" />
        <link rel="stylesheet" href="https://cdn.shopify.com/static/fonts/inter/v4/styles.css" />
        <script type="module">
            if (!import.meta.env || !import.meta.env.PROD) {
                // Because the base HTML is rendered in the server side, we need to manually embed the code to enable HMR in our
                // code, so that the vite server is properly enabled to run HMR
                const script = document.createElement('script');
                script.setAttribute('type', 'module');
                script.setAttribute('src', './dev_embed.js');
                document.getElementsByTagName('head')[0].append(script);
            }
        </script>
        <meta name="shopify-api-key" content="%VITE_SHOPIFY_API_KEY%" />
        <script src="https://cdn.shopify.com/shopifycloud/app-bridge.js"></script>
    </head>
    <body>
        <div id="root"></div>
        <script type="module" src="/src/main.tsx"></script>
    </body>
</html>
