import { FC } from 'react';
import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER } from '@giaminhautoketing/auto-builder';
import videoPlaceholder from '@/assets/svgs/video-placeholder.svg?url';
import { getVideoEmbedCode } from '../helper';

export const VideoViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const embedCode = getVideoEmbedCode(configs.url as string, {
        showControls: configs.showControls as boolean,
        autoplay: false,
        muted: false,
        loop: false,
    });

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            bpConfigs={bpConfigs}
            configs={configs}
            css={{ overflow: 'hidden' }}
        >
            {embedCode ? (
                <div
                    dangerouslySetInnerHTML={{ __html: embedCode }}
                    style={{ width: '100%', height: '100%', pointerEvents: 'none' }}
                />
            ) : (
                <img
                    src={videoPlaceholder}
                    alt="Video"
                    style={{ width: '100%', height: '100%', objectFit: 'cover', pointerEvents: 'none' }}
                />
            )}
        </BlockViewer>
    );
};
