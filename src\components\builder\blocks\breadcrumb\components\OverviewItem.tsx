import { FC, useEffect, useState } from 'react';
import './style.scss';
import { SettingsToggle } from '@/components/builder/settings/SettingsToggle';
import { BlockStack, Icon } from '@shopify/polaris';
import { SettingsInput } from '@/components/builder/settings';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { InfoIcon } from '@shopify/polaris-icons';

const DEFAULT_PATH = {
    CONTENT: 'content',
};
type Props = {
    isUpdateConfigs?: boolean;
    isCollapse: boolean;
    id: string;
};

const OverflowToggle: FC<Props> = ({ isUpdateConfigs, id, isCollapse }) => {
    const { updateBlockConfigsProperty, updateBlockProperty } = useBlockStore();
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const [selected, setSelected] = useState(isCollapse ? 'Collapse' : 'Wrap');

    useEffect(() => {
        setSelected(isCollapse ? 'Collapse' : 'Wrap');
    }, [isCollapse]);

    return (
        <div className="toggle-container">
            <h2 className="toggle-title">Overflow items</h2>
            <div className="toggle-buttons">
                <button
                    onClick={() => {
                        setSelected('Wrap');
                        if (isUpdateConfigs) {
                            updateBlockConfigsProperty(id, 'content.isCollapse', false);
                        } else {
                            updateBlockProperty(id, currentDevice, 'content.isCollapse', false);
                        }
                    }}
                    className={`toggle-button ${selected === 'Wrap' ? 'selected' : ''}`}
                >
                    <div className="icon">
                        <div className="bar"></div>
                        <div className="bar"></div>
                        <div className="bar"></div>
                        <div className="bar"></div>
                    </div>
                    Wrap
                </button>

                <button
                    onClick={() => {
                        setSelected('Collapse');
                        if (isUpdateConfigs) {
                            updateBlockConfigsProperty(id, 'content.isCollapse', true);
                        } else {
                            updateBlockProperty(id, currentDevice, 'content.isCollapse', true);
                        }
                    }}
                    className={`toggle-button ${selected === 'Collapse' ? 'selected' : ''}`}
                >
                    <div className="icon">
                        <div className="bar"></div>
                        <div className="dot"></div>
                        <div className="bar"></div>
                        <div className="bar"></div>
                    </div>
                    Collapse
                </button>
            </div>
            <div style={{ marginTop: '16px' }}>
                {selected !== 'Collapse' && (
                    <div className="padding-block-start-300">
                        <BlockStack gap="400">
                            <SettingsToggle
                                key={'showHome' + id}
                                label={'Show home'}
                                path={`${DEFAULT_PATH.CONTENT}.showHome`}
                                blockId={id}
                                isUpdateConfigs
                                toggleProps={{ id: 'showHome' }}
                                tooltipContent="Show the homepage on all pages"
                                hasTooltip
                                tooltipChildren={<Icon source={InfoIcon} />}
                            />
                            <SettingsToggle
                                key={'showCurrentPage' + id}
                                label={'Show current page'}
                                path={`${DEFAULT_PATH.CONTENT}.showLastItem`}
                                blockId={id}
                                isUpdateConfigs
                                toggleProps={{ id: 'showCurrentPage' }}
                            />
                            <SettingsToggle
                                key={'hideWhenNotParent' + id}
                                label={
                                    <span>
                                        Hide the breadcrumbs when <br /> there isn't a parent page
                                    </span>
                                }
                                path={`${DEFAULT_PATH.CONTENT}.hideWhenNotParent`}
                                blockId={id}
                                isUpdateConfigs
                                toggleProps={{ id: 'hideWhenNotParent' }}
                                tooltipContent="Choose this option if you want breadcrumbs on all pages, except the homepage"
                                hasTooltip
                                tooltipChildren={<Icon source={InfoIcon} />}
                            />
                        </BlockStack>
                    </div>
                )}
                {selected === 'Collapse' && (
                    <BlockStack gap="400">
                        <SettingsInput
                            path={`${DEFAULT_PATH.CONTENT}.beforeEllipsis`}
                            label="Before Ellipsis"
                            blockId={id}
                            isUpdateConfigs={isUpdateConfigs}
                            inputProps={{ min: 0, type: 'number' }}
                        />
                        <SettingsInput
                            path={`${DEFAULT_PATH.CONTENT}.afterEllipsis`}
                            label="After Ellipsis"
                            blockId={id}
                            isUpdateConfigs={isUpdateConfigs}
                            inputProps={{ min: 0, type: 'number' }}
                        />
                    </BlockStack>
                )}
            </div>
        </div>
    );
};

export default OverflowToggle;
