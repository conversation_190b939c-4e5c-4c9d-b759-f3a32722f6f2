import { FC } from 'react';
import { Card, BlockStack, Text, Select, Box } from '@shopify/polaris';
import { type FormField } from '../hooks/useFormField';
interface LanguageCardProps {
    languageField: FormField<string>;
}
const LANGUAGE_OPTIONS = [{ label: 'English', value: 'en' }];
export const LanguageCard: FC<LanguageCardProps> = ({ languageField }) => {
    return (
        <Card padding="0">
            <Box paddingBlock="500" paddingInline="400">
                <BlockStack gap="400">
                    <Text as="h2" variant="headingSm">
                        Language
                    </Text>
                    <Select
                        label=""
                        options={LANGUAGE_OPTIONS}
                        onChange={languageField.handleChange}
                        value={languageField.value || 'en'}
                    />
                </BlockStack>
            </Box>
        </Card>
    );
};
