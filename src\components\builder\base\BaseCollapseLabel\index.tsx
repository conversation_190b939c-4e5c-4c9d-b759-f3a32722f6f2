import { InlineStack, Text, TextProps } from '@shopify/polaris';
import { FC } from 'react';
import clsx from 'clsx';
export interface BaseCollapseLabelProps {
    label: string;
    textProps?: TextProps;
    containerClassName?: string;
    onClick?: () => void;
    children?: React.ReactNode;
}

export const BaseCollapseLabel: FC<BaseCollapseLabelProps> = ({
    label,
    textProps,
    containerClassName,
    onClick,
    children,
}) => {
    return (
        <div
            className={clsx('base-collapse-label', containerClassName)}
            onClick={onClick}
            style={{ cursor: 'pointer' }}
        >
            <InlineStack align="space-between">
                <Text variant="bodyMd" as="p" fontWeight="medium" {...textProps}>
                    {label}
                </Text>
                <InlineStack>{children}</InlineStack>
            </InlineStack>
        </div>
    );
};
