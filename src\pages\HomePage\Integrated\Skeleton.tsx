import { FC } from 'react';
import { Card, InlineStack, SkeletonBodyText, SkeletonDisplayText, SkeletonThumbnail } from '@shopify/polaris';
import styled from '@emotion/styled';

export const Skeleton: FC = () => {
    const Container = styled.div`
        width: 100%;
        padding: 10px 0;
    `;

    const ContentLeft = styled.div`
        width: 45%;
        display: flex;
        flex-direction: column;
        row-gap: 20px;
        .Polaris-SkeletonThumbnail {
            width: 80px;
            height: 80px;
        }
        .Polaris-SkeletonDisplayText__DisplayText {
            width: 20%;
        }
    `;
    const ContentRight = styled.div`
        width: 31%;
        .Polaris-SkeletonThumbnail--sizeLarge {
            width: 100%;
            height: 100%;
        }
    `;
    const ContentRightContent = styled.div`
        width: 65%;
        display: flex;
        flex-direction: column;
        row-gap: 16px;
    `;

    return (
        <Card>
            <Container>
                <InlineStack gap="200" align="space-between">
                    <ContentLeft>
                        <SkeletonBodyText lines={2} />
                        <InlineStack gap="200" align="start">
                            <SkeletonThumbnail size="medium" />
                            <ContentRightContent>
                                <SkeletonBodyText lines={1} />
                                <SkeletonBodyText lines={2} />
                            </ContentRightContent>
                        </InlineStack>
                        <InlineStack gap="200" align="start">
                            <SkeletonDisplayText size="small" />
                            <SkeletonDisplayText size="small" />
                        </InlineStack>
                    </ContentLeft>
                    <ContentRight>
                        <SkeletonThumbnail size="large" />
                    </ContentRight>
                </InlineStack>
            </Container>
        </Card>
    );
};
