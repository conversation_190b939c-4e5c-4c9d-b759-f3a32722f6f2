import { HTMLAttributes, ReactNode } from 'react';
import {
    CollisionDetection,
    DropAnimation,
    MeasuringConfiguration,
    Modifiers,
    PointerActivationConstraint,
    UniqueIdentifier,
} from '@dnd-kit/core';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { AnimateLayoutChanges, arrayMove } from '@dnd-kit/sortable';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SortableItemRequireProps = { id: UniqueIdentifier } & Record<string, any>;

export type RendererProps<T extends SortableItemRequireProps> = {
    index: number;
    data: T;
    onRemove?: () => void;
    clone?: boolean;
    isDragging?: boolean;
    insertPosition?: 'before' | 'after';
    handleProps?: SyntheticListenerMap;
};

export interface SortableProps<T extends SortableItemRequireProps> {
    items: T[];
    onChange: (data: T[]) => void;
    itemRenderer: (props: RendererProps<T>) => ReactNode;
    extendNode?: ReactNode;
    containerProps?: HTMLAttributes<HTMLDivElement>;
    useDragOverlay?: boolean;
    handle?: boolean;
    modifiers?: Modifiers;
    adjustScale?: boolean;
    dropAnimation?: DropAnimation;
    measuring?: MeasuringConfiguration;
    activationConstraint?: PointerActivationConstraint;
    animateLayoutChanges?: AnimateLayoutChanges;
    collisionDetection?: CollisionDetection;
    reorderItems?: typeof arrayMove;
    itemProps?: HTMLAttributes<HTMLDivElement>;
}

export interface SortableItemProps<T extends SortableItemRequireProps> {
    index: number;
    data: T;
    itemRenderer: (props: RendererProps<T>) => ReactNode;
    onRemove?: () => void;
    clone?: boolean;
    handle?: boolean;
    dragOverlay?: boolean;
    animateLayoutChanges?: AnimateLayoutChanges;
    elementProps?: HTMLAttributes<HTMLDivElement>;
}
