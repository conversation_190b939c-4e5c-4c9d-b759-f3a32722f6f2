import { FC } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsCheckbox, SettingsSliderInput, SettingsRTE } from '@/components/builder/settings';
import { InfoIcon } from '@shopify/polaris-icons';
import { BaseItemLayout } from '@/components/builder/base';
import { useFormFieldPaths } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';

interface FreeTextSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget?: HTMLElement;
}

export const FreeTextSettings: FC<FreeTextSettingsProps> = ({ formField, selectedBlockTarget }) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH_FORM } = useFormFieldPaths(formField);

    return (
        <Box paddingBlockStart="100" paddingBlockEnd="400" paddingInline="200">
            <BlockStack gap="200">
                <SettingsInput
                    path={`${DEFAULT_PATH_FORM}.label`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    inputProps={{ type: 'text', placeholder: 'Enter label' }}
                    direction="column"
                    label="Title"
                    textProps={{ fontWeight: 'medium' }}
                    tooltipContent="Add field titles to help customers identify information more easily"
                    hasTooltip
                    tooltipChildren={<Icon source={InfoIcon} />}
                />
                <BaseItemLayout
                    direction="column"
                    textProps={{ as: 'p', fontWeight: 'medium', variant: 'bodyMd', children: 'Description' }}
                    tooltipChildren={<Icon source={InfoIcon} />}
                    hasTooltip
                    tooltipContent="Add detailed content to provide more information to your customers"
                    containerClassName="form-rte-container"
                >
                    <SettingsRTE
                        path={`${DEFAULT_PATH_FORM}.description`}
                        blockId={selectedBlockId}
                        isUpdateConfigs
                        isFreeText
                    />
                </BaseItemLayout>

                <SettingsCheckbox
                    path={`${DEFAULT_PATH_FORM}.separateLine`}
                    blockId={selectedBlockId}
                    isUpdateConfigs
                    direction="column"
                    label="Separate line for this field"
                />

                <SettingsSliderInput
                    selectedBlockTarget={selectedBlockTarget as HTMLElement}
                    cssVariable={`--form-${formField.key}-width`}
                    path={`fieldSizes.${formField.key}.fieldWidth`}
                    blockId={selectedBlockId}
                    isUpdateConfigs={false}
                    direction="column"
                    title="Field width"
                    sliderProps={{
                        min: 0,
                        max: 100,
                        step: 1,
                    }}
                    inputProps={{
                        suffix: '%',
                        min: 0,
                        max: 100,
                        step: 1,
                    }}
                />
            </BlockStack>
        </Box>
    );
};
