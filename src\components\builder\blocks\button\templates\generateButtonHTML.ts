/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockData, DATA_SET_AUTO_ID, DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';
import { DATA_SET_BUTTON_TEXT } from '@/components/builder/blocks/button/constants';
import { fetchSvgFromUrl } from '@/utils/fetchSvgFromUrl';

export const generateButtonHTML = async (blockId: string, block: Auto_BlockData) => {
    const { type, text, icon } = block.configs.content as any;
    const { animation } = block.configs as any;

    let iconContent = null;
    if (icon) {
        if (typeof icon === 'string' && icon.trim().startsWith('http') && icon.endsWith('.svg')) {
            iconContent = await fetchSvgFromUrl(icon);
        } else {
            iconContent = icon;
        }
    }

    let buttonHTML = '';
    switch (type) {
        case 'icon-only':
            buttonHTML = `<button class="atk-button" data-atk-animation="${
                animation.type !== 'none'
            }" ${DATA_SET_AUTO_ID_INNER}="${blockId}" ${DATA_SET_BUTTON_TEXT}="${blockId}">
            ${
                iconContent ? `<span class="atk-button-icon"">${iconContent}</span>` : ''
            } <span class="atk-button-text">${text}</span> 
      </button>`;
            break;
        case 'text-only':
            buttonHTML = `<button class="atk-button" data-atk-animation="${
                animation.type !== 'none'
            }" ${DATA_SET_AUTO_ID_INNER}="${blockId}" ${DATA_SET_BUTTON_TEXT}="${blockId}">
            <span class="atk-button-text">${text}</span>  
            </button>`;
            break;
        case 'text-and-icon':
            buttonHTML = `<button class="atk-button" data-atk-animation="${
                animation.type !== 'none'
            }" ${DATA_SET_AUTO_ID_INNER}="${blockId}" ${DATA_SET_BUTTON_TEXT}="${blockId}">
          ${
              iconContent ? `<span class="atk-button-icon">${iconContent}</span>` : ''
          } <span class="atk-button-text">${text}</span> 
      </button>`;
            break;
        default:
            buttonHTML = `<button class="atk-button" data-atk-animation="${
                animation.type !== 'none'
            }" ${DATA_SET_AUTO_ID_INNER}="${blockId}" ${DATA_SET_BUTTON_TEXT}="${blockId}"/>`;
    }

    return `
        <div class="atk-button-wrapper" ${DATA_SET_AUTO_ID}="${blockId}" style="cursor: pointer;">${buttonHTML}</div>
    `;
};
