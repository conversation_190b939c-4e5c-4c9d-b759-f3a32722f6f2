import { CSSProperties } from 'react';
import { getResponsiveValue } from '@/stores/appStore/cssSystem';
import type { Generator } from '@/stores/appStore/cssSystem';

export const sectionCssGenerators: Generator[] = [
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const position = getResponsiveValue<string>(blockData, 'position', breakpoint);
            return {
                display: 'block',
                ...(position && { position: position as CSSProperties['position'] }),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const innerDisplay = getResponsiveValue<string>(blockData, 'innerDisplay', breakpoint);
            const gridTemplateColumns = getResponsiveValue<Array<{ val: string; unit: string }>>(
                blockData,
                'gridTemplateColumns',
                breakpoint,
            );
            const minHeight = getResponsiveValue<{ val: string; unit: string }>(blockData, 'minHeight', breakpoint);
            const maxWidth = getResponsiveValue<{ val: string; unit: string }>(blockData, 'maxWidth', breakpoint);

            return {
                display: innerDisplay || 'block',
                ...(gridTemplateColumns && {
                    gridTemplateColumns: gridTemplateColumns.map((col) => `${col.val}${col.unit}`).join(' ') || '1fr',
                }),
                gridTemplateRows: 'auto',
                ...(minHeight?.val && { minHeight: `${minHeight.val}${minHeight.unit || 'px'}` }),
                ...(maxWidth?.val && { maxWidth: `${maxWidth.val}${maxWidth.unit || 'px'}` }),
            };
        },
        applyTo: 'all',
    },
];
