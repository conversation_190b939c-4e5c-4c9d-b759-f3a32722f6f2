import { FC } from 'react';
import {
    Auto_BlockViewer,
    BlockViewer,
    DATA_SET_VIEWER,
    DATA_SET_AUTO_ID_INNER,
    useBuilderStore,
} from '@giaminhautoketing/auto-builder';
import { useAppStore } from '@/stores/appStore';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
} from '@/components/builder/constants/constants';

export const ShapeViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const setIsOpenModalShape = useAppStore((state) => state.setIsOpenModalShape);

    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleDoubleClick = () => {
        setIsOpenModalShape(true);
    };

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            bpConfigs={bpConfigs}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            configs={configs}
            css={{
                overflow: 'hidden',
            }}
        >
            <div
                {...{ [DATA_SET_AUTO_ID_INNER]: autoId }}
                {...(currentDevice === 'desktop'
                    ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                    : {})}
                {...(currentDevice === 'mobile' ? { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile } : {})}
                style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    ...((configs?.content as { type?: string })?.type === 'svg' && {
                        maskImage: `url("data:image/svg+xml,${encodeURIComponent(
                            (configs.content as { svg: string }).svg,
                        )}")`,
                        WebkitMaskImage: `url("data:image/svg+xml,${encodeURIComponent(
                            (configs.content as { svg: string }).svg,
                        )}")`,
                        maskSize: '100% 100%',
                        WebkitMaskSize: '100% 100%',
                    }),
                }}
                onDoubleClick={handleDoubleClick}
            />
        </BlockViewer>
    );
};
