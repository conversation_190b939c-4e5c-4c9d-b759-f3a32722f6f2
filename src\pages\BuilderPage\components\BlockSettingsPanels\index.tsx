import { FC, useEffect, useMemo, useState } from 'react';
import { Box } from '@shopify/polaris';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { getBlockRelated, getDefaultActiveTab } from '@/pages/BuilderPage/utils';
import { SettingTabsInspector } from '@/components/builder/settings/SettingTabsInspector';

interface BlockSettingsPanelsProps {
    selectedBlock: Auto_BlockData;
    id: string;
}

export const BlockSettingsPanels: FC<BlockSettingsPanelsProps> = ({ selectedBlock }) => {
    const [activeTab, setActiveTab] = useState(getDefaultActiveTab(selectedBlock?.type || 'basic'));

    const blockRelated = useMemo(() => getBlockRelated(selectedBlock?.type), [selectedBlock]);

    useEffect(() => {
        setActiveTab(getDefaultActiveTab(selectedBlock?.type));
    }, [selectedBlock?.type]);

    return (
        <>
            <Box
                paddingInline="200"
                paddingBlock="300"
                borderColor="border"
                borderStyle="solid"
                borderBlockEndWidth="025"
                minHeight="52px"
            >
                <SettingTabsInspector
                    activeTab={activeTab}
                    onChange={(val) => setActiveTab(val)}
                    selectedBlock={selectedBlock}
                />
            </Box>
            <>{blockRelated[activeTab as keyof typeof blockRelated]}</>
        </>
    );
};
