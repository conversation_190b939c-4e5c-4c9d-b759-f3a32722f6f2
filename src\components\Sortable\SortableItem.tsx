import { useEffect } from 'react';
import { UniqueIdentifier } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableItemProps } from './types';

export function SortableItem<T extends { id: UniqueIdentifier }>({
    index,
    clone,
    dragOverlay,
    data,
    handle,
    onRemove,
    animateLayoutChanges,
    itemRenderer,
    elementProps,
}: SortableItemProps<T>) {
    const { listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging, activeIndex, overIndex } =
        useSortable({
            id: data.id,
            animateLayoutChanges,
        });

    const insertPosition =
        overIndex === index && overIndex !== activeIndex && activeIndex !== -1
            ? overIndex > activeIndex
                ? 'after'
                : 'before'
            : undefined;

    useEffect(() => {
        if (!(isDragging && !dragOverlay)) return;

        document.body.style.cursor = 'grabbing';
        return () => {
            document.body.style.cursor = '';
        };
    }, [dragOverlay, isDragging]);

    return (
        <div
            ref={setNodeRef}
            style={{
                transition: transition,
                transform: CSS.Transform.toString(transform),
                cursor: clone ? 'grabbing' : 'unset',
            }}
            {...elementProps}
            {...(!handle ? listeners : undefined)}
        >
            {itemRenderer({
                index,
                data,
                onRemove,
                clone,
                isDragging,
                insertPosition,
                handleProps: handle ? { ref: setActivatorNodeRef, ...listeners } : undefined,
            })}
        </div>
    );
}
