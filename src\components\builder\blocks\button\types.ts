/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockType } from '@giaminhautoketing/auto-builder';

export type UnitValue = { val: string; unit?: string };

interface BorderRadius {
    'top-left': UnitValue;
    'top-right': UnitValue;
    'bottom-right': UnitValue;
    'bottom-left': UnitValue;
}

interface BorderStyle {
    color?: string;
    top?: UnitValue;
    right?: UnitValue;
    bottom?: UnitValue;
    left?: UnitValue;
    type?: string;
}

interface BackgroundImage {
    url: string;
    repeat: string;
    position: string;
    attachment: string;
    fill: string;
}

interface Background {
    type: 'color' | 'image';
    color?: string;
    image?: BackgroundImage;
}

interface TextStyle {
    fontSize?: UnitValue;
    fontWeight?: string;
    fontFamily?: string;
    color: string;
    textTransform?: string;
    textDecoration?: string;
    textAlign?: string;
    fontStyle?: string;
    textDirection?: string;
    lineHeight?: UnitValue;
    letterSpacing?: UnitValue;
    textShadow?: any;
}

export interface ButtonBreakpointConfig {
    backgroundButton: Background;

    width: UnitValue;
    height: UnitValue;
    position: {
        positionType: 'default' | 'sticky';
        stickTo: {
            stickToType: 'top' | 'bottom';
            top?: UnitValue;
            bottom?: UnitValue;
        };
    };
    boxShadow?: any;
    typography: TextStyle;
    borderButton: BorderStyle & { radius?: BorderRadius };
    buttonsSpace?: {
        padding: {
            top: UnitValue;
            right: UnitValue;
            bottom: UnitValue;
            left: UnitValue;
        };
    };
}

export interface ButtonConfigs {
    content: {
        type: 'text-and-icon' | 'text-only' | 'icon-only';
        text: string;
        icon?: string | undefined;
        iconColor?: string | undefined;
        iconSize?: UnitValue | undefined;
        direction?: 'row' | 'column';
        reverse?: boolean;
        spacing?: UnitValue;
    };
    displayOnDesktop: boolean;
    displayOnMobile: boolean;
    animation: {
        type: string;
        duration: UnitValue;
        loop: string;
        delay: UnitValue;
    };
    customCSS: {
        enable: boolean;
        className: string;
        style: string;
    };
    events: Record<string, any>;
}

export interface ButtonSettings {
    id: string;
    cname: string;
    label: string;
    type: Auto_BlockType;
    bpConfigs: {
        desktop: ButtonBreakpointConfig;
        mobile: ButtonBreakpointConfig;
    };
    configs: ButtonConfigs;
    overlay: {
        desktop: { width: number; height: number };
        mobile: { width: number; height: number };
    };
}
