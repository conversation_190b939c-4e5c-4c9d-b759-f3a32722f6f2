import {
    useRef,
    useEffect,
    useState,
    FocusEvent,
    ChangeEvent,
    ForwardRefRenderFunction,
    forwardRef,
    HTMLAttributes,
} from 'react';
import { validHex } from '@uiw/color-convert';

export interface ChanelInputProps extends Omit<HTMLAttributes<HTMLInputElement>, 'onChange'> {
    value?: string | number;
    onChange?(e: ChangeEvent<HTMLInputElement>, value: string | number): void;
}

const ChanelInputFR: ForwardRefRenderFunction<HTMLInputElement, ChanelInputProps> = (
    { value: valueProps, onChange, onBlur, ...otherProps },
    ref,
) => {
    const isFocus = useRef(false);
    const [value, setValue] = useState<string | number | undefined>(valueProps);

    useEffect(() => {
        if (valueProps !== value) {
            if (!isFocus.current) {
                setValue(valueProps);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [valueProps]);

    const handleChange = (e: FocusEvent<HTMLInputElement>, valInit?: string) => {
        try {
            const value = valInit || e.target.value;
            if (validHex(value)) {
                if (onChange) {
                    onChange(e, value);
                }
            }
            const val = Number(String(value).replace(/%/g, ''));
            if (!isNaN(val)) {
                if (onChange) {
                    onChange(e, val);
                }
            }
            setValue(value);
        } catch (error) {
            console.log((error as Error).message);
        }
    };

    const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
        isFocus.current = false;
        setValue(valueProps);
        if (onBlur) {
            onBlur(e);
        }
    };

    return (
        <input
            ref={ref}
            autoComplete="off"
            css={{
                width: '37px',
                height: '100%',
                fontFamily: 'var(--p-font-family-sans)',
                fontSize: '12px',
                fontWeight: '450',
                outline: 'none',
                border: 'none',
                flexShrink: 0,
                borderRight: '1px solid #ebebeb',
            }}
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            onFocus={() => (isFocus.current = true)}
            {...otherProps}
        />
    );
};

export const ChanelInput = forwardRef(ChanelInputFR);
