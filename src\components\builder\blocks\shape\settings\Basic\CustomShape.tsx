import { FC } from 'react';
import { Box, TextProps } from '@shopify/polaris';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseInput } from '@/components/builder/base/BaseInput';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseInputProps = Parameters<typeof BaseInput>[0];

interface CustomShapeProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
    label: string;
    inputProps?: Omit<BaseInputProps, 'value' | 'onChange'>;
    textProps?: Omit<Partial<TextProps>, 'children'>;
}

export const CustomShape: FC<CustomShapeProps> = ({ path, blockId, inputProps, textProps, ...otherProps }) => {
    const defaultTextProps = { as: 'p', variant: 'bodyMd' };

    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const styleValue = getBlockProperty(`configs.${path}.svg`, blockId);

    const handleStyleChange = (value: string) => {
        updateBlockConfigsProperty(blockId, `${path}.svg`, value);
    };

    return (
        <Box paddingBlockStart="300">
            <BaseItemLayout
                textProps={{ ...defaultTextProps, ...textProps, children: 'Custom shape' } as TextProps}
                direction="column"
                {...otherProps}
            >
                <BaseInput
                    type="text"
                    value={styleValue}
                    onChange={handleStyleChange}
                    placeholder="<svg>...</svg>"
                    multiline={4}
                    maxHeight={100}
                    {...inputProps}
                />
            </BaseItemLayout>
        </Box>
    );
};
