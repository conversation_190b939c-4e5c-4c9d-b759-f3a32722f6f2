import { FC, useCallback, useMemo, useState } from 'react';
import { ChevronDownIcon, XSmallIcon } from '@shopify/polaris-icons';
import { Box, Button, ButtonGroup, Card, Collapsible, InlineGrid, Text, Tooltip } from '@shopify/polaris';
import { StepStartContentItem } from './StepStartContentItem';
import { stepStartConfigs } from './configs';
import * as sc from './styled';

export const StepStart: FC = () => {
    const [open, setOpen] = useState(false);
    const [openCollapsible, setOpenCollapsible] = useState(false);
    const [dataStep, setDataStep] = useState(stepStartConfigs);

    const onHide = () => {
        setOpen(true);
    };

    const handleCheck = useCallback((id: string) => {
        setDataStep((prevData) => prevData.map((item) => (item.id === id ? { ...item, loading: true } : item)));
        setTimeout(() => {
            setDataStep((prevData) =>
                prevData.map((item) =>
                    item.id === id ? { ...item, isChecked: !item.isChecked, loading: false } : item,
                ),
            );
        }, 500);
    }, []);

    const onOpenItem = useCallback((id: string) => {
        setDataStep((prevData) =>
            prevData.map((item) => {
                if (item.id === id) {
                    if (item.isCollapsible) {
                        return item;
                    }
                    return { ...item, isCollapsible: true };
                }
                return { ...item, isCollapsible: false };
            }),
        );
    }, []);

    const handleCollapsable = useCallback(() => setOpenCollapsible(!openCollapsible), [openCollapsible]);
    const completedTasks = useMemo(() => dataStep.filter((item) => item.isChecked).length, [dataStep]);
    const progressWidth = useMemo(() => `${completedTasks * 25}%`, [completedTasks]);

    return (
        <sc.StepStartContainer isHidden={open}>
            <Card>
                <sc.StepStartHeader>
                    <InlineGrid columns="1fr auto">
                        <Text as="h2" variant="headingSm">
                            Get started with Autoketing Builder
                        </Text>
                        <ButtonGroup>
                            <Tooltip content="Close">
                                <Button variant="plain" onClick={onHide} icon={XSmallIcon} />
                            </Tooltip>
                            <Tooltip content={openCollapsible ? 'Collapse' : 'Expand'}>
                                <Button variant="plain" onClick={handleCollapsable} icon={ChevronDownIcon} />
                            </Tooltip>
                        </ButtonGroup>
                    </InlineGrid>
                    <Text tone="subdued" as="span">
                        Basic yet important steps to start using and raise your conversion rate.
                    </Text>
                    <sc.ProgressBar>
                        <Text tone="subdued" as="p">
                            {completedTasks} of 4 tasks completed
                        </Text>
                        <sc.CustomProgressBar>
                            <sc.ProgressIndicator style={{ width: progressWidth }} />
                        </sc.CustomProgressBar>
                    </sc.ProgressBar>
                </sc.StepStartHeader>
                <Collapsible
                    open={openCollapsible}
                    id="step-start-collapsible"
                    transition={{ duration: '300ms', timingFunction: 'ease-in-out' }}
                    expandOnPrint
                >
                    <sc.Line />
                    <sc.StepStartContent>
                        <Box>
                            {dataStep.map((item) => (
                                <StepStartContentItem
                                    {...item}
                                    key={item.id}
                                    handleCheck={handleCheck}
                                    onOpenItem={onOpenItem}
                                />
                            ))}
                        </Box>
                    </sc.StepStartContent>
                </Collapsible>
            </Card>
        </sc.StepStartContainer>
    );
};
