import { FC } from 'react';
import clsx from 'clsx';
import { BlockStack, TextProps } from '@shopify/polaris';
import { BaseItemTitle, BaseItemTitleProps } from '@/components/builder/base/BaseItemTitle';
import './style.scss';

interface BaseItemLayoutProps extends Pick<BaseItemTitleProps, 'tooltipContent' | 'hasTooltip' | 'tooltipChildren'> {
    children: React.ReactNode;
    containerClassName?: string;
    direction?: 'row' | 'column';
    textProps?: TextProps;
    keyProps?: string;
    hideTitle?: boolean;
}

export const BaseItemLayout: FC<BaseItemLayoutProps> = ({
    children,
    containerClassName,
    direction = 'row',
    textProps,
    keyProps,
    hideTitle = false,
    tooltipContent,
    hasTooltip,
    tooltipChildren,
}) => {
    return (
        <div
            className={clsx('base-item-layout', containerClassName, {
                'base-item-layout--column': direction === 'column',
            })}
            key={keyProps}
        >
            {!hideTitle && (
                <BaseItemTitle
                    {...(textProps as BaseItemTitleProps)}
                    tooltipContent={tooltipContent}
                    hasTooltip={hasTooltip}
                    tooltipChildren={tooltipChildren}
                />
            )}
            <BlockStack gap="100">{children}</BlockStack>
        </div>
    );
};
