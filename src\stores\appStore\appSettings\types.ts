import { ActivityLogParams } from '@/pages/SettingsPage/types';

export interface AppSettingsProps {
    accountEmail: {
        emailSubscribed: string;
        isSubscribed: boolean;
    };
    accountStatus: {
        shopifyDomain: string;
        plan: {
            current: string;
            list?: Array<{ key: string; value: string }>;
        };
    };
    language: string;
    storefrontPassword: string;
    shopName: string;
}

export interface AppSettingsState {
    appSettings: AppSettingsProps | null;
    activityLogs: ActivityLogs[];
    apiStatus: 'loading' | 'success' | 'error';
    totalLogs: number;
}

export interface AppSettingsActions {
    getAppSettings: () => Promise<void>;
    updateAppSettings: (appSettings: AppSettingsRequest) => Promise<void>;
    resetAppSettings: () => Promise<void>;
    setActivityLogs: (params: ActivityLogParams) => void;
    setTotalLogs: (total: number) => void;
}

export interface AppSettingsRequest {
    shopifyDomain: string;
    emailSubscribed: string;
    isSubscribe: number;
    storefrontPassword: string;
    language: string;
    plan: string;
}

export interface ActivityLogs {
    id: number;
    shopId: number;
    action: {
        key: string;
        value: string | string[];
    };
    ip: {
        key: string;
        value: string;
    };
    description: {
        key: string;
        value: string | string[];
    };
    created_at: string;
}
