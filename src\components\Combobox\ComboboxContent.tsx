import { ComboboxContentProps } from './types';
import { useComboboxContext } from './context';

export const ComboboxContent = ({
    loading = false,
    maxHeight = '300px',
    children,
    hideEmptyState,
}: ComboboxContentProps) => {
    const { isOpen, optionsRef } = useComboboxContext();

    if (!isOpen) return null;

    return (
        <div
            ref={optionsRef}
            data-scope="combobox"
            data-part="content"
            data-state={isOpen ? 'open' : 'closed'}
            data-hide-empty-state={hideEmptyState || undefined}
            style={{
                maxHeight: typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight,
            }}
        >
            <div data-scope="combobox" data-part="content-inner">
                {loading ? (
                    <div data-scope="combobox" data-part="loading">
                        <div data-scope="combobox" data-part="spinner"></div>
                        <span>Loading...</span>
                    </div>
                ) : (
                    children
                )}
            </div>
        </div>
    );
};
