import { FC } from 'react';
import { ActionListItemDescriptor, TextProps } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BaseSelect } from '../../base';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseSelectProps = Parameters<typeof BaseSelect>[0];

interface SettingsSelectProps
    extends Pick<
        BaseItemLayoutProps,
        'direction' | 'containerClassName' | 'tooltipContent' | 'hasTooltip' | 'tooltipChildren'
    > {
    path: string;
    blockId: string;
    options: ActionListItemDescriptor[];
    isUpdateConfigs?: boolean;
    label?: string;
    textProps?: Omit<Partial<TextProps>, 'children'>;
    selectProps?: Omit<BaseSelectProps, 'options' | 'value' | 'onChange'>;
}

export const SettingsSelect: FC<SettingsSelectProps> = ({
    path,
    blockId,
    options,
    isUpdateConfigs,
    textProps,
    label,
    selectProps,
    ...otherProps
}) => {
    const value = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const handleChange = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, path, value);
        } else {
            updateBlockProperty(blockId, currentDevice, path, value);
        }
    };

    const defaultTextProps = { as: 'p', variant: 'bodyMd', children: label };

    return (
        <BaseItemLayout textProps={{ ...defaultTextProps, ...textProps } as TextProps} {...otherProps}>
            <BaseSelect options={options} value={value} onChange={handleChange} {...selectProps} />
        </BaseItemLayout>
    );
};
