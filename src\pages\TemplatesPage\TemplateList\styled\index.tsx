import { keyframes, css } from '@emotion/react';
import styled from '@emotion/styled';

const skeleton = keyframes`
  0% {
    transform: translate3d(-30%, 0, 0);
  }
  100% {
    transform: translate3d(100%, 0, 0);
  }
`;

export const CardContent = styled.div`
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 334px;
`;

export const CardImage = styled.div<{ transitionTime: string }>`
    position: relative;
    height: calc(100% - 64px);
    overflow: hidden;
    background: var(--p-color-bg-surface-hover);
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.25), 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    img {
        width: 100%;
        max-width: 100%;
        height: auto;
        object-fit: cover;
        object-position: center center;
        :hover {
            transform: translateY(calc(-100% + 269px));
            transition: ${(props) => `transform ${props.transitionTime} ease-in-out;`};
        }
    }
`;

export const CardImageSkeleton = styled.div`
    position: relative;
    height: calc(100% - 64px);
    overflow: hidden;
    background: var(--p-color-bg-surface-hover);
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.25), 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
    ::before {
        content: '';
        position: absolute;
        inset: 0;
        width: 100vw;
        max-width: 1000px;
        background: linear-gradient(to right, transparent 0%, #efefef 15%, transparent 30%);
        animation-duration: 1.5s;
        animation-fill-mode: forwards;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        ${css`
            animation-name: ${skeleton};
        `}
    }
`;
