import { FC, useCallback, useState, useRef, useEffect } from 'react';
import { SearchIcon, UploadIcon, XIcon } from '@shopify/polaris-icons';
import { But<PERSON>, Divider, Icon, Text, TextField, Popover, DropZone, Spinner, InlineStack } from '@shopify/polaris';
import { useBlockStore, useBuilderStore, Auto_BlockDeviceType } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { useDebounce, useQuery } from '@/hooks';
import { extractFontName } from '@/utils';
import { httpRequest } from '@/configs/api';
import { apiAddress } from '@/configs/apiAddress';
import { ListItemFont } from './ListItemFont';
import { Font, FontResponse } from './types';
import { BaseSwitchTab } from '../../base/BaseSwitchTab';
import clsx from 'clsx';
import './styles.scss';
interface SettingsFontFamilyProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}
const LIMIT = 10;

export const SettingsFontFamily: FC<SettingsFontFamilyProps> = ({ blockId = '', path, isUpdateConfigs }) => {
    const [search, setSearch] = useState('');
    const [active, setActive] = useState(false);
    const [page, setPage] = useState(1);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [isLoadingImport, setIsLoadingImport] = useState(false);
    const [fonts, setFonts] = useState<Font[]>([]);
    const [tabSelected, setTabSelected] = useState('autoketing-fonts');

    const modelValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}`, blockId)
        : getBlockBPProperty(path as string, blockId);

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice as Auto_BlockDeviceType);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const togglePopover = () =>
        setActive((active) => {
            if (!active) {
                setFonts([]);
                setPage(1);
                setTabSelected('autoketing-fonts');
            }
            return !active;
        });
    const handleSearchChange = (value: string) => {
        setSearch(value);
    };
    const searchValue = useDebounce(search, 500, () => {
        setPage(1);
        setFonts([]);
    });
    const listRef = useRef<HTMLDivElement>(null);

    const { data, isLoading } = useQuery<FontResponse>({
        url: clsx(
            apiAddress.fonts.index,
            `?currentPage=${page}`,
            `&perPage=${LIMIT}`,
            tabSelected === 'google-fonts' && active && `&type=google`,
            tabSelected === 'uploaded' && active && `&type=customer`,
            tabSelected === 'autoketing-fonts' && active && `&type=autoketing`,
            searchValue && `&keyword=${searchValue}`,
        ).replace(/\s+/g, ''),
        method: 'GET',
        sleepTime: 300,
        onSuccess(data) {
            const fontsData = data?.result?.data?.data;
            if (!fontsData) return;
            const fontUrls = fontsData.map((font) => font.url);

            let styleElement = document.querySelector('style#so-load-fonts');

            if (!styleElement) {
                styleElement = document.createElement('style');
                styleElement.id = 'so-load-fonts';
                document.head.appendChild(styleElement);
            }

            const sheet = (styleElement as HTMLStyleElement).sheet;
            fontUrls.forEach((url) => {
                const ruleExists = Array.from(sheet!.cssRules).some((rule) =>
                    rule.cssText.includes(`@import url('${url}?v=1');`),
                );
                if (!ruleExists) {
                    sheet!.insertRule(`@import url('${url}?v=1');`, sheet!.cssRules.length);
                }
            });
            setFonts((prevFonts) => {
                const newFonts = page === 1 ? fontsData : [...prevFonts, ...fontsData];
                return newFonts;
            });
            setIsLoadingMore(false);
        },
    });

    const handleScroll = useCallback(() => {
        if (!listRef.current || !data?.result?.data?.total || isLoadingMore) return;

        const { scrollTop, scrollHeight, clientHeight } = listRef.current;
        const total = data.result.data.total;

        if (scrollTop + clientHeight >= scrollHeight - 50 && fonts.length < total) {
            setIsLoadingMore(true);
            setPage((prev) => prev + 1);
        }
    }, [data?.result?.data?.total, fonts.length, isLoadingMore]);

    const handleChange = (value: string) => {
        if (blockId) {
            if (isUpdateConfigs) {
                updateBlockConfigsProperty(blockId, path, value);
            } else {
                updateBlockProperty(blockId, currentDevice, path, value);
            }
        }
    };
    const handleChangeTab = (value: string) => {
        setTabSelected(value);
    };

    const handleImport = useCallback(async (fontFiles: File[]) => {
        const formData = new FormData();
        setIsLoadingImport(true);
        fontFiles.forEach((file) => {
            formData.append('fontFiles[]', file);
        });
        try {
            const response = await httpRequest({
                url: apiAddress.fonts.upload,
                method: 'POST',
                data: formData,
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            setIsLoadingImport(false);
            setFonts((prev) => [...prev, ...response.data.result.data]);
            shopify.toast.show('Font uploaded successfully');
        } catch (error) {
            console.error('Import failed:', error);
            shopify.toast.show('Failed to upload font', { isError: true });
            setIsLoadingImport(false);
        }
    }, []);

    const handleDeleteFont = useCallback(async (id: string) => {
        try {
            await httpRequest({
                url: `${apiAddress.fonts.index}/${id}`,
                method: 'DELETE',
            });
            setFonts((prev) => prev.filter((font) => font.id !== id));
            shopify.toast.show('Font deleted successfully');
        } catch (error) {
            console.error('Delete font failed:', error);
            shopify.toast.show('Failed to delete font', { isError: true });
        }
    }, []);

    useEffect(() => {
        setPage(1);
        setFonts([]);
    }, [tabSelected, active, searchValue]);

    return (
        <div className="settings-font-family">
            <InlineStack align="space-between" wrap={false} blockAlign="center">
                <Text as="span" variant="bodyMd">
                    Font Family
                </Text>

                <Popover
                    active={active}
                    preferredAlignment="left"
                    activator={
                        <Button
                            onClick={togglePopover}
                            accessibilityLabel="Other save actions"
                            size="large"
                            variant="tertiary"
                            disclosure="select"
                            textAlign="left"
                            fullWidth
                            id="base-select-activator"
                        >
                            {modelValue ? extractFontName(modelValue) : 'Please select font'}
                        </Button>
                    }
                    autofocusTarget="first-node"
                    onClose={togglePopover}
                >
                    <div className="settings-font-family__popover">
                        <div className="settings-font-family__popover__title">
                            <Text as="span" variant="bodyMd" fontWeight="medium">
                                Select font
                            </Text>
                            <Button
                                size="slim"
                                variant="tertiary"
                                icon={XIcon}
                                onClick={togglePopover}
                                accessibilityLabel="Close"
                            />
                        </div>

                        <div className="settings-font-family__popover__content">
                            <BaseSwitchTab
                                options={[
                                    {
                                        contentTooltip: 'Font library',
                                        id: 'autoketing-fonts',
                                    },
                                    {
                                        contentTooltip: 'Google fonts',
                                        id: 'google-fonts',
                                    },
                                    {
                                        contentTooltip: 'Uploaded',
                                        id: 'uploaded',
                                    },
                                ]}
                                noTooltip
                                valueData={tabSelected}
                                onChange={handleChangeTab}
                            />
                            <div className="settings-font-family__popover__content__search">
                                <TextField
                                    label=""
                                    value={search}
                                    onChange={handleSearchChange}
                                    prefix={<Icon source={SearchIcon} />}
                                    placeholder="Search font..."
                                    autoComplete="off"
                                    clearButton
                                    onClearButtonClick={() => setSearch('')}
                                    labelHidden
                                    inputMode="search"
                                />
                                <Divider />
                            </div>
                            <div
                                className="settings-font-family__popover__content__list"
                                ref={listRef}
                                onScroll={handleScroll}
                            >
                                {isLoading && page === 1 ? (
                                    <div className="settings-font-family__popover__content__list__loading">
                                        <Spinner size="small" />
                                    </div>
                                ) : fonts.length === 0 ? (
                                    <div className="settings-font-family__popover__content__list__empty">
                                        <Text as="span" variant="bodyMd" tone="subdued">
                                            No fonts found
                                        </Text>
                                    </div>
                                ) : (
                                    <>
                                        {fonts.map((font, index) => (
                                            <ListItemFont
                                                key={`${font.id}-${index}`}
                                                fonts={font}
                                                selectedFont={modelValue}
                                                handleFontClick={handleChange}
                                                tabSelected={tabSelected}
                                                handleDeleteFont={handleDeleteFont}
                                            />
                                        ))}
                                        {isLoadingMore && (
                                            <div className="settings-font-family__popover__content__list__loading">
                                                <Spinner size="small" />
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                        <div className="settings-font-family__popover__footer">
                            {tabSelected === 'uploaded' && (
                                <DropZone disabled={isLoadingImport} onDrop={handleImport}>
                                    <Button disabled={isLoadingImport} size="large" icon={UploadIcon}>
                                        {isLoadingImport ? 'Uploading...' : 'Upload font'}
                                    </Button>
                                </DropZone>
                            )}
                        </div>
                    </div>
                </Popover>
            </InlineStack>
        </div>
    );
};
