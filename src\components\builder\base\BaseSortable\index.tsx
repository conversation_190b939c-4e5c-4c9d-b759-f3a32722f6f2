import { Sortable, SortableProps, SortableItemRequireProps } from '@/components';

type BaseSortableProps<T extends SortableItemRequireProps> = SortableProps<T>;

export function BaseSortable<T extends SortableItemRequireProps>({
    extendNode,
    itemRenderer = ({ data }) => <div>{data.id}</div>,
    items,
    onChange,
    ...otherProps
}: BaseSortableProps<T>) {
    return (
        <Sortable<T>
            items={items}
            onChange={onChange}
            extendNode={extendNode}
            itemRenderer={itemRenderer}
            {...otherProps}
        />
    );
}
