import { FC } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { BlockStack, Box } from '@shopify/polaris';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import { buttonPositionOptions } from '@/components/builder/data/options';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { getBlockBPProperty } from '@/utils/shared';

interface PositionProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
}

export const Position: FC<PositionProps> = ({ path, blockId, isUpdateConfigs, label }) => {
    const positionType = getBlockBPProperty('position.positionType', blockId);
    const stickToType = getBlockBPProperty('position.stickTo.stickToType', blockId);
    return (
        <BaseCollapse label={label}>
            <Box paddingBlockStart="300">
                <BlockStack gap="300">
                    <SettingsSelect
                        blockId={blockId}
                        path={`${path}.positionType`}
                        isUpdateConfigs={isUpdateConfigs}
                        options={buttonPositionOptions}
                        label="Position"
                    />
                    {positionType === 'sticky' && (
                        <>
                            <SettingsSelect
                                blockId={blockId}
                                path={`${path}.stickTo.stickToType`}
                                isUpdateConfigs={isUpdateConfigs}
                                options={[
                                    {
                                        id: 'top',
                                        content: 'Top',
                                    },
                                    {
                                        id: 'bottom',
                                        content: 'Bottom',
                                    },
                                    {
                                        id: 'default',
                                        content: 'Default',
                                    },
                                ]}
                                label="Fixed position"
                            />
                            {stickToType === 'top' && (
                                <>
                                    <SettingsInput
                                        blockId={blockId}
                                        path={`${path}.stickTo.top`}
                                        label="Top"
                                        isUpdateConfigs={isUpdateConfigs}
                                        inputProps={{
                                            type: 'number',
                                            suffix: '%',
                                        }}
                                    />
                                </>
                            )}
                            {stickToType === 'bottom' && (
                                <SettingsInput
                                    blockId={blockId}
                                    path={`${path}.stickTo.bottom`}
                                    label="Bottom"
                                    isUpdateConfigs={isUpdateConfigs}
                                    inputProps={{
                                        type: 'number',
                                        suffix: '%',
                                    }}
                                />
                            )}
                        </>
                    )}
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
