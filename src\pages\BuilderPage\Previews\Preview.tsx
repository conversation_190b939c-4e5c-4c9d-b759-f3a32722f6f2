import { FC } from 'react';
import { Text } from '@shopify/polaris';
import { TabItemProps } from '@/pages/BuilderPage/types/tabs';
import { previewMapping } from '@/pages/BuilderPage/Previews/previewMapping';
interface PreviewProps {
    selectedElementItem: TabItemProps | null;
}

export const Preview: FC<PreviewProps> = ({ selectedElementItem }) => {
    if (!selectedElementItem || !selectedElementItem.type) {
        return <Text as="p">Select an element to preview</Text>;
    }

    const PreviewComponent = previewMapping[selectedElementItem.type];

    if (!PreviewComponent) {
        return <Text as="p">No preview available</Text>;
    }

    return <PreviewComponent />;
};
