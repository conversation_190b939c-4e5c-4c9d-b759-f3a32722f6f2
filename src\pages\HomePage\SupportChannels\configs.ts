import { IconSource } from '@shopify/polaris';
import { ChatIcon, GlobeAsiaIcon, PlayIcon, TextBlockIcon } from '@shopify/polaris-icons';

export interface SupportChannelsProps {
    id: string | number;
    name: string;
    icon: IconSource;
    description: string;
    link: string;
    tooltip?: string;
}

export const supportChannels: SupportChannelsProps[] = [
    {
        id: 1,
        name: 'Live Chat',
        icon: ChatIcon,
        description: '24/7 live chat support, instant replies.',
        link: '#',
        tooltip: 'Open Live Chat',
    },
    {
        id: 2,
        name: 'Help Center',
        icon: TextBlockIcon,
        description: 'Find answers in our detailed manuals.',
        link: '#',
        tooltip: 'Visit Help Center',
    },
    {
        id: 3,
        name: 'Video Tutorial',
        icon: PlayIcon,
        description: 'Have fun learning with concise video tutorials. ',
        link: '#',
        tooltip: 'Watch Video Tutorial',
    },
    {
        id: 4,
        name: 'FanPage',
        icon: GlobeAsiaIcon,
        description: 'Get the latest updates, deals, tips, and more.',
        link: '#',
        tooltip: 'Visit Our FanPage',
    },
];
