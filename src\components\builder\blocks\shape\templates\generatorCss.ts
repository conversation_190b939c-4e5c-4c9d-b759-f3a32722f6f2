import {
    generateBackground,
    generateBoxShadowV2,
    getResponsiveValue,
    generateBaseCss,
} from '@/stores/appStore/cssSystem';
import type { Generator, Background, SettingsShadow } from '@/stores/appStore/cssSystem';

export const shapeCssGenerators: Generator[] = [
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const background = getResponsiveValue<Background>(blockData, 'background', breakpoint);
            return {
                ...generateBackground(background),
            };
        },
    },
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const boxShadow = getResponsiveValue<SettingsShadow>(blockData, 'boxShadow', breakpoint);
            return {
                ...generateBaseCss(blockData, breakpoint),
                ...generateBoxShadowV2(boxShadow),
            };
        },
    },

    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const { displayOnDesktop, displayOnMobile } = blockData.configs;
            const display = breakpoint === 'desktop' ? displayOnDesktop : displayOnMobile;
            return {
                display: display ? 'block' : 'none',
            };
        },
        applyTo: 'publish',
    },
];
