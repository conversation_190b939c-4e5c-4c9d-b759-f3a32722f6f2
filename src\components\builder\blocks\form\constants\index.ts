export const DATA_SET_FORM_CSS_VARIABLE = 'data-atk-form-css-variable';

// TITLE
export const DATA_SET_FORM_TITLE = 'data-atk-form-title';
// FIELD
export const DATA_SET_FORM_FIELD_WRAP = 'data-atk-form-field-wrap';
export const DATA_SET_FORM_FIELD_LABEL = 'data-atk-form-field-label';
export const DATA_SET_FORM_FIELD = 'data-atk-form-field';
export const DATA_SET_FORM_ELEMENT = 'data-atk-form-element';
export const DATA_SET_FORM_INPUT = 'data-atk-form-input';
export const DATA_SET_FORM_CHECKBOX_MULTI_ELEMENT = 'data-atk-form-checkbox-multi-element';
export const DATA_SET_FORM_CHECKBOX_ELEMENT = 'data-atk-form-checkbox-element';
export const DATA_SET_FORM_CHECKBOX_LABEL = 'data-atk-form-checkbox-label';
export const DATA_SET_FORM_CHECKBOX_ELEMENT_WRAP = 'data-atk-form-checkbox-element-wrap';
export const DATA_SET_FORM_CHECKBOX_ELEMENT_INNER = 'data-atk-form-checkbox-element-inner';
export const DATA_SET_FORM_RADIO_MULTI_ELEMENT = 'data-atk-form-radio-multi-element';
export const DATA_SET_FORM_RADIO_ELEMENT = 'data-atk-form-radio-element';
export const DATA_SET_FORM_RADIO_LABEL = 'data-atk-form-radio-label';
export const DATA_SET_FORM_RADIO_ELEMENT_WRAP = 'data-atk-form-radio-element-wrap';
export const DATA_SET_FORM_RADIO_ELEMENT_INNER = 'data-atk-form-radio-element-inner';
export const DATA_SET_FORM_DROPDOWN_ELEMENT = 'data-atk-form-dropdown-element';
export const DATA_SET_FORM_TEXTAREA_ELEMENT = 'data-atk-form-textarea-element';
export const DATA_SET_FORM_SEPARATOR_LINE = 'data-atk-form-separator-line';
export const DATA_SET_FORM_FIELD_ID = 'data-atk-form-field-id';
// BUTTON
export const DATA_SET_FORM_SUBMIT_WRAP = 'data-atk-form-submit-wrap';
export const DATA_SET_FORM_SUBMIT = 'data-atk-form-submit';

// CUSTOM
export const DATA_SET_FORM_HAS_CUSTOM = 'data-atk-form-has-custom';
export const DATA_SET_FORM_CUSTOM_CLASS = 'data-atk-form-custom-class';
export const DATA_SET_FORM_CUSTOM_CSS = 'data-atk-form-custom-css';

// INPUT ATTRIBUTE
export const DATA_SET_FORM_INPUT_PLACEHOLDER = 'data-atk-form-input-placeholder';
export const DATA_SET_FORM_INPUT_MAX_LENGTH = 'data-atk-form-input-max-length';
export const DATA_SET_FORM_INPUT_MIN_LENGTH = 'data-atk-form-input-min-length';
export const DATA_SET_FORM_INPUT_PATTERN = 'data-atk-form-input-pattern';
export const DATA_SET_FORM_INPUT_REQUIRED = 'data-atk-form-input-required';
export const DATA_SET_FORM_INPUT_READ_ONLY = 'data-atk-form-input-read-only';

// ANIMATION

export const DATA_SET_FORM_POSITION = 'data-atk-form-position';

// CONFIGS
export const DATA_SET_FORM_CONFIGS = 'data-atk-form-after-submit-configs';
