import { TextItalicIcon } from '@shopify/polaris-icons';
import { useRTE } from '../context';
import { BaseTool } from './BaseTool';

export const Italic = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('italic');
    const toggleItalic = () => {
        editor?.chain().focus().toggleItalic().run();
    };
    return <BaseTool onClick={toggleItalic} icon={TextItalicIcon} tooltip="Italic" isActive={isActive} />;
};
