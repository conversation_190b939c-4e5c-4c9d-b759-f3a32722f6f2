import { FC } from 'react';
import './styles.scss';

interface SuggestionProps {
    selectedValue: string;
    value: string;
    onClick?(): void;
}

export const Suggestion: FC<SuggestionProps> = ({ selectedValue, value, onClick }) => {
    return (
        <div className={`suggestion ${selectedValue === value ? 'suggestion--selected' : ''}`} onClick={onClick}>
            <div className="suggestion__value">{value}</div>
        </div>
    );
};
