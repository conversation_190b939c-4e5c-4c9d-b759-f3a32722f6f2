import { useCallback, useState } from 'react';
import AceEditor from 'react-ace';
import { Button, InlineStack, Text } from '@shopify/polaris';
import { useBlockStore, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import 'ace-builds/src-noconflict/mode-html';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/ext-language_tools';
import './styles.scss';

export const Contents = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;

    const htmlCode = getBlockProperty(`configs.content`, selectedBlockId);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const [value, setValue] = useState(htmlCode);

    const onChange = useCallback((newValue: string) => setValue(newValue), []);

    const onClickAdd = useCallback(() => {
        updateBlockConfigsProperty(selectedBlockId, `content`, value);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedBlockId, value]);

    const onCancelClick = useCallback(() => {
        setValue(htmlCode);
    }, [htmlCode]);

    return (
        <div className="html-code-editor">
            <div className="html-code-editor__header">
                <Text variant="bodySm" as="p" tone="subdued">
                    Embedded code only works on the live page.
                </Text>
            </div>
            <AceEditor
                value={value}
                fontSize={12}
                placeholder="Html code"
                mode="html"
                theme="chrome"
                onChange={onChange}
                name="Id"
                width="100%"
                height="calc(100% - 115px)"
                className="html-code-editor__editor"
                setOptions={{
                    enableBasicAutocompletion: true,
                    enableLiveAutocompletion: true,
                    enableSnippets: true,
                    showLineNumbers: true,
                    tabSize: 2,
                    wrap: true,
                    useWorker: false,
                    showPrintMargin: false,
                    highlightActiveLine: true,
                    enableEmmet: true,
                }}
                editorProps={{ $blockScrolling: true }}
            />
            <div className="html-code-editor__footer">
                <InlineStack align="end" gap="200">
                    <Button onClick={onCancelClick}>Cancel</Button>
                    <Button variant="primary" disabled={htmlCode === value} onClick={onClickAdd}>
                        Done
                    </Button>
                </InlineStack>
            </div>
        </div>
    );
};
