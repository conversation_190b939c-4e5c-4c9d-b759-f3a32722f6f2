/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import {
    generateBackground,
    generateBorder,
    generateContentStyles,
    generateTypography,
} from '@/components/builder/data/utils/CssGenerator/utils';
import { generateUnitValue } from '@/components/builder/data/utils/CssGenerator/helper';
import { getBlockClassName } from '@/utils/cssBlockGenerator';
import {
    isValidCssValue,
    isValidUnitValue,
} from '@/utils/cssValidator';

export const generateCss = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile'): string => {
    if (!block.bpConfigs?.[device]) return '';
    const bpConfig = block.bpConfigs[device];
    const config = block.configs;
    
    const className = getBlockClassName('button');

    const outer = `.${className}-wrapper[data-auto-id="${blockId}"]`;
    const inner = `${outer} .atk-button[data-auto-id-inner="${blockId}"]`;

    let outerCss = '';

    if (isValidCssValue(bpConfig.typography?.textAlign)) {
        outerCss += `--button-content-align: ${bpConfig.typography.textAlign};\n    `;
    }
    
    outerCss += 'display: block;\n    ';
    
    if (isValidCssValue(bpConfig.gridColStart)) {
        outerCss += `grid-column-start: ${bpConfig.gridColStart};\n    `;
    }

    
    
    if (isValidCssValue(bpConfig.gridColEnd)) {
        outerCss += `grid-column-end: ${bpConfig.gridColEnd};\n    `;
    }
    
    if (isValidCssValue(bpConfig.gridRowStart)) {
        outerCss += `grid-row-start: ${bpConfig.gridRowStart};\n    `;
    }
    
    if (isValidCssValue(bpConfig.gridRowEnd)) {
        outerCss += `grid-row-end: ${bpConfig.gridRowEnd};\n    `;
    }
    
    if (isValidUnitValue(bpConfig.mt)) {
        outerCss += `margin-top: ${bpConfig.mt.val}${bpConfig.mt.unit || 'px'};\n    `;
    }
    
    if (isValidUnitValue(bpConfig.ml)) {
        outerCss += `margin-left: ${bpConfig.ml.val}${bpConfig.ml.unit || 'px'};\n    `;
    }
    
    if (isValidCssValue(bpConfig.justifySelf)) {
        outerCss += `justify-self: ${bpConfig.justifySelf};\n    `;
    }
    
    if (bpConfig?.alignSelf && bpConfig.alignSelf !== 'none') {
        outerCss += `align-self: ${bpConfig.alignSelf};\n    `;
    }

    let css = `${outer} {\n    ${outerCss}}`;

    // Media queries
    if (device === 'desktop' && config.displayOnDesktop === false) {
        css += `\n@media (min-width: 769px) {\n    ${outer} {\n        display: none;\n    }\n}`;
    }

    if (device === 'mobile' && config.displayOnMobile === false) {
        css += `\n@media (max-width: 768px) {\n    ${outer} {\n        display: none;\n    }\n}`;
    }

    let innerCss = '';
    
    if (bpConfig.backgroundButton) {
        innerCss += `${generateBackground(bpConfig.backgroundButton)}\n    `;
    }
    
    if (bpConfig.borderButton) {
        innerCss += `${generateBorder(bpConfig.borderButton)}\n    `;
    }
    
    if (isValidUnitValue(bpConfig.width)) {
        innerCss += `width: ${bpConfig.width.val}${bpConfig.width.unit || 'px'};\n    `;
    }
    
    if (isValidUnitValue(bpConfig.height)) {
        innerCss += `height: ${bpConfig.height.val}${bpConfig.height.unit || 'px'};\n    `;
    }
    
    if (bpConfig.buttonsSpace?.padding) {
        const paddingDirs = ['top', 'right', 'bottom', 'left'];
        const validPaddings = paddingDirs.every(dir => 
            isValidUnitValue(bpConfig.buttonsSpace.padding[dir])
        );
        
        if (validPaddings) {
            const paddingValues = paddingDirs.map(dir => 
                generateUnitValue(bpConfig.buttonsSpace.padding[dir])
            ).join(' ');
            
            innerCss += `padding: ${paddingValues};\n    `;
        }
    }
    
    if (config.content) {
        innerCss += `${generateContentStyles(config.content as any)}\n    `;
    }
    
    css += `\n\n${inner} {\n    ${innerCss}}`;

    if ((config.content as any)?.icon && 
        isValidUnitValue((config.content as any)?.iconSize) && 
        isValidCssValue((config.content as any)?.iconColor)) {
        
        css += `\n\n${inner} .atk-button-icon {
          display: inline-flex;
          align-items: center;
          flex-shrink: 0;
          justify-content: center;
          width: ${(config.content as any).iconSize.val}${(config.content as any).iconSize.unit || 'px'};
          height: ${(config.content as any).iconSize.val}${(config.content as any).iconSize.unit || 'px'};  
        }`;

        css += `\n\n${inner} .atk-button-icon svg,
        ${inner} .atk-button-icon path {
          fill: ${(config.content as any).iconColor};
        }`;
    }

    if (bpConfig.typography) {
        css += `\n\n${inner} .atk-button-text {
        ${generateTypography(bpConfig.typography, '--atk-button-letter-spacing', '--atk-button-line-height')}
    }`;
    }

    css += `
      .atk-button-wrapper .animation {
        animation-name: ${(config as any).animation.type};
        animation-duration: ${(config as any).animation.duration.val}${(config as any).animation.duration.unit};
        animation-iteration-count: ${(config as any).animation.loop};
        animation-delay: ${(config as any).animation.delay.val}${(config as any).animation.delay.unit};
      }
    `;
    return css;
};