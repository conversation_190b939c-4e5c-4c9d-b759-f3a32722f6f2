import { FC } from 'react';
import { InputSettings } from './settings/InputSettings';
import { NumberSettings } from './settings/NumberSettings';
import { FreeTextSettings } from './settings/FreeTextSettings';
import { RadioSettings } from './settings/RadioSettings';
import { CheckboxSettings } from './settings/CheckboxSettings';
import { DropdownSettings } from './settings/DropdownSettings';
import { type FormField } from '@/components/builder/blocks/form/types';

// Group related field types
type InputBasedFieldType = 'text' | 'long-text' | 'email' | 'password' | 'tel';
type SelectionFieldType = 'radio' | 'checkbox' | 'dropdown';
type SpecialFieldType = 'number' | 'free-text';
export type FormFieldType = InputBasedFieldType | SelectionFieldType | SpecialFieldType;
interface SettingsComponentProps {
    formField: FormField;
    selectedBlockTarget: HTMLElement;
}

type SettingsComponent = FC<SettingsComponentProps>;

// Group related settings components
const inputBasedSettings = {
    text: InputSettings,
    'long-text': InputSettings,
    email: InputSettings,
    password: InputSettings,
    tel: InputSettings,
} as const;

const selectionSettings = {
    radio: RadioSettings,
    checkbox: CheckboxSettings,
    dropdown: DropdownSettings,
} as const;

const specialSettings = {
    number: NumberSettings,
    'free-text': FreeTextSettings,
} as const;

export const formFieldSettingsMap: Record<FormFieldType, SettingsComponent> = {
    ...inputBasedSettings,
    ...selectionSettings,
    ...specialSettings,
} as const;

export * from './FieldSettingsRenderer';
