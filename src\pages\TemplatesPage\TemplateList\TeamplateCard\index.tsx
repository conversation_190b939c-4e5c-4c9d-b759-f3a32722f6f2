import { ReactNode } from 'react';
import { ViewIcon } from '@shopify/polaris-icons';
import { Box, Button, ButtonGroup, Card, InlineStack, Text, Tooltip } from '@shopify/polaris';
import { useImageScrollDuration } from '@/hooks';
import * as sc from '../styled';

export type CardBaseProps<T = object> = T & {
    title: string;
    thumbnail: string;
};

export interface CardProps<T = object> {
    data: CardBaseProps<T>;
    onPreview?: () => void;
    onSelect?: () => void;
    previewTooltip?: string;
    selectButtonText?: string;
    renderActions?: (data: CardBaseProps<T>) => ReactNode;
    btnSelectLoading?: boolean;
}

export const TemplateCard = <T extends object>({
    data,
    onPreview,
    onSelect,
    previewTooltip = 'Preview',
    selectButtonText = 'Select',
    renderActions,
    btnSelectLoading,
}: CardProps<T>) => {
    const { title = '', thumbnail = '' } = data;
    const { imgRef, duration } = useImageScrollDuration();

    return (
        <Card padding="0" roundedAbove="xs">
            <sc.CardContent>
                <sc.CardImage transitionTime={duration}>
                    <img ref={imgRef} src={`${import.meta.env.VITE_BASE_URL}${thumbnail}`} alt={title} />
                </sc.CardImage>
                <Box paddingInline="400" paddingBlockStart="400" paddingBlockEnd="500">
                    <InlineStack align="space-between" blockAlign="center" wrap>
                        <Box width="50%">
                            <Tooltip content={title}>
                                <Text as="p" variant="headingSm" truncate>
                                    {title}
                                </Text>
                            </Tooltip>
                        </Box>
                        {renderActions ? (
                            renderActions(data)
                        ) : (
                            <ButtonGroup>
                                <Tooltip content={previewTooltip}>
                                    <Button icon={ViewIcon} onClick={onPreview} />
                                </Tooltip>
                                <Button loading={btnSelectLoading} onClick={onSelect}>
                                    {selectButtonText}
                                </Button>
                            </ButtonGroup>
                        )}
                    </InlineStack>
                </Box>
            </sc.CardContent>
        </Card>
    );
};
