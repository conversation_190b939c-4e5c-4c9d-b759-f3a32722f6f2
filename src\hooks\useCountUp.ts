import { useEffect, useState } from 'react';

export const useCountUp = (number: number) => {
    const [count, setCount] = useState(0);
    useEffect(() => {
        const startTime = Date.now();
        const duration = 1500;

        const interval = setInterval(() => {
            const currentTime = Date.now();
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            setCount(Math.floor(progress * number));

            if (progress === 1) {
                clearInterval(interval);
            }
        }, 16);

        return () => clearInterval(interval);
    }, [number]);

    return count;
};
