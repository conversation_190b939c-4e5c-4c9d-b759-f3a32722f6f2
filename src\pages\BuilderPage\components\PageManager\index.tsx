/* eslint-disable @typescript-eslint/no-explicit-any */

import {
    ActionList,
    Button,
    Icon,
    Scrollable,
    Tooltip,
    Popover,
    TextField,
    Divider,
    Modal,
    BlockStack,
} from '@shopify/polaris';
import { type FC, useState, useCallback, useEffect } from 'react';
import {
    SearchIcon,
    ChevronRightIcon,
    ChevronLeftIcon,
    EditIcon,
    DuplicateIcon,
    DeleteIcon,
    PlusCircleIcon,
    ThemeTemplateIcon,
} from '@shopify/polaris-icons';
import './style.scss';
import { useAppStore } from '@/stores/appStore';
import { PageItem } from '@/stores/appStore/pages/types';
import { TemplateType } from '@/stores/appStore/pages/types';

export const PageManager: FC = () => {
    const templateType = useAppStore((state) => state.templateType);
    const page = useAppStore((state) => state.page);
    const pageList = useAppStore((state) => state.pageList);
    const getDetailPage = useAppStore((state) => state.getDetailPage);
    const updatePage = useAppStore((state) => state.updatePage);
    const duplicatePage = useAppStore((state) => state.duplicatePage);
    const deletePage = useAppStore((state) => state.deletePage);
    const [active, setActive] = useState(false);
    const [search, setSearch] = useState('');
    const [selectedParent, setSelectedParent] = useState<PageItem[] | null>(null);
    const [currentView, setCurrentView] = useState<'main' | 'submenu'>('main');
    const [isRenaming, setIsRenaming] = useState(false);
    const [renamingItemId, setRenamingItemId] = useState<string>('');
    const [newPageName, setNewPageName] = useState('');
    const [pageName, setPageName] = useState('');
    const [openModal, setOpenModal] = useState(false);
    const [itemToDelete, setItemToDelete] = useState<PageItem | null>(null);
    const [loading, setLoading] = useState(false);
    const [modalState, setModalState] = useState({
        kind: '',
        inputText: '',
    });
    const [pageSelected, setPageSelected] = useState<TemplateType | null>(null);
    const [templateId, setTemplateId] = useState<number | null>(null);

    const handleSearchChange = useCallback((value: string) => setSearch(value), []);
    const togglePopover = useCallback(() => setActive((active) => !active), []);

    const filteredPages = templateType.filter(
        (item: TemplateType) => currentView === 'main' && item.typeName.toLowerCase().includes(search.toLowerCase()),
    );

    const filteredPagesSubmenu =
        selectedParent && currentView === 'submenu'
            ? selectedParent?.filter((item: PageItem) => item.title.toLowerCase().includes(search.toLowerCase()))
            : filteredPages;

    const handlePageSelect = (pageSelected: TemplateType) => {
        setPageSelected(pageSelected);
        const pages = pageList.filter((page: PageItem) => page.templateId === pageSelected.id);
        if (pages) {
            setSelectedParent(pages);
            setCurrentView('submenu');
            setSearch('');
        }
    };

    const handleBackToMain = () => {
        setCurrentView('main');
        setSelectedParent(null);
        setSearch('');
    };

    const handleRename = (childPage: any) => {
        setRenamingItemId(childPage.id);
        setNewPageName(childPage.title);
        setTemplateId(childPage.templateId);
        setPageName(childPage.title);
        setIsRenaming(true);
    };

    const handleSaveRename = () => {
        if (!newPageName.trim()) return;

        if (newPageName !== pageName) {
            updatePage(renamingItemId, { title: newPageName }, { currentPage: 1, perPage: 100, status: 0 });
            setLoading(true);
        } else {
            setIsRenaming(false);
        }
    };

    const handleDuplicate = (childPage: any) => {
        const newId = `${childPage.id}${Date.now()}`;
        const duplicatedPage = {
            ...childPage,
            id: newId,
            title: `${childPage.title} (Copy)`,
        };
        setTemplateId(childPage.templateId);
        duplicatePage(childPage.id, duplicatedPage, { currentPage: 1, perPage: 100, status: 0 });
        setLoading(true);
    };

    const handleDeleteConfirm = (childPage: PageItem) => {
        setItemToDelete(childPage);
        setModalState({ kind: 'delete', inputText: '' });
        setOpenModal(true);
    };

    const handleModalAction = () => {
        if (modalState.kind === 'delete' && itemToDelete) {
            setLoading(true);
            deletePage([itemToDelete.id.toString()], { currentPage: 1, perPage: 100, status: 0 });
            setTemplateId(itemToDelete.templateId);
        }
    };

    const handleNewPageNameChange = useCallback((value: string) => setNewPageName(value), []);

    const handleCreateNewPage = () => {
        if (!selectedParent) return;

        setNewPageName('New page');
        setIsRenaming(true);
    };

    const handleClear = () => {
        setNewPageName('');
        setIsRenaming(false);
        setRenamingItemId('');
    };

    const isOkDisabled = modalState.kind === 'delete' ? false : modalState.inputText.trim() === '';
    const currentModal = {
        title: modalState.kind === 'delete' ? 'Delete page' : 'Rename page',
        okText: modalState.kind === 'delete' ? 'Delete' : 'Save',
        cancelText: 'Cancel',
        showInput: modalState.kind !== 'delete',
    };
    const modalMessage =
        modalState.kind === 'delete' ? (
            <div>Are you sure you want to delete "{itemToDelete?.title}"? </div>
        ) : (
            <div>Enter a new name for this page.</div>
        );
    const handleClosePopover = () => {
        setActive(false);
    };
    const handleOpenPopover = () => {
        handleBackToMain();
        togglePopover();
    };

    const handleCloseModal = () => {
        setOpenModal(false);
    };

    useEffect(() => {
        if (newPageName !== page?.title) {
            const pages = pageList.filter((page: any) => page.templateId === templateId);
            setSelectedParent(pages);
            setIsRenaming(false);
            setLoading(false);
            setOpenModal(false);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [pageList]);

    const handleOpenBuilder = (templateId: string) => {
        getDetailPage(templateId);
        setActive(false);
    };
    const template = templateType.find((template) => template.id === page?.templateId);
    const title = template?.typeName + `  >  ` + page?.title;
    const icon = template?.icon ? <span dangerouslySetInnerHTML={{ __html: template.icon }} /> : null;

    return (
        <>
            <Popover
                active={active}
                preventCloseOnChildOverlayClick
                activator={
                    <div className="page-manager-activator">
                        <Button icon={icon ?? undefined} disclosure variant="tertiary" onClick={handleOpenPopover}>
                            {title}
                        </Button>
                    </div>
                }
                onClose={handleClosePopover}
                autofocusTarget="first-node"
            >
                <Scrollable shadow style={{ maxHeight: '420px' }}>
                    <div className="page-manager">
                        <TextField
                            label="Search"
                            value={search}
                            onChange={handleSearchChange}
                            prefix={<Icon source={SearchIcon} />}
                            placeholder="Search"
                            autoComplete="off"
                            clearButton
                            onClearButtonClick={() => setSearch('')}
                            labelHidden
                            inputMode="search"
                        />
                        {currentView === 'main' ? (
                            <ActionList
                                items={templateType.map((template) => ({
                                    content: template.typeName,
                                    prefix: template?.icon ? (
                                        <span dangerouslySetInnerHTML={{ __html: template.icon }} />
                                    ) : null,
                                    suffix: <Icon source={ChevronRightIcon} />,
                                    onAction: () => handlePageSelect(template),
                                    active: template.id === page?.templateId,
                                }))}
                            />
                        ) : (
                            <div className="submenu-container">
                                <div className="submenu-container__header">
                                    <Button icon={ChevronLeftIcon} onClick={handleBackToMain} variant="tertiary">
                                        {pageSelected?.typeName}
                                    </Button>
                                </div>
                                <Divider />
                                <div className="submenu-container__body">
                                    <ActionList
                                        items={
                                            filteredPagesSubmenu &&
                                            filteredPagesSubmenu.map((childPage: any) => ({
                                                id:
                                                    childPage.id === renamingItemId && isRenaming
                                                        ? 'renaming'
                                                        : undefined,
                                                content:
                                                    childPage.id === renamingItemId && isRenaming ? (
                                                        <TextField
                                                            label="Page name"
                                                            labelHidden
                                                            value={newPageName}
                                                            onChange={handleNewPageNameChange}
                                                            autoComplete="off"
                                                            connectedRight={
                                                                <Button
                                                                    loading={loading}
                                                                    onClick={handleSaveRename}
                                                                    variant="plain"
                                                                >
                                                                    Done
                                                                </Button>
                                                            }
                                                            clearButton={
                                                                childPage.id === renamingItemId && isRenaming
                                                                    ? false
                                                                    : true
                                                            }
                                                            onClearButtonClick={handleClear}
                                                            autoFocus
                                                        />
                                                    ) : (
                                                        <p
                                                            onClick={() => handleOpenBuilder(childPage.id)}
                                                            className="submenu-container__body__title"
                                                        >
                                                            {childPage.title}
                                                        </p>
                                                    ),
                                                prefix:
                                                    childPage.id === renamingItemId && isRenaming ? null : (
                                                        <Icon source={ThemeTemplateIcon} />
                                                    ),
                                                role: childPage.isAction ? 'menuitem' : undefined,
                                                status: childPage.isAction ? 'new' : undefined,
                                                suffix:
                                                    childPage.id === renamingItemId && isRenaming ? null : (
                                                        <div className="submenu-actions">
                                                            <Tooltip content="Rename">
                                                                <Button
                                                                    variant="tertiary"
                                                                    icon={EditIcon}
                                                                    onClick={() => handleRename(childPage)}
                                                                />
                                                            </Tooltip>
                                                            <Tooltip content="Duplicate">
                                                                <Button
                                                                    loading={loading}
                                                                    variant="tertiary"
                                                                    icon={DuplicateIcon}
                                                                    onClick={() => {
                                                                        handleDuplicate(childPage);
                                                                    }}
                                                                />
                                                            </Tooltip>
                                                            {childPage.id !== page?.id && (
                                                                <Tooltip content="Delete">
                                                                    <Button
                                                                        variant="tertiary"
                                                                        icon={DeleteIcon}
                                                                        tone="critical"
                                                                        onClick={() => {
                                                                            handleDeleteConfirm(childPage);
                                                                        }}
                                                                    />
                                                                </Tooltip>
                                                            )}
                                                        </div>
                                                    ),
                                                active: childPage.id === page?.id,
                                            }))
                                        }
                                    />
                                </div>
                                <div className="submenu-container__footer">
                                    <Button variant="plain" icon={PlusCircleIcon} onClick={handleCreateNewPage}>
                                        Create new a page
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </Scrollable>
            </Popover>
            <Modal open={openModal} onClose={handleCloseModal} title={currentModal.title}>
                <Modal.Section>
                    <BlockStack gap="500">
                        {modalMessage}
                        {currentModal.showInput && (
                            <div className="text-field-wrapper">
                                <TextField
                                    label={`Page title`}
                                    value={modalState.inputText}
                                    onChange={(value) => setModalState((prev) => ({ ...prev, inputText: value }))}
                                    autoComplete="off"
                                    autoFocus
                                    clearButton
                                    maxLength={255}
                                    showCharacterCount
                                    onClearButtonClick={() => setModalState((prev) => ({ ...prev, inputText: '' }))}
                                />
                            </div>
                        )}
                    </BlockStack>
                </Modal.Section>
                <Modal.Section>
                    <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <Button onClick={handleCloseModal}>{currentModal.cancelText}</Button>
                        <Button
                            variant="primary"
                            tone={modalState.kind === 'delete' ? 'critical' : undefined}
                            disabled={isOkDisabled}
                            onClick={handleModalAction}
                            loading={loading}
                        >
                            {currentModal.okText}
                        </Button>
                    </div>
                </Modal.Section>
            </Modal>
        </>
    );
};
