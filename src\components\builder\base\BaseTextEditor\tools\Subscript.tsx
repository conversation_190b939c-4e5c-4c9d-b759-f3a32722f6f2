import { BaseTool } from './BaseTool';
import { useRTE } from '../context';

export const Subscript = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('subscript');
    const toggleSubscript = () => {
        editor?.chain().focus().toggleSubscript().run();
    };

    const subscriptIcon = (
        <svg width="14" height="22" viewBox="0 0 14 22" fill="currentColor">
            <path
                d="M2.1875 7.9375V6.1875H10.9375V7.9375"
                stroke="black"
                strokeLinecap="round"
                strokeLinejoin="round"
            ></path>
            <path d="M6.5625 6.1875V15.8125" stroke="black" strokeLinecap="round" strokeLinejoin="round"></path>
            <path d="M4.8125 15.8125H8.3125" stroke="black" strokeLinecap="round" strokeLinejoin="round"></path>
            <path
                d="M13.1249 17.4086H10.4185V16.7453L11.3285 15.824C11.5893 15.5527 11.7596 15.3672 11.8395 15.2675C11.9054 15.1891 11.9616 15.103 12.0066 15.0111C12.0372 14.9401 12.053 14.8635 12.053 14.7862C12.0551 14.7435 12.0478 14.7008 12.0316 14.6612C12.0154 14.6216 11.9908 14.586 11.9594 14.557C11.8856 14.4966 11.7922 14.4654 11.6969 14.4695C11.5717 14.4714 11.4492 14.5061 11.3416 14.5701C11.1966 14.6557 11.0603 14.7553 10.9348 14.8676L10.3809 14.2192C10.5297 14.0804 10.6921 13.957 10.8656 13.8508C10.9998 13.7758 11.1434 13.719 11.2926 13.682C11.4628 13.641 11.6374 13.6219 11.8124 13.6251C12.0345 13.6216 12.2549 13.6651 12.459 13.7528C12.6367 13.8297 12.7886 13.9561 12.8965 14.1168C13.0002 14.2721 13.0548 14.4551 13.0531 14.6418C13.0571 14.9159 12.9791 15.185 12.8291 15.4145C12.7395 15.5501 12.6373 15.677 12.5238 15.7933C12.3966 15.9252 12.1268 16.1769 11.7144 16.5485V16.5738H13.1249V17.4086Z"
                fill="black"
                fillOpacity="0.85"
            ></path>
        </svg>
    );

    return <BaseTool onClick={toggleSubscript} tooltip="Subscript" isActive={isActive} customIcon={subscriptIcon} />;
};
