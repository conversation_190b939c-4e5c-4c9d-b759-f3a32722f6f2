import { BaseTool } from './BaseTool';
import { useRTE } from '../context';

export const BulletList = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('bulletList');
    const toggleBulletList = () => {
        editor?.chain().focus().toggleBulletList().run();
    };

    const bulletListIcon = (
        <svg viewBox="0 0 20 20" fill="none" width="16" height="16">
            <path
                d="M3.125 2.1875C2.08947 2.1875 1.25 3.02697 1.25 4.0625C1.25 5.09803 2.08947 5.9375 3.125 5.9375C4.16053 5.9375 5 5.09803 5 4.0625C5 3.02697 4.16053 2.1875 3.125 2.1875Z"
                fill="currentColor"
            ></path>
            <path
                d="M3.125 8.125C2.08947 8.125 1.25 8.96447 1.25 10C1.25 11.0355 2.08947 11.875 3.125 11.875C4.16053 11.875 5 11.0355 5 10C5 8.96447 4.16053 8.125 3.125 8.125Z"
                fill="currentColor"
            ></path>
            <path
                d="M1.25 15.9375C1.25 14.902 2.08947 14.0625 3.125 14.0625C4.16053 14.0625 5 14.902 5 15.9375C5 16.973 4.16053 17.8125 3.125 17.8125C2.08947 17.8125 1.25 16.973 1.25 15.9375Z"
                fill="currentColor"
            ></path>
            <path
                d="M7.18719 3.4375C6.84202 3.4375 6.56219 3.71732 6.56219 4.0625C6.56219 4.40768 6.84202 4.6875 7.18719 4.6875H18.125C18.4702 4.6875 18.75 4.40768 18.75 4.0625C18.75 3.71732 18.4702 3.4375 18.125 3.4375H7.18719Z"
                fill="currentColor"
            ></path>
            <path
                d="M6.56219 10C6.56219 9.65482 6.84202 9.375 7.18719 9.375H18.125C18.4702 9.375 18.75 9.65482 18.75 10C18.75 10.3452 18.4702 10.625 18.125 10.625H7.18719C6.84202 10.625 6.56219 10.3452 6.56219 10Z"
                fill="currentColor"
            ></path>
            <path
                d="M7.18719 15.3125C6.84202 15.3125 6.56219 15.5923 6.56219 15.9375C6.56219 16.2827 6.84202 16.5625 7.18719 16.5625H18.125C18.4702 16.5625 18.75 16.2827 18.75 15.9375C18.75 15.5923 18.4702 15.3125 18.125 15.3125H7.18719Z"
                fill="currentColor"
            ></path>
        </svg>
    );
    return (
        <BaseTool customIcon={bulletListIcon} isActive={isActive} onClick={toggleBulletList} tooltip="Bullet List" />
    );
};
