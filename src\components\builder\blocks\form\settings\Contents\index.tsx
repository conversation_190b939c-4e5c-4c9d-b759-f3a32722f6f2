import { BaseItemLayout } from '@/components/builder/base';
import { BaseNoBorderLayout } from '@/components/builder/base/BaseNoBorderLayout';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingsSortable } from '@/components/builder/settings';
import { BlockStack, Button } from '@shopify/polaris';
import { PlusCircleIcon } from '@shopify/polaris-icons';
import { useAppStore } from '@/stores/appStore';
import { SortableFormField } from './renderer/SortableFormField';
export const Contents = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const setIsAddNewFormField = useAppStore((state) => state.setIsAddNewFormField);

    return (
        <BaseNoBorderLayout>
            <BaseItemLayout direction="column" textProps={{ as: 'p', variant: 'headingMd', children: 'Main' }}>
                <BlockStack gap="300">
                    <SettingsSortable
                        blockId={selectedBlockId}
                        path="form"
                        isUpdateConfigs
                        handle={true}
                        useDragOverlay={true}
                        containerProps={{ className: 'form-field-container' }}
                        itemRenderer={(props) => <SortableFormField {...props} blockId={selectedBlockId} />}
                    />
                    <Button
                        textAlign="left"
                        variant="plain"
                        pressed={false}
                        icon={PlusCircleIcon}
                        onClick={() => setIsAddNewFormField(true)}
                    >
                        Add new
                    </Button>
                </BlockStack>

                <SettingsInput
                    label="Form title"
                    path="contentForm.formTitle"
                    blockId={selectedBlockId || ''}
                    isUpdateConfigs
                    direction="column"
                    inputProps={{
                        type: 'text',
                        placeholder: 'Enter form title',
                    }}
                />
                <SettingsInput
                    label="Button title"
                    path="contentForm.buttonTitle"
                    blockId={selectedBlockId || ''}
                    isUpdateConfigs
                    direction="column"
                    inputProps={{
                        type: 'text',
                        placeholder: 'Enter button title',
                    }}
                />
            </BaseItemLayout>
        </BaseNoBorderLayout>
    );
};
