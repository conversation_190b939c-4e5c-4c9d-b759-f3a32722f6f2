import { FC, useState, useCallback } from 'react';
import { Text, BlockStack, Banner, Box, Link, Button, InlineStack } from '@shopify/polaris';
import { Modal, TitleBar, useAppBridge } from '@shopify/app-bridge-react';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { pathnames } from '@/configs';
export const ResetSettings: FC = () => {
    const shopify = useAppBridge();
    const [isDismissed, setIsDismissed] = useState(true);
    const [isOpenModal, setIsOpenModal] = useState(false);
    const apiStatus = useAppStore((state) => state.apiStatus);
    const navigate = useNavigate();
    const { resetAppSettings } = useAppStore();
    const handleDismiss = () => {
        setIsDismissed(false);
    };

    const handleReset = useCallback(() => {
        setIsOpenModal(true);
    }, []);

    const handleCloseModal = useCallback(() => {
        setIsOpenModal(false);
    }, []);

    const handleResetApp = useCallback(async () => {
        try {
            await resetAppSettings();
            setIsOpenModal(false);
            shopify.toast.show('App settings reset successfully', {
                duration: 1500,
                isError: false,
            });
            navigate(pathnames.dashboard);
        } catch (error) {
            shopify.toast.show('Failed to reset app settings' + error, {
                duration: 1500,
                isError: true,
            });
        }
    }, [shopify, resetAppSettings, navigate]);

    return (
        <Box padding="400" background="bg-fill" width="100%" borderRadius="300" shadow="button">
            <BlockStack gap="400">
                <Text as="p" fontWeight="medium">
                    Reset the app
                </Text>
                {isDismissed && (
                    <Banner title="Careful" tone="warning" onDismiss={handleDismiss}>
                        <p>
                            To uninstall Autoketing Builder safely, please click "Reset" button here to fully remove all
                            app data and codes from your theme. Then remove app from your Shopify admin.
                        </p>
                    </Banner>
                )}
                <Text as="p">
                    Be careful because this action cannot be reversed! <Link url="#">See detailed guide</Link>
                </Text>
                <InlineStack gap="200" align="end">
                    <Button onClick={handleReset}>Reset</Button>
                </InlineStack>

                <Modal open={isOpenModal}>
                    <TitleBar title="Reset Autoketing Builder ">
                        <button onClick={handleCloseModal}>No, don’t reset</button>
                        <button
                            loading={apiStatus === 'loading' ? '' : undefined}
                            variant={'primary'}
                            tone={'critical'}
                            onClick={handleResetApp}
                        >
                            Reset app
                        </button>
                    </TitleBar>
                    <Box padding="400" paddingBlockEnd="800">
                        Lorem ipsum dolor sit amet consectetur. Risus nec sagittis eget proin vitae.?
                    </Box>
                </Modal>
            </BlockStack>
        </Box>
    );
};
