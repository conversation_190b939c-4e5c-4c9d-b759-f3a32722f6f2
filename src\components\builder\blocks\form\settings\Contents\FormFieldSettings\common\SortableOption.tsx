/* eslint-disable @typescript-eslint/no-explicit-any */
import { SortableItemRequireProps, RendererProps } from '@/components/Sortable/types';
import { Box, InlineStack, Button, Popover, Text, Icon, BlockStack } from '@shopify/polaris';
import { useState, useCallback } from 'react';
import { DragHandleIcon, XIcon, InfoIcon } from '@shopify/polaris-icons';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { useBlockStore } from '@giaminhautoketing/auto-builder';
import { findIndexFormField } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { SettingsCheckbox } from '@/components/builder/settings/SettingsCheckbox';
import { SettingsChooseImage } from '@/components/builder/settings/SettingsChooseImage';
import { BaseItemLayout } from '@/components/builder/base';
import './sortableOption.scss';
export function SortableOption<T extends SortableItemRequireProps>({
    blockId,
    index,
    clone,
    data,
    isDragging,
    onRemove,
    insertPosition,
    handleProps,
    type,
}: RendererProps<T> & {
    blockId: string;
    type: string;
}) {
    const [activePopover, setActivePopover] = useState<string | null>(null);

    const handleEditOption = useCallback(
        (item: T) => {
            setActivePopover((prev) => (prev === item.value ? null : item.value));
        },
        [setActivePopover],
    );

    const isGhost = isDragging && !clone;
    const formArr = useBlockStore((state) => state.blocks[blockId]?.configs?.form) as FormFieldType[];
    const indexCheckboxField = findIndexFormField(formArr, data, type as any);

    const DEFAULT_PATH_FORM = `form.${indexCheckboxField}` as const;
    const OPTION_PATH = `${DEFAULT_PATH_FORM}.options.${index}`;

    return (
        <Popover
            active={activePopover !== null}
            onClose={() => setActivePopover(null)}
            fullHeight
            preferredAlignment="right"
            zIndexOverride={98}
            fullWidth
            activator={
                <div
                    key={data.id}
                    className={`option ${insertPosition ? `insert-${insertPosition}` : ''} ${isGhost ? 'ghost' : ''}`}
                    onClick={() => handleEditOption(data)}
                >
                    <div className="option-label">
                        <div className="option-label-drag-handle" {...handleProps}>
                            <Button icon={DragHandleIcon} variant="plain" pressed={false} />
                        </div>
                        <div className="option-label-text">{isGhost ? '' : data.label}</div>
                    </div>
                    <div className="option-remove">
                        <Button icon={XIcon} variant="plain" pressed={false} onClick={() => onRemove?.()} />
                    </div>
                </div>
            }
        >
            <Popover.Pane height="100%">
                <Box padding="300" id="settings-option">
                    <BlockStack gap="300">
                        <Box borderBlockEndWidth="025" paddingBlockEnd="200" borderColor="border">
                            <InlineStack gap="200" align="space-between" blockAlign="center">
                                <Text as="p" variant="bodyMd" fontWeight="medium">
                                    Option Settings
                                </Text>
                                <Button
                                    icon={XIcon}
                                    variant="plain"
                                    pressed={false}
                                    onClick={() => setActivePopover(null)}
                                />
                            </InlineStack>
                        </Box>
                        <SettingsInput
                            path={`${OPTION_PATH}.label`}
                            blockId={blockId}
                            isUpdateConfigs
                            inputProps={{ type: 'text', placeholder: 'Enter label' }}
                            direction="column"
                            label="Title"
                            textProps={{ fontWeight: 'medium' }}
                            tooltipContent="Add label to the option"
                            hasTooltip
                            tooltipChildren={<Icon source={InfoIcon} />}
                        />
                        {type === 'checkbox' && (
                            <SettingsInput
                                path={`${OPTION_PATH}.key`}
                                blockId={blockId}
                                isUpdateConfigs
                                inputProps={{ type: 'text' }}
                                direction="column"
                                label="Key"
                                textProps={{ fontWeight: 'medium' }}
                                tooltipContent="Key is only, required and cannot be duplicated"
                                hasTooltip
                                tooltipChildren={<Icon source={InfoIcon} />}
                            />
                        )}
                        <SettingsInput
                            path={`${OPTION_PATH}.value`}
                            blockId={blockId}
                            isUpdateConfigs
                            inputProps={{ type: 'text', placeholder: 'Enter value' }}
                            direction="column"
                            label="Value"
                            textProps={{ fontWeight: 'medium' }}
                            tooltipContent="Add value to the option"
                            hasTooltip
                            tooltipChildren={<Icon source={InfoIcon} />}
                        />
                        {type === 'checkbox' && (
                            <>
                                <SettingsCheckbox
                                    path={`${OPTION_PATH}.defaultChecked`}
                                    blockId={blockId}
                                    isUpdateConfigs
                                    direction="column"
                                    label="Default checked"
                                    textProps={{ fontWeight: 'medium' }}
                                />
                                <BaseItemLayout
                                    direction="column"
                                    textProps={{ as: 'p', variant: 'headingMd', children: 'Image' }}
                                >
                                    <SettingsChooseImage
                                        path={`${OPTION_PATH}.checkboxImage`}
                                        blockId={blockId}
                                        isUpdateConfigs
                                    />
                                </BaseItemLayout>
                            </>
                        )}
                    </BlockStack>
                </Box>
            </Popover.Pane>
        </Popover>
    );
}
