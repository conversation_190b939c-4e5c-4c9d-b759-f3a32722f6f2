import { useState } from 'react';
import { ActionList, Button, DropZone, Icon, Popover, Tabs, Text } from '@shopify/polaris';
import { CheckSmallIcon } from '@shopify/polaris-icons';
import { BaseModal } from '@/components/builder/base';
import { getEditorDefaults } from '@pqina/pintura';
import { PinturaEditor } from '@pqina/react-pintura';
import { useMutation } from '@/hooks';
import { apiAddress } from '@/configs/apiAddress';
import { useMediaManager } from './useMediaManager';
import { Upload, Autoketing, Unplash } from './contents';
import { listCategory, listType } from './configs';
import { MediaManagerTab } from './types';
import '@pqina/pintura/pintura.css';
import './styles.scss';

const editorConfig = getEditorDefaults();

export const MediaManager = () => {
    const [openDropdown, setOpenDropdown] = useState(false);
    const open = useMediaManager((state) => state.open);
    const sortBy = useMediaManager((state) => state.sortBy);
    const currentTab = useMediaManager((state) => state.currentTab);
    const selected = useMediaManager((state) => state.selected);
    const multiple = useMediaManager((state) => state.multiple);
    const setSortBy = useMediaManager((state) => state.setSortBy);
    const setOpen = useMediaManager((state) => state.setOpen);
    const setCurrentTab = useMediaManager((state) => state.setCurrentTab);
    const onSelect = useMediaManager((state) => state.onSelect);

    const mutation = useMutation({
        url: apiAddress.media.index,
        method: 'POST',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        timeout: 50000,
        onMutate: () => {
            shopify.toast.show('Uploading...');
        },
        onSuccess: () => {
            shopify.toast.show('Uploaded successfully');
            setOpen(
                { mode: 'manager' },
                { page: 1, endCursor: '', currentTab: 'upload', sortBy: 'all', forceUpdate: true },
            );
        },
        onError: () => {
            shopify.toast.show('Upload failed', { isError: true });
        },
    });

    const tabMap = { upload: 0, autoketing: 1, unplash: 2 };

    const tabs = [
        {
            id: 'upload',
            content: 'Uploaded',
            accessibilityLabel: 'Upload new media',
            panelID: 'upload-panel',
            component: <Upload />,
        },
        {
            id: 'autoketing',
            content: 'From Autoketing Builder',
            accessibilityLabel: 'From Autoketing Builder',
            panelID: 'autoketing-panel',
            component: <Autoketing />,
        },
        {
            id: 'unplash',
            content: 'Unplash',
            accessibilityLabel: 'Unplash',
            panelID: 'unplash-panel',
            component: <Unplash />,
        },
    ];

    const isUnplash = currentTab === 'unplash';

    const options = {
        upload: listType,
        autoketing: listType,
        unplash: listCategory,
    }[currentTab];

    const actionListItems = options.map(({ label, value }) => ({
        active: sortBy === value,
        content: label,
        suffix: sortBy === value && <Icon source={CheckSmallIcon} tone="emphasis" />,
        onAction: () => {
            setSortBy(value);
            setOpenDropdown(false);
        },
        truncate: true,
    }));

    const onClose = () => setOpen(false);

    const handleSelect = () => {
        if (selected.length > 0) {
            onSelect?.(multiple ? selected : selected[0]);
            onClose();
        }
    };

    const handleUpload = (files: File[]) => {
        const formData = new FormData();
        formData.append('mediaType', isUnplash || sortBy === 'all' ? 'image' : sortBy);
        files.forEach((file) => {
            formData.append('files[]', file);
        });
        mutation.mutate(formData);
    };

    return (
        <BaseModal
            isOpen={!!open}
            onCancel={onClose}
            okTitle="Add"
            onOk={handleSelect}
            okElementProps={{ disabled: selected.length === 0 }}
            hiddenFooter={open && open.mode === 'editor'}
            elementModalContentProps={{
                title: open && open.mode === 'manager' ? 'Media Manager' : 'Media Studio',
                children: (
                    <div className="media-manager" data-mode={open && open.mode}>
                        {open === false ? null : open.mode === 'manager' ? (
                            <>
                                <div css={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Tabs
                                        tabs={tabs}
                                        selected={tabMap[currentTab]}
                                        onSelect={(selectedTabIndex) => {
                                            setCurrentTab(Object.keys(tabMap)[selectedTabIndex] as MediaManagerTab);
                                        }}
                                    />
                                    <div css={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                        <Text as="p" variant="bodySm" css={{ fontWeight: 500 }}>
                                            Filter by:
                                        </Text>
                                        <div css={{ width: isUnplash ? '134px' : '110px' }}>
                                            <Popover
                                                active={openDropdown}
                                                activator={
                                                    <Button
                                                        disclosure
                                                        fullWidth
                                                        textAlign="left"
                                                        onClick={() => setOpenDropdown(!openDropdown)}
                                                    >
                                                        {options.find(({ value }) => value === sortBy)?.label}
                                                    </Button>
                                                }
                                                onClose={() => setOpenDropdown(false)}
                                                preferredAlignment="left"
                                            >
                                                <div css={{ width: isUnplash ? '134px' : '110px' }}>
                                                    <ActionList actionRole="menuitem" items={actionListItems} />
                                                </div>
                                            </Popover>
                                        </div>
                                        <div className="dropzone-hidden">
                                            <DropZone
                                                outline={false}
                                                onDrop={(_, acceptedFiles) => {
                                                    handleUpload(acceptedFiles);
                                                }}
                                                accept={
                                                    isUnplash || sortBy === 'all'
                                                        ? 'image/*'
                                                        : sortBy === 'image'
                                                        ? 'image/*'
                                                        : 'video/*'
                                                }
                                            >
                                                <Button variant="primary" size="medium" loading={mutation.isLoading}>
                                                    Upload
                                                </Button>
                                            </DropZone>
                                        </div>
                                    </div>
                                </div>
                                <div className="media-manager-content">{tabs[tabMap[currentTab]].component}</div>
                            </>
                        ) : (
                            <PinturaEditor
                                {...editorConfig}
                                src={open?.url}
                                enableButtonClose
                                onProcess={(data) => {
                                    handleUpload([data.dest]);
                                }}
                                onClose={() => {
                                    setOpen({ mode: 'manager' }, { page: 1, endCursor: '' });
                                }}
                            />
                        )}
                    </div>
                ),
            }}
        />
    );
};
