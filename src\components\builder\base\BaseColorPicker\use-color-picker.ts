import { useId } from 'react';
import * as colorPicker from '@zag-js/color-picker';
import { type PropTypes, normalizeProps, useMachine } from '@zag-js/react';
import { Optional } from './types';

export type UseColorPickerProps = Optional<colorPicker.Props, 'id'>;

export type UseColorPickerReturn = colorPicker.Api<PropTypes> & {
    onValueChangeEnd?: (value: colorPicker.ValueChangeDetails) => void;
};

export function useColorPicker(props?: UseColorPickerProps) {
    const id = useId();

    const machineProps: colorPicker.Props = {
        id,
        ...props,
    };
    const service = useMachine(colorPicker.machine, machineProps);
    const api = colorPicker.connect(service, normalizeProps);

    return { ...api, onValueChangeEnd: machineProps.onValueChangeEnd };
}
