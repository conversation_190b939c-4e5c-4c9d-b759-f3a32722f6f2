.shared-floating-base-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 10px;
    z-index: 99;
    width: calc(100vw - 7.5rem);
    height: calc(100vh - 7.5rem);
    max-width: 82.5rem;
    max-height: 56.25rem;
    overflow-y: auto;
    &__content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &__close {
            position: absolute;
            top: 1.125rem;
            right: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            .Polaris-Button {
                min-width: 20px;
                min-height: 20px;
                width: 20px;
                height: 20px;
                padding: 0;
                margin: 0;
                &:hover {
                    background-color: transparent;
                }
                svg {
                    fill: var(--p-color-text);
                }
            }
        }
        &__body {
            width: 100%;
            height: 100%;
            overflow-y: auto;
        }
        &__footer {
            padding: 1.25rem 1rem;
        }
    }
}
