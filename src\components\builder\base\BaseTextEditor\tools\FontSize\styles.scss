[data-scope='combobox'][data-part='root'] {
    position: relative;
    width: 52px;
}

[data-scope='combobox'][data-part='trigger'] {
    width: 100%;
}

[data-scope='combobox'][data-part='label'] {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #202223;
    margin-bottom: 4px;
}

[data-scope='combobox'][data-part='input-wrapper'] {
    position: relative;
    display: flex;
    align-items: center;
}

[data-scope='combobox'][data-part='input'] {
    width: 100%;
    height: 36px;
    padding: 0 4px;
    // border: 1px solid #c9cccf;
    border: none;
    border-radius: 4px;
    font-size: 15px;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
    color: #202223;
    background-color: transparent;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

[data-scope='combobox'][data-part='input'][data-has-icon-search] {
    padding-left: 32px;
}

// [data-scope='combobox'][data-part='input'][data-has-icon-arrow] {
//     padding-right: 36px;
// }

[data-scope='combobox'][data-part='input']:focus {
    border-color: #6086f2;
}

[data-scope='combobox'][data-part='input'][data-has-error] {
    border-color: #d82c0d;
}

[data-scope='combobox'][data-part='input'][data-has-error]:focus {
    border-color: #d82c0d;
}

[data-scope='combobox'][data-part='input'][data-disabled] {
    background-color: #f6f6f7;
    color: #8c9196;
    cursor: not-allowed;
}

[data-scope='combobox'][data-part='arrow'] {
    position: absolute;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    color: #5c5f62;
    cursor: pointer;
    padding: 0;
}

[data-scope='combobox'][data-part='arrow'][data-state='open'] svg {
    transform: rotate(180deg);
}

[data-scope='combobox'][data-part='arrow'][data-state='closed'] svg {
    transform: rotate(0deg);
}

[data-scope='combobox'][data-part='search-icon'] {
    position: absolute;
    left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    color: #5c5f62;
    pointer-events: none;
}

[data-scope='combobox'][data-part='loading-indicator'] {
    position: absolute;
    right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

[data-scope='combobox'][data-part='spinner'] {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(140, 145, 150, 0.3);
    border-top-color: #5c5f62;
    border-radius: 50%;
    animation: combobox-spin 0.6s linear infinite;
}

@keyframes combobox-spin {
    to {
        transform: rotate(360deg);
    }
}

[data-scope='combobox'][data-part='content'] {
    position: absolute;
    z-index: 100;
    width: 100%;
    margin-top: 4px;
    background-color: #ffffff;
    border: 1px solid #c9cccf;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    overflow-y: auto;
}
[data-scope='combobox'][data-part='content'][data-hide-empty-state='true'] {
    border: none;
    [data-scope='combobox'][data-part='empty'] {
        display: none;
    }
}

[data-scope='combobox'][data-part='content-inner'] {
    padding: 0;
}

[data-scope='combobox'][data-part='options'] {
    margin: 0;
    padding: 0;
    list-style: none;
}

[data-scope='combobox'][data-part='option'] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 12px;
    font-size: 12px;
    color: #202223;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

[data-scope='combobox'][data-part='option']:not([data-disabled]):hover,
[data-scope='combobox'][data-part='option'][data-highlighted]:not([data-disabled]) {
    background-color: #e5e5e5;
}

[data-scope='combobox'][data-part='option'][data-selected] {
    color: #6086f2;
}

[data-scope='combobox'][data-part='option'][data-disabled] {
    color: #8c9196;
    cursor: not-allowed;
}

[data-scope='combobox'][data-part='option-text'] {
    flex: 1;
}

[data-scope='combobox'][data-part='option-check'] {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    color: #6086f2;
    flex-shrink: 0;
}

[data-scope='combobox'][data-part='empty'] {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 4px;
    color: #5c5f62;
    font-size: 12px;
}

[data-scope='combobox'][data-part='loading'] {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    color: #5c5f62;
    font-size: 14px;
    gap: 8px;
}

[data-scope='combobox'][data-part='error'] {
    margin-top: 4px;
    font-size: 12px;
    color: #d82c0d;
}

[data-scope='combobox'][data-part='help-text'] {
    margin-top: 4px;
    font-size: 12px;
    color: #6d7175;
}
