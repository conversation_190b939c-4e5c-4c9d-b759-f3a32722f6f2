import { Auto_BlockToolbar, genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const video1: Auto_BlockToolbar = {
    id: genRandomBlockId(),
    cname: 'video',
    label: 'Video',
    type: 'video',
    configs: {
        displayOnDesktop: true,
        displayOnTablet: true,
        displayOnMobile: true,
        url: 'https://www.w3schools.com/html/mov_bbb.mp4',
        showControls: true,
        loop: true,
        autoplay: false,
        playAudio: false,
    },
    bpConfigs: {
        desktop: {
            color: '#ffffff',
            width: { val: '300', unit: 'px' },
            height: { val: '180', unit: 'px' },
            border: {
                radius: {
                    'top-left': { val: '8', unit: 'px' },
                    'top-right': { val: '8', unit: 'px' },
                    'bottom-right': { val: '8', unit: 'px' },
                    'bottom-left': { val: '8', unit: 'px' },
                },
            },
        },
        tablet: {},
        mobile: {},
    },
    overlay: {
        desktop: {
            width: 300,
            height: 180,
        },
        tablet: {
            width: 300,
            height: 180,
        },
        mobile: {
            width: 300,
            height: 180,
        },
    },
};
