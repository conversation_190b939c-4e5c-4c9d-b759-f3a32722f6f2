import { FC, Fragment } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import {
    DATA_SET_FORM_FIELD,
    DATA_SET_FORM_FIELD_LABEL,
    DATA_SET_FORM_SEPARATOR_LINE,
} from '@/components/builder/blocks/form/constants';

import { FormInput } from '@/components/builder/blocks/form/components/FormInput';
import { FormCheckbox } from '@/components/builder/blocks/form/components/FormCheckBox';
import { FormRadio } from '@/components/builder/blocks/form/components/FormRadio';
import { FormDropdown } from '@/components/builder/blocks/form/components/FormDropdown';
import { FormTextarea } from '@/components/builder/blocks/form/components/FormTextarea';
import { FormFreeText } from '@/components/builder/blocks/form/components/FormFreeText';

interface FormFieldProps {
    field: FormFieldType;
    index: number;
    autoId: string;
}

export const FormField: FC<FormFieldProps> = ({ field, index, autoId }) => {
    const renderFieldContent = () => {
        switch (field.type) {
            case 'text':
            case 'email':
            case 'password':
            case 'number':
            case 'tel':
                return <FormInput field={field} index={index} autoId={autoId} />;
            case 'checkbox':
                return <FormCheckbox field={field} index={index} autoId={autoId} />;
            case 'radio':
                return <FormRadio field={field} index={index} autoId={autoId} />;
            case 'dropdown':
                return <FormDropdown field={field} index={index} autoId={autoId} />;
            case 'long-text':
                return <FormTextarea field={field} index={index} autoId={autoId} />;
            case 'free-text':
                return <FormFreeText field={field} index={index} autoId={autoId} />;
            default:
                return null;
        }
    };

    return (
        <Fragment>
            <span {...{ [`${DATA_SET_FORM_SEPARATOR_LINE}-${index}`]: autoId }} />
            <div {...{ [`${DATA_SET_FORM_FIELD}`]: autoId }}>
                {field.type !== 'free-text' && (
                    <label {...{ [`${DATA_SET_FORM_FIELD_LABEL}`]: autoId }}>
                        {field.label}
                        {field.validations?.required && <span style={{ color: 'red' }}>*</span>}
                    </label>
                )}
                {renderFieldContent()}
            </div>
        </Fragment>
    );
};
