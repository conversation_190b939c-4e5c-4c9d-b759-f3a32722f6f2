import { FC, PropsWithChildren } from 'react';
import {
    Auto_BlockViewer,
    BlockViewer,
    DATA_SET_AUTO_ID_INNER,
    DATA_SET_CNAME,
    DATA_SET_DROP,
    DATA_SET_DROP_INNER,
} from '@giaminhautoketing/auto-builder';

export const SectionViewer: FC<PropsWithChildren<Auto_BlockViewer>> = ({
    children,
    autoId,
    cname,
    label,
    type,
    bpConfigs,
    configs,
}) => (
    <BlockViewer
        autoId={autoId}
        cname={cname}
        label={label}
        type={type}
        bpConfigs={bpConfigs}
        configs={configs}
        className="section"
        attrs={{ [DATA_SET_DROP]: true }}
    >
        <div
            {...{ [DATA_SET_AUTO_ID_INNER]: autoId, [DATA_SET_CNAME]: cname, [DATA_SET_DROP_INNER]: true }}
            className="section__inner"
        >
            {children}
        </div>
    </BlockViewer>
);
