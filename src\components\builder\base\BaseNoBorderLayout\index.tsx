import { BlockStack, Box, Scrollable } from '@shopify/polaris';
import { FC } from 'react';
import clsx from 'clsx';
import './style.scss';

interface BaseNoBorderLayoutProps {
    children: React.ReactNode;
    containerClassName?: string;
}

export const BaseNoBorderLayout: FC<BaseNoBorderLayoutProps> = ({ children, containerClassName }) => {
    return (
        <div className={clsx('base-no-border-layout', containerClassName)}>
            <Scrollable style={{ height: 'calc(100% - 52px)', position: 'absolute', inset: 0 }} scrollbarWidth="none">
                <Box paddingBlockStart="100" paddingBlockEnd="400" paddingInline="200">
                    <BlockStack gap="400">{children}</BlockStack>
                </Box>
            </Scrollable>
        </div>
    );
};
