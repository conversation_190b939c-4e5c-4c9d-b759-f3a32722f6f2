import { FC, useState } from 'react';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { SettingsCustomCSS } from '@/components/builder/settings/SettingsCustomCSS';
import { BaseToggle } from '@/components/builder/base/BaseToggle';
interface CustomCSSProps {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
}

export const CustomCSS: FC<CustomCSSProps> = ({ path, blockId, isUpdateConfigs, label }) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <BaseCollapse
            label={label}
            open={isOpen}
            labelContent={
                <BaseToggle valueData={isOpen} onChange={() => setIsOpen(!isOpen)} id={`css-${blockId}-${path}`} />
            }
        >
            <SettingsCustomCSS path={path} blockId={blockId} isUpdateConfigs={isUpdateConfigs} label={label} />
        </BaseCollapse>
    );
};
