/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
import { BreadcrumbConfigs } from '../types';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
    DATA_SET_ATK_ANIMATION,
} from '@/components/builder/constants/constants';
import { DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';
import {
    DATA_SET_BREADCRUMB_POSITION,
    DATA_SET_BREADCRUMB_CUSTOM_CSS_STYLE,
    DATA_SET_BREADCRUMB_CUSTOM_CSS_CLASS,
    DATA_SET_BREADCRUMB_CUSTOM_CSS_ENABLE,
} from '../constants';

const getCustomCssAttributes = (configs: BreadcrumbConfigs) => {
    if (!configs?.customCSS?.enable) return {};

    return {
        [DATA_SET_BREADCRUMB_CUSTOM_CSS_CLASS]: configs.customCSS?.className || undefined,
        [DATA_SET_BREADCRUMB_CUSTOM_CSS_STYLE]: configs.customCSS?.style || undefined,
    };
};

const hasPosition = (position: any, autoId: string) => {
    if (!position) return false;
    return position.positionType !== 'default' ? autoId : undefined;
};

const hasAnimation = (animation: BreadcrumbConfigs['animation']) => {
    if (!animation) return false;
    return animation.type && animation.type !== 'none';
};

    const generateBreadcrumbAttributes = (
    configs: BreadcrumbConfigs,
    autoId: string,
    currentDevice: string,
    bpConfigs: any,
) => {
    if (!configs) return { [DATA_SET_AUTO_ID_INNER]: autoId };

    const displayAttr =
        currentDevice === 'desktop'
            ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
            : { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile };

    return {
        [DATA_SET_AUTO_ID_INNER]: autoId,
        ...displayAttr,
        [DATA_SET_BREADCRUMB_POSITION]: hasPosition(bpConfigs[currentDevice]?.position, autoId),
        [DATA_SET_ATK_ANIMATION]: hasAnimation(configs.animation),  
        [DATA_SET_BREADCRUMB_CUSTOM_CSS_ENABLE]: configs?.customCSS?.enable,
        ...getCustomCssAttributes(configs),
        className: 'animate__animated',
    };
};

// Hook
export const useBreadcrumbAttributes = (
    configs: BreadcrumbConfigs,
    autoId: string,
    currentDevice: string,
    bpConfigs: any,
) => {
    return useMemo(
        () => generateBreadcrumbAttributes(configs, autoId, currentDevice, bpConfigs),
        [configs, autoId, currentDevice, bpConfigs],
    );
}; 