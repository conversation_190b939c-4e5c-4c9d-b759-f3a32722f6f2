@mixin device-display {
    display: block !important;
    background: repeating-linear-gradient(45deg, #d3d3d3 0, #d3d3d3 10%, transparent 0, transparent 50%) !important;
    background-size: 1em 1em !important;
    background-color: #fff !important;
    opacity: 0.1 !important;
    filter: brightness(80%) !important;
}

[data-atk-display-on-desktop='false'],
[data-atk-display-on-mobile='false'],
[data-atk-hide-when-not-parent='true'] {
    @include device-display;
}

[data-atk-form-input-read-only='true'] {
    background: var(--p-color-bg-surface-disabled);
    border-color: var(--p-color-border-tertiary);
    opacity: 0.8;
}
