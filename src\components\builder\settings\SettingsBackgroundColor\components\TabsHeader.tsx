import { BaseSwitchTab } from '@/components/builder/base/BaseSwitchTab';
import { FC } from 'react';

interface TabsHeaderProps {
    type: 'color' | 'image';
    setType: (type: 'color' | 'image') => void;
}

export const TabsHeader: FC<TabsHeaderProps> = ({ type, setType }) => {
    return (
        <BaseSwitchTab
            options={[
                { id: 'color', contentTooltip: 'Color' },
                { id: 'image', contentTooltip: 'Image' },
            ]}
            valueData={type}
            onChange={(value) => setType(value as 'color' | 'image')}
            fullWidth
            noTooltip
        />
    );
};
