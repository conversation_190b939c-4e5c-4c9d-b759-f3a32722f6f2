import { ReactElement, ReactNode } from 'react';
import { InlineGrid, InlineStack } from '@shopify/polaris';
import { Pagination } from '@/components';
import { SkeletonCard } from './SkeletonCard';
import { CardBaseProps } from './TeamplateCard';
import { EmptyState } from '../EmptyState';

interface TemplateListProps<T = object> {
    data?: CardBaseProps<T>[];
    isLoading?: boolean;
    emptyTitle?: string;
    emptyDescription?: string;
    emptyRender?: ReactNode;
    renderItem: (item: CardBaseProps<T>, index: number) => ReactElement;
}

export const TemplateList = <T extends object>({
    data = [],
    isLoading = false,
    renderItem,
    emptyTitle,
    emptyDescription,
    emptyRender,
}: TemplateListProps<T>) => {
    const renderGrid = (children: React.ReactNode) => (
        <div
            css={{
                '.Polaris-InlineGrid': {
                    rowGap: '24px',
                    columnGap: '12px',
                },
            }}
        >
            <InlineGrid columns={{ sm: 2, lg: 3 }} gap="400">
                {children}
            </InlineGrid>
        </div>
    );

    const skeleton = (
        <>
            {renderGrid(Array.from({ length: 9 }).map((_, index) => <SkeletonCard key={index} />))}
            <InlineStack align="center">
                <Pagination total={3} size="large" disabled />
            </InlineStack>
        </>
    );

    const list = renderGrid(data.map((item, i) => renderItem(item, i)));

    if (isLoading) return skeleton;
    if (data.length === 0) {
        return emptyRender ? emptyRender : <EmptyState title={emptyTitle} description={emptyDescription} />;
    }
    return list;
};

export { TemplateCard } from './TeamplateCard';
