import { FC } from 'react';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import { buttonAnimationLoopOptions, buttonAnimationTypeOptions } from '@/components/builder/data/options';
import { SettingsSliderInput } from '@/components/builder/settings/SettingsSliderInput';
interface AnimationProps {
    blockId: string;
    isUpdateConfigs: boolean;
    path: string;
    label: string;
}

export const Animation: FC<AnimationProps> = ({ blockId, isUpdateConfigs, path, label }) => {
    return (
        <BaseCollapse
            label={label}
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BlockStack gap="300">
                    <SettingsSelect
                        options={buttonAnimationTypeOptions}
                        path={`${path}.type`}
                        blockId={blockId}
                        isUpdateConfigs={isUpdateConfigs}
                        label="Type"
                    />

                    <SettingsSelect
                        options={buttonAnimationLoopOptions}
                        path={`${path}.loop`}
                        blockId={blockId}
                        isUpdateConfigs={isUpdateConfigs}
                        label="Loop"
                    />

                    <SettingsSliderInput
                        path={`${path}.duration`}
                        blockId={blockId}
                        isUpdateConfigs={isUpdateConfigs}
                        isSeconds
                        title="Duration"
                        direction="column"
                        inputProps={{
                            suffix: 's',
                        }}
                    />

                    <SettingsSliderInput
                        path={`${path}.delay`}
                        blockId={blockId}
                        isUpdateConfigs={isUpdateConfigs}
                        isSeconds
                        title="Delay"
                        direction="column"
                        inputProps={{
                            suffix: 's',
                        }}
                    />
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
