import { FC } from 'react';
import {
    BlockStack,
    Box,
    Card,
    ChoiceList,
    Divider,
    Filters,
    InlineStack,
    Page,
    Text,
    AppliedFilterInterface,
} from '@shopify/polaris';
import { css } from '@emotion/react';
import { useNavigate } from 'react-router-dom';
import { useTemplates } from '@/hooks';
import { useAppStore } from '@/stores';
import { Pagination, SortButton } from '@/components';
import { TemplateCard, TemplateList } from '@/pages/TemplatesPage/TemplateList';

export const CreatePage: FC = () => {
    const navigate = useNavigate();
    const setTemplatePreview = useAppStore((state) => state.setTemplatePreview);
    const {
        isLoading,
        page,
        limit,
        pageType,
        industry,
        queryValue,
        sortSelected,
        industryChoices,
        pageTypeChoices,
        sortChoices,
        templates,
        totalTemplates,
        appliedPageTypeLabel,
        appliedIndustryLabel,
        setPageType,
        setIndustry,
        setQueryValue,
        setSortSelected,
        setPage,
    } = useTemplates();

    const filters = [
        {
            key: 'page_type',
            label: 'Page Type',
            filter: (
                <ChoiceList
                    title=""
                    titleHidden
                    choices={pageTypeChoices}
                    allowMultiple
                    selected={pageType}
                    onChange={(selected) => {
                        setPageType(selected);
                        setPage(1);
                    }}
                />
            ),
            pinned: true,
        },
        {
            key: 'industry_category',
            label: 'Industry category',
            filter: (
                <ChoiceList
                    title=""
                    titleHidden
                    choices={industryChoices}
                    allowMultiple
                    selected={industry}
                    onChange={(selected) => {
                        setIndustry(selected);
                        setPage(1);
                    }}
                />
            ),
            pinned: true,
        },
    ];
    const appliedFilters: AppliedFilterInterface[] = [];

    if (pageType && pageType.length > 0) {
        const key = 'page_type';
        appliedFilters.push({
            key,
            label: appliedPageTypeLabel,
            onRemove: () => setPageType([]),
        });
    }
    if (industry && industry.length > 0) {
        const key = 'industry_category';
        appliedFilters.push({
            key,
            label: appliedIndustryLabel,
            onRemove: () => setIndustry([]),
        });
    }

    return (
        <Page title="Create from template" backAction={{ content: 'Back', onAction: () => navigate(-1) }}>
            <div css={{ paddingBottom: '60px' }}>
                <Card padding="0">
                    <Box paddingInline="400" paddingBlockStart="400" paddingBlockEnd="0">
                        <BlockStack gap="300">
                            <Text as="p" variant="headingMd">
                                Templates
                            </Text>
                            <div
                                css={css`
                                    margin-inline: -8px;
                                    .Polaris-Filters__Container {
                                        border-bottom: none;
                                        .Polaris-InlineStack {
                                            gap: 8px;
                                        }
                                        .Polaris-TextField__Backdrop {
                                            border: none;
                                            box-shadow: 0px 1px 0px 0px #e3e3e3 inset, 1px 0px 0px 0px #e3e3e3 inset,
                                                -1px 0px 0px 0px #e3e3e3 inset, 0px -1px 0px 0px #b5b5b5 inset;
                                        }
                                    }
                                    .Polaris-Filters__FiltersWrapper {
                                        height: auto;
                                        overflow: visible;
                                        border-bottom: none;
                                        .Polaris-Filters-FilterPill__ToggleButton {
                                            height: auto;
                                            min-height: 24px;
                                            overflow: visible;
                                            .Polaris-Text--root {
                                                text-align: start;
                                                text-wrap: auto;
                                            }
                                        }
                                    }
                                    .Polaris-TextField__Input {
                                        font-size: var(--p-font-size-325);
                                        line-height: var(--p-font-line-height-500);
                                    }
                                `}
                            >
                                <Filters
                                    queryPlaceholder="Search templates  "
                                    filters={filters}
                                    appliedFilters={appliedFilters}
                                    queryValue={queryValue}
                                    onQueryChange={setQueryValue}
                                    onQueryClear={() => setQueryValue('')}
                                    onClearAll={() => {
                                        setIndustry([]);
                                        setPageType([]);
                                        setPage(1);
                                    }}
                                >
                                    <SortButton
                                        choices={sortChoices}
                                        selected={sortSelected}
                                        onChange={setSortSelected}
                                    />
                                </Filters>
                            </div>
                        </BlockStack>
                    </Box>
                    <Divider />
                    <Box paddingInline="400" paddingBlockStart="800" paddingBlockEnd="1000">
                        <BlockStack gap="800">
                            <TemplateList
                                data={templates}
                                isLoading={isLoading}
                                emptyTitle="No template found"
                                emptyDescription="Lorem ipsum dolor sit amet consectetur scelerisque."
                                renderItem={(data, index) => (
                                    <TemplateCard
                                        key={index}
                                        data={data}
                                        onPreview={() => {
                                            setTemplatePreview({
                                                name: data.title,
                                                url: 'https://seal-commerce-asia.myshopify.com/pages/socks-monthly-subscription',
                                            });
                                            shopify.modal.show('modal-preview-template');
                                        }}
                                    />
                                )}
                            />
                            {!isLoading && templates && templates.length > 0 && (
                                <InlineStack align="center">
                                    <Pagination
                                        value={page}
                                        onChange={setPage}
                                        total={Math.ceil(totalTemplates / limit)}
                                        size="large"
                                    />
                                </InlineStack>
                            )}
                        </BlockStack>
                    </Box>
                </Card>
            </div>
        </Page>
    );
};
