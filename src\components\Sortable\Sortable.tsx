import { useState } from 'react';
import { createPortal } from 'react-dom';
import {
    DndContext,
    DragEndEvent,
    DragOverlay,
    DragStartEvent,
    PointerSensor,
    UniqueIdentifier,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext } from '@dnd-kit/sortable';
import { SortableItem } from './SortableItem';
import { SortableItemRequireProps, SortableProps } from './types';

export function Sortable<T extends SortableItemRequireProps>({
    items = [],
    onChange,
    itemRenderer: ItemRenderer,
    extendNode,
    containerProps,
    useDragOverlay = true,
    handle,
    adjustScale,
    animateLayoutChanges,
    collisionDetection,
    dropAnimation,
    measuring,
    modifiers,
    activationConstraint = {
        distance: 0,
    },
    reorderItems = arrayMove,
    itemProps,
}: SortableProps<T>) {
    const [selected, setSelected] = useState<T>();
    const getIndex = (id: UniqueIdentifier) => items.findIndex((item) => item.id === id);
    const onRemove = (id: UniqueIdentifier) => onChange(items.filter((item) => item.id !== id));

    const onDragStart = ({ active }: DragStartEvent) => {
        if (!active) return;
        const itemSelected = items.find((item) => item.id === active.id);
        setSelected(itemSelected);
    };

    const onDragEnd = ({ active, over }: DragEndEvent) => {
        setSelected(undefined);
        if (over) {
            const oldIndex = getIndex(active.id);
            const newIndex = getIndex(over.id);
            if (active.id !== over.id) {
                onChange(reorderItems(items, oldIndex, newIndex));
            }
        }
    };

    const onDragCancel = () => setSelected(undefined);

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint,
        }),
    );

    return (
        <DndContext
            sensors={sensors}
            onDragStart={onDragStart}
            onDragEnd={onDragEnd}
            onDragCancel={onDragCancel}
            collisionDetection={collisionDetection}
            measuring={measuring}
            modifiers={modifiers}
        >
            <SortableContext items={items}>
                <div {...containerProps}>
                    {extendNode}
                    {items.map((item, index) => (
                        <SortableItem
                            index={index}
                            key={item.id}
                            data={item}
                            handle={handle}
                            dragOverlay={useDragOverlay}
                            animateLayoutChanges={animateLayoutChanges}
                            onRemove={() => onRemove(item.id)}
                            itemRenderer={ItemRenderer}
                            elementProps={itemProps}
                        />
                    ))}
                </div>
            </SortableContext>
            {useDragOverlay &&
                createPortal(
                    <DragOverlay adjustScale={adjustScale} dropAnimation={dropAnimation} style={{ zIndex: 999999 }}>
                        {selected && (
                            <SortableItem
                                index={getIndex(selected.id)}
                                data={selected}
                                clone
                                handle={handle}
                                dragOverlay={useDragOverlay}
                                itemRenderer={ItemRenderer}
                            />
                        )}
                    </DragOverlay>,
                    document.body,
                )}
        </DndContext>
    );
}
