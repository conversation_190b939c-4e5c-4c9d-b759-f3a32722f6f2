import { useState, useCallback, useMemo } from 'react';

export type FormField<T> = {
    value: T;
    handleChange: (newValue: T) => void;
    reset: () => void;
    setInitialValue: (newInitialValue: T) => void;
    hasChanges: boolean;
};

export const useFormField = <T>(initialValue: T): FormField<T> => {
    const [value, setValue] = useState<T>(initialValue);
    const [initialValueState, setInitialValueState] = useState<T>(initialValue);

    const isEqual = useCallback((a: T, b: T): boolean => {
        if (Array.isArray(a) && Array.isArray(b)) {
            return JSON.stringify(a) === JSON.stringify(b);
        }
        return a === b;
    }, []);

    const hasChanges = useMemo(() => !isEqual(value, initialValueState), [value, initialValueState, isEqual]);

    const handleChange = useCallback((newValue: T) => {
        setValue(newValue);
    }, []);

    const reset = useCallback(() => {
        setValue(initialValueState);
    }, [initialValueState]);

    const setInitialValue = useCallback((newInitialValue: T) => {
        setInitialValueState(newInitialValue);
        setValue(newInitialValue);
    }, []);

    return {
        value,
        handleChange,
        reset,
        hasChanges,
        setInitialValue,
    };
};
