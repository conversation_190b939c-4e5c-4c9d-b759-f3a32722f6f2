import { FC } from 'react';
import { alignHorizontalOptions2 } from '@/components/builder/data/options';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';

interface SettingsAlignProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const SettingsAlign: FC<SettingsAlignProps> = ({ blockId, path, isUpdateConfigs }) => {
    return (
        <SettingsSwitchTab
            label="Align"
            options={alignHorizontalOptions2}
            blockId={blockId}
            path={path}
            isUpdateConfigs={isUpdateConfigs}
        />
    );
};
