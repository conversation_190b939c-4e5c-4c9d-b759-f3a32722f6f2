

.account-settings{
   display : none;
   &--success{
    display : block;
   }
   .bill-table {
    .Polaris-IndexTable__Table--unselectable.Polaris-IndexTable__Table--sticky .Polaris-IndexTable__TableCell:first-of-type {
        position: relative ;
    }
    .Polaris-IndexTable__TableCell:not(.Polaris-IndexTable__TableCell--flush) {
      &:first-of-type {
          padding-left: var(--p-space-400);
      }
    }
    .Polaris-IndexTable__TableHeading:first-of-type:not(.Polaris-IndexTable__TableHeading--flush) {
        padding-left: var(--p-space-400);
    }

    &__pagination {
      display: flex;
      align-items: center;
      justify-content: end;
      padding: 16px;
      border-top: 0.0625rem solid rgba(227, 227, 227, 1);
      .Polaris-Button--disabled {
          background-color: rgba(0, 0, 0, 0.04);
      }
    }
}

.Polaris-Select__Input{
    cursor: pointer;
}
}
