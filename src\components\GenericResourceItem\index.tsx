import { useState } from 'react';
import {
    ActionList,
    ActionListItemDescriptor,
    Badge,
    BadgeProps,
    BlockStack,
    Box,
    Button,
    ButtonGroup,
    InlineStack,
    Popover,
    ResourceItem,
    ResourceItemProps,
    Text,
    TextField,
} from '@shopify/polaris';
import { MenuHorizontalIcon, ChartVerticalFilledIcon } from '@shopify/polaris-icons';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import clsx from 'clsx';
import { useUncontrolled } from '@/hooks';
import './styles.scss';

export interface ActionConfig<T> {
    content: string;
    icon?: ActionListItemDescriptor['icon'];
    onAction?: (data: T) => void;
    disabled?: boolean | ((item: T) => boolean);
    destructive?: boolean;
    popoverAction?: boolean;
}

export interface ModalConfig {
    title: string;
    cancelText: string;
    okText: string;
    showInput?: boolean;
    message?: React.ReactNode | ((title: string) => React.ReactNode);
    disableOkWhen?: (inputText: string, originalTitle: string) => boolean;
}

export interface GenericItemRenderers<T> {
    id: (data: T) => string | number;
    title: (data: T) => string;
    media?: (data: T) => ResourceItemProps['media'];
    badge?: (data: T) => { content: string; tone: BadgeProps['tone'] } | null;
    subtitle?: (data: T) => string | null;
    metadata?: (data: T) => React.ReactNode;
}

export interface GenericResourceItemProps<T> {
    data: T;
    typeName: string;
    selectMode?: boolean;
    renderers: GenericItemRenderers<T>;
    actions: ActionConfig<T>[];
    modalConfigs: Record<string, ModalConfig>;
    actionHandlers: {
        onRename?: (args: { data: T; newTitle: string; [key: string]: unknown }) => void;
        onDuplicate?: (args: { data: T; newTitle: string; [key: string]: unknown }) => void;
        onDelete?: (args: { data: T; [key: string]: unknown }) => void;
        onExport?: (args: { data: T; [key: string]: unknown }) => void;
    };
    loading?: boolean;
    openModal?: boolean;
    popoverActive?: boolean;
    onClick?: (data: T) => void;
    onPreview?: (data: T) => void;
    onAnalyze?: (data: T) => void;
    onChangeOpenModal?: (openModal: boolean) => void;
    onChangePopoverActive?: (popoverActive: boolean) => void;
}

export function GenericResourceItem<T>({
    data,
    typeName,
    selectMode,
    renderers,
    actions,
    modalConfigs,
    actionHandlers,
    loading,
    openModal: openModalProp,
    popoverActive: popoverActiveProp,
    onClick,
    onPreview,
    onChangeOpenModal,
    onChangePopoverActive,
    onAnalyze,
}: GenericResourceItemProps<T>) {
    const [openModal, setOpenModal] = useUncontrolled({
        value: openModalProp,
        onChange: onChangeOpenModal,
    });
    const [popoverActive, setPopoverActive] = useUncontrolled({
        value: popoverActiveProp,
        onChange: onChangePopoverActive,
    });
    const [modalState, setModalState] = useState({
        kind: 'rename',
        inputText: '',
    });

    const id = renderers.id(data);
    const title = renderers.title(data);
    const media = renderers.media ? renderers.media(data) : undefined;
    const badge = renderers.badge ? renderers.badge(data) : null;
    const subtitle = renderers.subtitle ? renderers.subtitle(data) : null;
    const metadata = renderers.metadata ? renderers.metadata(data) : null;

    const handleActionPopover = (actionKind: string) => {
        setModalState({
            kind: actionKind,
            inputText: actionKind === 'duplicate' ? `Copy of ${title}` : title,
        });
        setOpenModal(true);
    };

    const handleModalAction = () => {
        const { kind, inputText } = modalState;
        const args = { openModal, popoverActive, setOpenModal, setPopoverActive };

        if (kind === 'rename' && actionHandlers.onRename) {
            actionHandlers.onRename({ data, newTitle: inputText, ...args });
        } else if (kind === 'duplicate' && actionHandlers.onDuplicate) {
            actionHandlers.onDuplicate({ data, newTitle: inputText, ...args });
        } else if (kind === 'delete' && actionHandlers.onDelete) {
            actionHandlers.onDelete({ data, ...args });
        } else if (kind === 'export' && actionHandlers.onExport) {
            actionHandlers.onExport({ data, ...args });
        }
    };

    const isDisabled = (action: ActionConfig<T>): boolean => {
        if (typeof action.disabled === 'function') {
            return action.disabled(data);
        }
        return !!action.disabled;
    };

    const actionItems = actions.map((action) => ({
        content: action.content,
        icon: action.icon,
        destructive: action.destructive,
        disabled: isDisabled(action),
        onAction: () => {
            if (action.popoverAction) {
                handleActionPopover(action.content.toLowerCase());
            } else if (action.onAction) {
                action.onAction(data);
            }
        },
    }));

    const currentModal = modalConfigs[modalState.kind.toLowerCase()] || {
        title: 'Action',
        cancelText: 'Cancel',
        okText: 'Confirm',
    };

    const isOkDisabled = currentModal.disableOkWhen ? currentModal.disableOkWhen(modalState.inputText, title) : false;

    const modalMessage =
        typeof currentModal.message === 'function' ? currentModal.message(title) : currentModal.message;

    return (
        <div
            className="rsItem-wrapper"
            onClickCapture={(e) => {
                const target = e.target as HTMLElement;
                const stopPropagation = selectMode && !(target.tagName === 'INPUT') && !target.closest('button');
                if (stopPropagation) {
                    e.stopPropagation();
                }
            }}
        >
            <ResourceItem id={id.toString()} media={media} onClick={() => onClick?.(data)}>
                <div
                    css={{
                        maxWidth: 'calc(100% - 200px)',
                        '@media screen and (max-width: 767px)': { maxWidth: '100%' },
                    }}
                >
                    <BlockStack gap="150">
                        <InlineStack gap="300">
                            <Text as="h3" variant="headingMd" fontWeight="bold" breakWord>
                                {title}
                            </Text>
                            {badge && <Badge tone={badge.tone}>{badge.content}</Badge>}
                        </InlineStack>
                        {subtitle && (
                            <Text as="p" variant="bodyMd">
                                {subtitle}
                            </Text>
                        )}
                        {metadata}
                    </BlockStack>
                </div>
            </ResourceItem>
            <div className={clsx('more-action-wrapper', popoverActive && 'show')}>
                <ButtonGroup>
                    <Button onClick={() => onPreview?.(data)}>Preview</Button>
                    {onAnalyze && <Button icon={ChartVerticalFilledIcon} onClick={() => onAnalyze?.(data)} />}
                    <Popover
                        active={popoverActive}
                        activator={
                            <Button
                                icon={MenuHorizontalIcon}
                                onClick={() => {
                                    setPopoverActive(!popoverActive);
                                    document.body.click();
                                }}
                            />
                        }
                        onClose={() => setPopoverActive(false)}
                        preferredAlignment="right"
                        captureOverscroll={false}
                    >
                        <div className="pane-wrapper">
                            <Popover.Pane>
                                <ActionList actionRole="menuitem" items={actionItems} />
                            </Popover.Pane>
                        </div>
                    </Popover>
                </ButtonGroup>
            </div>
            <Modal open={openModal} onHide={() => setOpenModal(false)}>
                <Box padding="400" paddingBlockEnd="800">
                    <BlockStack gap="500">
                        {modalMessage}
                        {currentModal.showInput && (
                            <div className="text-field-wrapper">
                                <TextField
                                    label={`${typeName} title`}
                                    value={modalState.inputText}
                                    onChange={(inputText) => setModalState((prev) => ({ ...prev, inputText }))}
                                    autoComplete="off"
                                    autoFocus
                                    clearButton
                                    maxLength={255}
                                    showCharacterCount
                                    onClearButtonClick={() => setModalState((prev) => ({ ...prev, inputText: '' }))}
                                />
                            </div>
                        )}
                    </BlockStack>
                </Box>
                <TitleBar title={currentModal.title}>
                    <button onClick={() => setOpenModal(false)}>{currentModal.cancelText}</button>
                    <button
                        variant="primary"
                        tone={modalState.kind === 'delete' ? 'critical' : 'default'}
                        disabled={isOkDisabled}
                        onClick={handleModalAction}
                        loading={loading ? '' : undefined}
                    >
                        {currentModal.okText}
                    </button>
                </TitleBar>
            </Modal>
        </div>
    );
}
