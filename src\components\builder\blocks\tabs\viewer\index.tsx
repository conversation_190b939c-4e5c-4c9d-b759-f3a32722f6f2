import { FC, useEffect } from 'react';
import clsx from 'clsx';
import {
    Auto_BlockViewer,
    BlockViewer,
    DATA_SET_AUTO_ID_INNER,
    DATA_SET_VIEWER,
    useBlockStore,
    useBuilderStore,
} from '@giaminhautoketing/auto-builder';
import { getNestedProperty } from '@/utils/shared';
import {
    DATA_SET_ATK_ANIMATION,
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
} from '@/components/builder/constants/constants';
import { useAppStore } from '@/stores/appStore/useAppStore';
import { DATA_SET_TABS_CONTENT, DATA_SET_TABS_NAV_LIST, DATA_SET_TABS_NAV_ITEM } from '../constants';
import { ConfigsProps } from '../types';
import './styles.scss';

export const TabsViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const { items, animation } = configs as unknown as ConfigsProps;
    const device = useBuilderStore((state) => state.currentDevice);
    const direction = getNestedProperty(bpConfigs, `${device}.basic.tabContainer.direction`);
    const normalIcon = getNestedProperty(bpConfigs, `${device}.basic.tabNormal.icon.source`);
    const selectedIcon = getNestedProperty(bpConfigs, `${device}.basic.tabSelected.icon.source`);
    const tempTab = useAppStore((state) => state.tempTab);
    const setTempTab = useAppStore((state) => state.setTempTab);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);

    const activeId = tempTab[autoId]?.activeId;
    const defaultId = configs.defaultId as string;

    useEffect(() => {
        if (defaultId && items.map((item) => item.id).includes(defaultId) && !activeId) {
            setTempTab(autoId, defaultId);
        } else if (!defaultId && !activeId) {
            setTempTab(autoId, items[0].id);
            updateBlockConfigsProperty(autoId, 'defaultId', items[0].id);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            bpConfigs={bpConfigs}
            configs={configs}
        >
            <div
                className="tabs-wrapper animate__animated"
                style={
                    {
                        '--atk-tabs-direction': direction,
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: direction === 'column' ? 'row' : 'column',
                    } as React.CSSProperties
                }
                {...{
                    ...(device === 'desktop'
                        ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                        : { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile }),
                    [DATA_SET_AUTO_ID_INNER]: autoId,
                    [DATA_SET_ATK_ANIMATION]: animation.type && animation.type !== 'none',
                }}
            >
                <div className="tabs-nav-list" {...{ [DATA_SET_TABS_NAV_LIST]: autoId }}>
                    {items.map((item) => {
                        const isSelected = item.id === tempTab[autoId]?.activeId;
                        return (
                            <div
                                key={item.id}
                                className={clsx('tabs-nav-item', isSelected && 'selected')}
                                {...{ [DATA_SET_TABS_NAV_ITEM]: autoId }}
                            >
                                {normalIcon && (
                                    <span
                                        className="tabs-nav-item-icon"
                                        data-role="normal-icon"
                                        dangerouslySetInnerHTML={{ __html: normalIcon }}
                                    />
                                )}
                                {selectedIcon && (
                                    <span
                                        className="tabs-nav-item-icon"
                                        data-role="selected-icon"
                                        dangerouslySetInnerHTML={{ __html: selectedIcon }}
                                    />
                                )}
                                <span className="tabs-nav-item-label">{item.name}</span>
                            </div>
                        );
                    })}
                </div>
                <div className="tabs-content" {...{ [DATA_SET_TABS_CONTENT]: autoId }}>
                    {items.map((item) => (
                        <div key={item.id} className="tabs-panel"></div>
                    ))}
                </div>
            </div>
        </BlockViewer>
    );
};
