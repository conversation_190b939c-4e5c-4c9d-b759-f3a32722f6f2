import { Auto_BlockViewer, BlockViewer, DATA_SET_VIEWER, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { FC } from 'react';
import { BreadcrumbViewerContentProps, BreadcrumbConfigs } from '../types';
import { DEFAULT_BREADCRUMB_ITEMS, DEFAULT_BREADCRUMB_CONFIG } from '../constants';
import { BreadcrumbList } from '../components/BreadcrumbItem';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
    DATA_SET_ATK_HIDE_WHEN_NOT_PARENT,
} from '@/components/builder/constants/constants';
import { useBreadcrumbAttributes } from '../hooks/useBreadcrumbAttributes';

const BreadcrumbViewerContent: FC<BreadcrumbViewerContentProps> = ({ configs, autoId, bpConfigs }) => {
    const {
        items = DEFAULT_BREADCRUMB_ITEMS,
        showHome,
        showSeparator,
        showLastItem,
        isCollapse = false,
        separatorIcon,
        beforeEllipsis,
        afterEllipsis,
    } = { ...DEFAULT_BREADCRUMB_CONFIG, ...configs?.content };

    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const attributes = useBreadcrumbAttributes(configs, autoId, currentDevice, bpConfigs);

    return (
        <div
            {...(currentDevice === 'desktop' ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop } : {})}
            {...(currentDevice === 'mobile' ? { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile } : {})}
            {...(configs.content?.hideWhenNotParent
                ? { [DATA_SET_ATK_HIDE_WHEN_NOT_PARENT]: configs.content?.hideWhenNotParent }
                : {})}
        >
            <nav aria-label="Breadcrumb" {...attributes}>
                <BreadcrumbList
                    items={items}
                    showHome={showHome}
                    showSeparator={showSeparator}
                    showLastItem={showLastItem}
                    isCollapse={isCollapse}
                    separatorIcon={separatorIcon}
                    autoId={autoId}
                    beforeEllipsis={beforeEllipsis?.val}
                    afterEllipsis={afterEllipsis?.val}
                />
            </nav>
        </div>
    );
};

interface BreadcrumbViewerProps extends Auto_BlockViewer {
    configs: BreadcrumbConfigs;
}

export const BreadcrumbViewer: FC<BreadcrumbViewerProps> = ({ autoId, cname, label, bpConfigs, type, configs }) => (
    <BlockViewer
        autoId={autoId}
        cname={cname}
        label={label}
        type={type}
        bpConfigs={bpConfigs}
        configs={configs}
        attrs={{ [DATA_SET_VIEWER]: 'true' }}
    >
        <BreadcrumbViewerContent configs={configs} autoId={autoId} isUpdateConfigs bpConfigs={bpConfigs} />
    </BlockViewer>
);

export { BreadcrumbViewerContent };
