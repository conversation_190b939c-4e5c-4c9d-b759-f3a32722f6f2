export const updateCssVariable = (
    target: HTMLElement,
    cssVariable: string,
    value: string | number,
    unit?: string,
): void => {
    if (!target || !cssVariable) return;
    const finalValue = `${value}${unit ? unit : ''}`;
    target.style.setProperty(cssVariable, finalValue);
};

export const removeCssVariable = (target: HTMLElement, cssVariable: string): void => {
    if (!target || !cssVariable) return;

    target.style.removeProperty(cssVariable);
};
