import { EditorContent } from '@tiptap/react';
import { useTextEditor } from '@/pages/BuilderPage/hooks';
import { BaseTextEditor } from '../../base';
import './styles.scss';

interface SettingsRTEProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
    isFreeText?: boolean;
}

export const SettingsRTE = ({ blockId, path, isUpdateConfigs, isFreeText }: SettingsRTEProps) => {
    const { editor } = useTextEditor({ blockId, path, isUpdateConfigs });

    return (
        <BaseTextEditor.Provider value={{ editor }}>
            <div className="rte">
                <div className="rte-controls--top">
                    <BaseTextEditor.Bold />
                    <BaseTextEditor.Italic />
                    <BaseTextEditor.Underline />
                    <BaseTextEditor.Color />
                    <BaseTextEditor.Highlight />
                    <BaseTextEditor.Strike />
                </div>
                <div className="rte-controls--bottom">
                    <BaseTextEditor.Superscript />
                    <BaseTextEditor.Subscript />
                    {!isFreeText ? (
                        <>
                            <BaseTextEditor.OrderList />
                            <BaseTextEditor.BulletList />
                        </>
                    ) : (
                        <BaseTextEditor.FontSize />
                    )}
                    <BaseTextEditor.ClearFormat />
                </div>
                <EditorContent editor={editor} spellCheck={false} className="rte-content" />
            </div>
        </BaseTextEditor.Provider>
    );
};
