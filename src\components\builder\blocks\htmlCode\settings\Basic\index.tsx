import { FC } from 'react';
// import { Button } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { SettingElementID } from '@/components/builder/settings/SettingElementID';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
// import { EditHtml } from './EditHtml';

// const DEFAULT_PATH_CONFIGS_CONTENT = 'content';

export const Basic: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId);

    return (
        <>
            <BaseHasBorderLayout>
                <SettingElementID value={selectedBlockId || ''} />
                {/* <EditHtml
                    elementTriggerProps={{ children: <Button>Edit HTML</Button> }}
                    blockId={selectedBlockId || ''}
                    path={DEFAULT_PATH_CONFIGS_CONTENT}
                    isUpdateConfigs
                /> */}
            </BaseHasBorderLayout>
        </>
    );
};
