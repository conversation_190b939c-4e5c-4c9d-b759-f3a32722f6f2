import { StateCreator } from 'zustand';
import { httpRequest } from '@/configs';
import { apiAddress } from '@/configs/apiAddress';
import { AppStore } from '@/stores/appStore/types';
import { PageState, PageActions, PageItem, RequestName, RequestStatus } from '@/stores/appStore/pages/types';

export const createPageSlice: StateCreator<AppStore, [], [], PageState & PageActions> = (set, get) => ({
    pageList: [],
    templateType: [],
    apiStatus: 'loading',
    total: 0,
    page: null,
    templatePreview: {
        name: '',
        url: '',
    },
    currentParams: {
        currentPage: 1,
        perPage: 10,
    },
    templateTypeId: '',
    pageCreationStatus: 'idle',
    hasEdits: false,
    requestsStatus: {},
    pageId: null,
    setPageId: (pageId: number) => set((state) => ({ ...state, pageId })),
    updateRequestStatus: (name: RequestName, status: RequestStatus, error?: Error) => {
        set((state) => {
            return {
                ...state,
                requestsStatus: {
                    ...state.requestsStatus,
                    [name]: { status, timestamp: Date.now(), error },
                },
            };
        });
    },

    setCurrentParams: (currentParams) =>
        set((state) => ({
            ...state,
            currentParams: currentParams,
        })),

    setTemplatePreview: (templatePreview) =>
        set((state) => ({
            ...state,
            templatePreview: templatePreview,
        })),

    setApiStatus: (apiStatus) =>
        set((state) => ({
            ...state,
            apiStatus,
        })),

    setTotal: (total) =>
        set((state) => ({
            ...state,
            total,
        })),

    setPageList: async (params?: Record<string, unknown>) => {
        get().updateRequestStatus('setPageList', 'loading');
        try {
            const response = await httpRequest.get(apiAddress.pagesList, { params });
            set((state) => ({
                ...state,
                pageList: response.data.result.data.list,
                total: response.data.result.data.total,
            }));
            get().updateRequestStatus('setPageList', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('setPageList', 'error');
            console.error('Failed to fetch page list:', error);
        }
    },

    updatePage: async (id: string, updateData: Partial<PageItem>, currentParams) => {
        get().updateRequestStatus('updatePage', 'loading');
        try {
            const response = await httpRequest.put(apiAddress.pageDetail.replace(':id', id), updateData);
            await set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('updatePage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('updatePage', 'error');
            console.error('Failed to update page:', error);
            throw error;
        }
    },

    deletePage: async (pageIds: string[], currentParams) => {
        try {
            get().updateRequestStatus('deletePage', 'loading');
            const response = await httpRequest.delete(apiAddress.deletePage, { data: { pageIds: pageIds } });
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('deletePage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('deletePage', 'error');
            console.error('Failed to delete page:', error);
            throw error;
        }
    },

    duplicatePage: async (id: string, duplicateData: Partial<PageItem>, currentParams) => {
        get().updateRequestStatus('duplicatePage', 'loading');
        try {
            const response = await httpRequest.post(apiAddress.duplicatePage.replace(':id', id), duplicateData);
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('duplicatePage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('duplicatePage', 'error');
            console.error('Failed to duplicate page:', error);
            throw error;
        }
    },

    setTemplateType: async () => {
        try {
            const response = await httpRequest.get(apiAddress.templateType);
            set((state) => ({
                ...state,
                templateType: response.data.result.data,
            }));
        } catch (error) {
            console.error('Failed to fetch template type:', error);
        }
    },

    unfavoriteTemplate: async (templateIds: string[], currentParams) => {
        get().updateRequestStatus('unfavoriteTemplate', 'loading');
        try {
            const response = await httpRequest.post(apiAddress.unfavoriteTemplate, {
                templateIds: templateIds,
            });
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('unfavoriteTemplate', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('unfavoriteTemplate', 'error');
            console.error('Failed to unfavorite template:', error);
            throw error;
        }
    },

    exportPage: async (pageIds: string[], currentParams) => {
        get().updateRequestStatus('exportPage', 'loading');
        try {
            const response = await httpRequest.post(apiAddress.exportPage, {
                pages: pageIds,
            });
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('exportPage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('exportPage', 'error');
            console.error('Failed to export page:', error);
            throw error;
        }
    },

    importPage: async (formData: FormData, currentParams) => {
        get().updateRequestStatus('importPage', 'loading');
        try {
            const response = await httpRequest.post(apiAddress.importPage, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('importPage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('importPage', 'error');
            console.error('Failed to import page:', error);
            throw error;
        }
    },

    updatePageStatus: async (id: number, status: string, currentParams) => {
        get().updateRequestStatus('updatePageStatus', 'loading');
        try {
            const response = await httpRequest.put(apiAddress.changeStatus.replace(':id', String(id)), { status });
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('updatePageStatus', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('updatePageStatus', 'error');
            console.error('Failed to update page status:', error);
            throw error;
        }
    },
    restorePage: async (id: number, currentParams) => {
        try {
            get().updateRequestStatus('restorePage', 'loading');
            const response = await httpRequest.post(apiAddress.restorePage.replace(':id', String(id)));
            set((state) => {
                state.setPageList(currentParams);
                return {
                    ...state,
                };
            });
            get().updateRequestStatus('restorePage', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('restorePage', 'error');
            console.error('Failed to update page status:', error);
            throw error;
        }
    },
    getDetailPage: async (id: string) => {
        get().updateRequestStatus('getDetailPage', 'loading');
        try {
            const response = await httpRequest.get(apiAddress.pageDetail.replace(':id', id));
            set((state) => ({
                ...state,
                page: response.data.result.data,
            }));
            get().updateRequestStatus('getDetailPage', 'success');
            get().setPageId(Number(response.data.result.data.id));
            return response.data;
        } catch (error) {
            get().updateRequestStatus('getDetailPage', 'error');
            console.error('Failed to get detail page:', error);
            throw error;
        }
    },
    updatePageJson: async (id: string, updateData: string) => {
        try {
            get().updateRequestStatus('updatePageJson', 'loading');
            const response = await httpRequest.put(apiAddress.updatePage.replace(':id', id), updateData);
            get().updateRequestStatus('updatePageJson', 'success');
            return response.data;
        } catch (error) {
            get().updateRequestStatus('updatePageJson', 'error');
            console.error('Failed to update page json:', error);
            throw error;
        }
    },
    previewPage: async (id: number) => {
        try {
            const response = await httpRequest.get(apiAddress.previewPage.replace(':id', id.toString()));
            return response.data;
        } catch (error) {
            console.error('Failed to preview page:', error);
            throw error;
        }
    },

    setPageCreationStatus: (status: 'idle' | 'starting' | 'pending' | 'complete') =>
        set((state) => ({
            ...state,
            pageCreationStatus: status,
        })),
    setHasEdits: (hasEdits: boolean) =>
        set((state) => ({
            ...state,
            hasEdits: hasEdits,
        })),

    createPage: async (createData: string) => {
        get().updateRequestStatus('createPage', 'loading');
        try {
            const response = await httpRequest.post(apiAddress.pagesList, createData);
            get().updateRequestStatus('createPage', 'success');
            return response.data.result.data;
        } catch (error) {
            get().updateRequestStatus('createPage', 'error');
            console.error('Failed to create page:', error);
            throw error;
        }
    },
    setTemplateTypeId: (templateTypeId: number | string) => {
        set((state) => ({
            ...state,
            templateTypeId: templateTypeId,
        }));
    },
});
