import { useEffect, RefObject } from 'react';

export const useClickOutside = <T extends HTMLElement = HTMLElement>(
    ref: RefObject<T | null>,
    handler: (event: MouseEvent | TouchEvent) => void,
    exceptRefs: RefObject<HTMLElement | null>[] = [],
): void => {
    useEffect(() => {
        const listener = (event: MouseEvent | TouchEvent) => {
            const target = event.target as Node;

            if (!ref.current || ref.current.contains(target)) {
                return;
            }

            for (const exceptRef of exceptRefs) {
                if (exceptRef.current && exceptRef.current.contains(target)) {
                    return;
                }
            }

            handler(event);
        };

        document.addEventListener('mousedown', listener);
        document.addEventListener('touchstart', listener);

        return () => {
            document.removeEventListener('mousedown', listener);
            document.removeEventListener('touchstart', listener);
        };
    }, [ref, handler, exceptRefs]);
};
