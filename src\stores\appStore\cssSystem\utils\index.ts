/* eslint-disable @typescript-eslint/no-explicit-any */
import { CSSProperties } from 'react';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { Background, Border, BorderRadius, SettingsShadow, Spacing, Typography, UnitValue } from './types';
import { Breakpoint, getResponsiveValue } from '../core';
import { generateTextShadow } from '@/utils';
import { SettingsShadowData } from '@/components/builder/settings/SettingsShadow/types';

export const generateBaseCss = (blockData: Auto_BlockData, breakpoint: Breakpoint) => {
    const width = getResponsiveValue<UnitValue>(blockData, 'width', breakpoint);
    const height = getResponsiveValue<UnitValue>(blockData, 'height', breakpoint);
    const gridColStart = getResponsiveValue<number>(blockData, 'gridColStart', breakpoint);
    const gridColEnd = getResponsiveValue<number>(blockData, 'gridColEnd', breakpoint);
    const gridRowStart = getResponsiveValue<number>(blockData, 'gridRowStart', breakpoint);
    const gridRowEnd = getResponsiveValue<number>(blockData, 'gridRowEnd', breakpoint);
    const mt = getResponsiveValue<UnitValue>(blockData, 'mt', breakpoint);
    const ml = getResponsiveValue<UnitValue>(blockData, 'ml', breakpoint);
    const justifySelf = getResponsiveValue<string>(blockData, 'justifySelf', breakpoint);
    const alignSelf = getResponsiveValue<string>(blockData, 'alignSelf', breakpoint);

    return {
        width: generateUnitValue(width),
        height: generateUnitValue(height),
        gridColumnStart: gridColStart,
        gridColumnEnd: gridColEnd,
        gridRowStart: gridRowStart,
        gridRowEnd: gridRowEnd,
        marginTop: generateUnitValue(mt),
        marginLeft: generateUnitValue(ml),
        justifySelf: justifySelf,
        alignSelf: alignSelf !== 'none' ? alignSelf : undefined,
    };
};

export const generateUnitValue = (value: UnitValue) => {
    if (!value) return undefined;
    return `${value.val}${value.unit || 'px'}`;
};

export const generateBackground = (background: Background): CSSProperties => {
    if (!background) return {};
    if (background.type === 'color') {
        return { backgroundColor: background.color };
    }
    if (background.type === 'image' && background.image) {
        const { url, repeat, position, attachment, fill } = background.image;
        return {
            backgroundImage: `url(${url})`,
            backgroundRepeat: repeat === 'no' ? 'no-repeat' : 'repeat',
            backgroundPosition: position || 'center',
            backgroundAttachment: attachment || 'scroll',
            backgroundSize: fill || 'auto',
        };
    }
    return {};
};

export const generateBorderRadius = (borderRadius: BorderRadius): CSSProperties => {
    if (!borderRadius) return {};
    const {
        'top-left': topLeft,
        'top-right': topRight,
        'bottom-right': bottomRight,
        'bottom-left': bottomLeft,
    } = borderRadius;
    return {
        borderTopLeftRadius: `${topLeft.val}${topLeft.unit || 'px'}`,
        borderTopRightRadius: `${topRight.val}${topRight.unit || 'px'}`,
        borderBottomRightRadius: `${bottomRight.val}${bottomRight.unit || 'px'}`,
        borderBottomLeftRadius: `${bottomLeft.val}${bottomLeft.unit || 'px'}`,
    };
};

export const generateBorder = (border: Border): CSSProperties => {
    if (!border) return {};
    return {
        borderWidth: `${border.top.val}${border.top.unit || 'px'} ${border.right.val}${border.right.unit || 'px'} ${
            border.bottom.val
        }${border.bottom.unit || 'px'} ${border.left.val}${border.left.unit || 'px'}`,
        borderColor: border.color || 'transparent',
        borderStyle: border.type === 'default' ? 'solid' : border.type || 'none',
        ...generateBorderRadius(border.radius),
    };
};

export const generateTypography = (
    typography: Typography,
    cssVarPrefixLineHeight?: string,
    cssVarPrefixLetterSpacing?: string,
): CSSProperties => {
    if (!typography) return {};

    const DEFAULTS = {
        FONT_WEIGHT: 'normal',
        FONT_FAMILY: 'inherit',
        COLOR: 'inherit',
        ALIGN: 'center',
    };
    const ALIGN_TO_FLEX = {
        left: 'flex-start',
        justify: 'flex-start',
        right: 'flex-end',
        center: 'center',
    } as const;

    const result: CSSProperties = {
        fontSize: `${typography.fontSize.val}${typography.fontSize.unit || 'px'}`,
        fontWeight: typography.fontWeight || DEFAULTS.FONT_WEIGHT,
        fontFamily: typography.fontFamily || DEFAULTS.FONT_FAMILY,
        color: typography.color || DEFAULTS.COLOR,
    };

    // Text properties
    if (typography.textTransform && typography.textTransform !== 'default') {
        result.textTransform = typography.textTransform as CSSProperties['textTransform'];
    }

    if (typography.textDecoration && typography.textDecoration !== 'default') {
        result.textDecoration = typography.textDecoration;
    }

    if (typography.textAlign) {
        (result as Record<string, string>)['--button-content-align'] =
            ALIGN_TO_FLEX[typography.textAlign as keyof typeof ALIGN_TO_FLEX] || DEFAULTS.ALIGN;
        result.textAlign = typography.textAlign as CSSProperties['textAlign'];
    }

    if (typography.fontStyle && typography.fontStyle !== 'default') {
        result.fontStyle = typography.fontStyle;
    }

    if (typography.textDirection) {
        result.direction = typography.textDirection as CSSProperties['direction'];
    }

    // Handle letter spacing
    if (typography.letterSpacing) {
        if (typeof typography.letterSpacing === 'object' && 'type' in typography.letterSpacing) {
            const { type, value } = typography.letterSpacing;
            if (type === 'custom' && value) {
                result.letterSpacing = `${value.val}${value.unit || 'px'}`;
            } else if (type === 'narrow') {
                result.letterSpacing = '-0.05em';
            } else if (type === 'wide') {
                result.letterSpacing = '0.2em';
            }
        } else {
            result.letterSpacing = `var(--${cssVarPrefixLetterSpacing}, ${typography.letterSpacing.val}${
                typography.letterSpacing.unit || 'px'
            })`;
        }
    }

    // Handle line height
    if (typography.lineHeight) {
        if (typeof typography.lineHeight === 'object' && 'type' in typography.lineHeight) {
            const { type, value } = typography.lineHeight;
            if (type === 'custom' && value) {
                result.lineHeight = `${value.val}${value.unit || ''}`;
            } else if (['1', '1.5', '2'].includes(type)) {
                result.lineHeight = type;
            }
        } else {
            result.lineHeight = `var(--${cssVarPrefixLineHeight}, ${typography.lineHeight.val}${
                typography.lineHeight.unit || ''
            })`;
        }
    }

    // Handle content alignment
    if (typography.justifyContent) {
        result.justifyContent = typography.justifyContent;
    }

    if (typography.textShadow) {
        const shadow = generateTextShadow(typography.textShadow as SettingsShadowData);
        result.textShadow = shadow || undefined;
    }

    return result;
};
export function generateBoxShadowV2(shadows: SettingsShadow): CSSProperties {
    if (!shadows) {
        return {};
    }
    const { type, position, color, x, y, blur, spread, focus, diffustion } = shadows;

    const isInset = position === 'inside';
    const shadowType = isInset ? 'inset' : '';

    let blurValue = '0px';
    let spreadValue = '0px';

    if (type === 'realistic') {
        if (diffustion) {
            blurValue = `${diffustion.val}${diffustion.unit}`;
        }
        if (focus) {
            spreadValue = `${focus.val}${focus.unit}`;
        }
    } else {
        if (blur) {
            blurValue = `${blur.val}${blur.unit}`;
        }
        if (spread) {
            spreadValue = `${spread.val}${spread.unit}`;
        }
    }

    const boxShadowValue = `${shadowType} ${color} ${x.val}${x.unit} ${y.val}${y.unit} ${blurValue} ${spreadValue}`;
    return { boxShadow: boxShadowValue };
}

export function generateSpacingCSS(
    spacing: Spacing,
    property: 'margin' | 'padding',
    cssVarPrefix?: string,
): CSSProperties {
    if (!spacing) return {};

    if (cssVarPrefix) {
        return {
            [property]: `var(--${cssVarPrefix}-${property}, var(--${cssVarPrefix}-${property}-top, ${generateUnitValue(
                spacing.top,
            )}) var(--${cssVarPrefix}-${property}-right, ${generateUnitValue(
                spacing.right,
            )}) var(--${cssVarPrefix}-${property}-bottom, ${generateUnitValue(
                spacing.bottom,
            )}) var(--${cssVarPrefix}-${property}-left, ${generateUnitValue(spacing.left)}))`,
        };
    }

    return {
        [property]: `${generateUnitValue(spacing.top)} ${generateUnitValue(spacing.right)} ${generateUnitValue(
            spacing.bottom,
        )} ${generateUnitValue(spacing.left)}`,
    };
}

export const createTypographyTransformer =
    (prefix: string) =>
    (data: Record<string, any>): Typography => {
        const typography: any = {};
        const mappings = {
            fontSize: `${prefix}FontSize`,
            fontWeight: `${prefix}FontWeight`,
            fontFamily: `${prefix}FontFamily`,
            color: `${prefix}Color`,
            textTransform: `${prefix}TextTransform`,
            textDecoration: `${prefix}TextDecoration`,
            textAlign: `${prefix}TextAlign`,
            fontStyle: `${prefix}FontStyle`,
            textDirection: `${prefix}TextDirection`,
        };

        Object.entries(mappings).forEach(([key, value]) => {
            if (data[value]) {
                typography[key as keyof Typography] = data[value];
            }
        });

        return typography;
    };
