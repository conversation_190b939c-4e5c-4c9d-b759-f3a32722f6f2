import { FC, ReactNode, ReactElement, Fragment } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { BlockStack, Box } from '@shopify/polaris';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockProperty } from '@/utils/shared';
import {
    LabelSettings,
    KeySettings,
    InitialTextSettings,
    ValidationSettings,
    CharacterLimitSettings,
    PatternValidationSettings,
    LayoutSettings,
} from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/CommonInputSettings';
import { useFormFieldPaths } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/utils';

interface BaseInputFieldSettingsProps {
    formField: FormFieldType;
    children?: ReactNode;
    customSettings?: ReactElement;
    insertPosition?: number;
    showCharLimit?: boolean;
    selectedBlockTarget?: HTMLElement;
}

export const BaseInputFieldSettings: FC<BaseInputFieldSettingsProps> = ({
    formField,
    children,
    customSettings,
    insertPosition = -1,
    showCharLimit = true,
    selectedBlockTarget,
}) => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const { DEFAULT_PATH, DEFAULT_PATH_FORM } = useFormFieldPaths(formField);
    const setCharLimit = getBlockProperty(`${DEFAULT_PATH}.characterLimit.setCharLimit`, selectedBlockId);
    const addPatternValidation = getBlockProperty(`${DEFAULT_PATH}.pattern.addPatternValidation`, selectedBlockId);
    const showInitialText = getBlockProperty(`${DEFAULT_PATH}.showInitialText`, selectedBlockId);

    const settings: ReactElement[] = [
        <LabelSettings key="label" path={DEFAULT_PATH_FORM} blockId={selectedBlockId} />,
        <KeySettings key="key" path={DEFAULT_PATH_FORM} blockId={selectedBlockId} disabled={formField.defaultField} />,
        <InitialTextSettings
            key="initial-text"
            path={DEFAULT_PATH_FORM}
            blockId={selectedBlockId}
            showInitialText={showInitialText}
        />,
        <ValidationSettings key="validation" path={DEFAULT_PATH_FORM} blockId={selectedBlockId} />,
    ];

    if (showCharLimit) {
        settings.push(
            <CharacterLimitSettings
                key="char-limit"
                path={DEFAULT_PATH_FORM}
                blockId={selectedBlockId}
                setCharLimit={setCharLimit}
            />,
        );
    }

    settings.push(
        <PatternValidationSettings
            key="pattern-validation"
            path={DEFAULT_PATH_FORM}
            blockId={selectedBlockId}
            addPatternValidation={addPatternValidation}
        />,
        <LayoutSettings
            key="layout"
            path={DEFAULT_PATH_FORM}
            blockId={selectedBlockId}
            fieldKey={formField.key}
            selectedBlockTarget={selectedBlockTarget}
        />,
    );

    if (customSettings) {
        if (insertPosition >= 0 && insertPosition < settings.length) {
            settings.splice(
                insertPosition,
                0,
                <Fragment key={`custom-settings-${insertPosition}`}>{customSettings}</Fragment>,
            );
        } else {
            settings.push(<Fragment key={`custom-settings-${settings.length}`}>{customSettings}</Fragment>);
        }
    }

    return (
        <Box>
            <BlockStack gap="400">
                {settings}
                {children}
            </BlockStack>
        </Box>
    );
};
