import { useMemo } from 'react';
import { useUncontrolled } from './useUncontrolled';

function range(start: number, end: number) {
    const length = end - start + 1;
    return Array.from({ length }, (_, index) => index + start);
}

export const DOTS = 'dots';

export interface PaginationParams {
    initialPage?: number;
    page?: number;
    total: number;
    siblings?: number;
    boundaries?: number;
    onChange?: (page: number) => void;
}

export function usePagination({
    total,
    siblings = 1,
    boundaries = 1,
    page,
    initialPage = 1,
    onChange,
}: PaginationParams) {
    const _total = Math.max(Math.trunc(total), 0);
    const [activePage, setActivePage] = useUncontrolled({
        value: page,
        onChange,
        defaultValue: initialPage,
        finalValue: initialPage,
    });

    const setPage = (pageNumber: number) => {
        if (pageNumber <= 0) {
            setActivePage(1);
        } else if (pageNumber > _total) {
            setActivePage(_total);
        } else {
            setActivePage(pageNumber);
        }
    };

    const next = () => setPage(activePage + 1);
    const previous = () => setPage(activePage - 1);
    const first = () => setPage(1);
    const last = () => setPage(_total);

    const paginationRange = useMemo((): (number | 'dots')[] => {
        const totalPageNumbers = siblings * 2 + 3 + boundaries * 2;
        if (totalPageNumbers >= _total) {
            return range(1, _total);
        }

        const leftSiblingIndex = Math.max(activePage - siblings, boundaries);
        const rightSiblingIndex = Math.min(activePage + siblings, _total - boundaries);

        const shouldShowLeftDots = leftSiblingIndex > boundaries + 2;
        const shouldShowRightDots = rightSiblingIndex < _total - (boundaries + 1);

        if (!shouldShowLeftDots && shouldShowRightDots) {
            const leftItemCount = siblings * 2 + boundaries + 2;
            return [...range(1, leftItemCount), DOTS, ...range(_total - (boundaries - 1), _total)];
        }

        if (shouldShowLeftDots && !shouldShowRightDots) {
            const rightItemCount = boundaries + 1 + 2 * siblings;
            return [...range(1, boundaries), DOTS, ...range(_total - rightItemCount, _total)];
        }

        return [
            ...range(1, boundaries),
            DOTS,
            ...range(leftSiblingIndex, rightSiblingIndex),
            DOTS,
            ...range(_total - boundaries + 1, _total),
        ];
    }, [_total, siblings, activePage, boundaries]);

    return {
        range: paginationRange,
        active: activePage,
        setPage,
        next,
        previous,
        first,
        last,
        hasNext: activePage < _total,
        hasPrevious: activePage > 1,
    };
}
