/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect, useRef } from 'react';
import { AxiosRequestConfig } from 'axios';
import { httpRequest } from '@/configs';
import { sleep } from '@/utils';

interface QueryConfig<P = unknown> {
    key: string;
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    body?: P;
    headers?: AxiosRequestConfig['headers'];
    enabled?: boolean;
    sleepTime?: number;
    dependencies?: unknown[];
}

export interface QueryResult<T> {
    data: T | null;
    isLoading: boolean;
    error: Error | null;
    refetch: () => Promise<void>;
}

interface QueriesResult<T> {
    results: T;
    isLoading: boolean;
    isSuccess: boolean;
    isError: boolean;
    refetchAll: () => Promise<void>;
}

type ResultsMap = Record<string, QueryResult<unknown>>;

export function useQueries<T = ResultsMap>(queries: QueryConfig[]): QueriesResult<T> {
    const [queryResults, setQueryResults] = useState<ResultsMap>({});
    const [isLoading, setIsLoading] = useState(queries.some((q) => q.enabled !== false));
    const prevQueriesRef = useRef<QueryConfig[]>([]);

    useEffect(() => {
        const initialResults: ResultsMap = {};
        queries.forEach((query) => {
            if (!queryResults[query.key]) {
                initialResults[query.key] = {
                    data: null,
                    isLoading: false,
                    error: null,
                    refetch: async () => {},
                };
            }
        });

        if (Object.keys(initialResults).length > 0) {
            setQueryResults((prev) => ({
                ...prev,
                ...initialResults,
            }));
        }
    }, []);

    const createFetchFunction = (query: QueryConfig) => {
        return async () => {
            setQueryResults((prev) => ({
                ...prev,
                [query.key]: {
                    ...prev[query.key],
                    isLoading: true,
                    error: null,
                },
            }));

            const startTime = Date.now();

            try {
                const response = await httpRequest({
                    url: query.url,
                    method: query.method || 'GET',
                    data: query.body,
                    headers: {
                        'Content-Type': 'application/json',
                        ...query.headers,
                    },
                });

                const elapsedTime = Date.now() - startTime;
                if ((query.sleepTime || 0) > 0 && elapsedTime < query.sleepTime!) {
                    await sleep(query.sleepTime! - elapsedTime);
                }

                setQueryResults((prev) => ({
                    ...prev,
                    [query.key]: {
                        ...prev[query.key],
                        data: response.data,
                        isLoading: false,
                    },
                }));
            } catch (err) {
                setQueryResults((prev) => ({
                    ...prev,
                    [query.key]: {
                        ...prev[query.key],
                        error: err instanceof Error ? err : new Error('An unknown error occurred'),
                        isLoading: false,
                    },
                }));
            }
        };
    };

    useEffect(() => {
        const refetchFunctions: Record<string, () => Promise<void>> = {};

        queries.forEach((query) => {
            refetchFunctions[query.key] = createFetchFunction(query);
        });

        setQueryResults((prev) => {
            const updated = { ...prev };

            Object.keys(refetchFunctions).forEach((key) => {
                if (updated[key]) {
                    updated[key] = {
                        ...updated[key],
                        refetch: refetchFunctions[key],
                    };
                }
            });

            return updated;
        });
    }, [queries.map((q) => q.key).join(',')]);

    const fetchAll = async () => {
        setIsLoading(true);
        const enabledQueries = queries.filter((q) => q.enabled !== false);
        await Promise.all(enabledQueries.map((query) => createFetchFunction(query)()));
        setIsLoading(false);
    };

    const getQueriesToRefetch = () => {
        const prevQueriesMap = prevQueriesRef.current.reduce((acc, query) => {
            acc[query.key] = query;
            return acc;
        }, {} as Record<string, QueryConfig>);

        return queries.filter((current) => {
            const prev = prevQueriesMap[current.key];
            if (!prev) return true;
            if (prev.url !== current.url) return true;
            if (prev.enabled !== current.enabled) return true;
            if (current.dependencies && current.dependencies.length > 0) {
                if (!prev.dependencies) return true;
                for (let i = 0; i < current.dependencies.length; i++) {
                    if (i >= prev.dependencies.length || current.dependencies[i] !== prev.dependencies[i]) {
                        return true;
                    }
                }
            }
            return false;
        });
    };

    useEffect(() => {
        if (prevQueriesRef.current.length === 0) {
            fetchAll();
        } else {
            const queriesToRefetch = getQueriesToRefetch();
            if (queriesToRefetch.length > 0) {
                setIsLoading(true);
                const enabledQueries = queriesToRefetch.filter((q) => q.enabled !== false);
                Promise.all(enabledQueries.map((query) => createFetchFunction(query)())).finally(() => {
                    setIsLoading(false);
                });
            }
        }
        prevQueriesRef.current = [...queries];
    }, [queries.map((q) => `${q.key}-${q.url}-${q.enabled}-${q.dependencies?.join(',') || ''}`).join('|')]);

    const allQueriesFinished = Object.values(queryResults).every((result) => !result.isLoading);
    const hasErrors = Object.values(queryResults).some((result) => result.error !== null);

    return {
        results: queryResults as T,
        isLoading: isLoading || !allQueriesFinished,
        isSuccess: allQueriesFinished && !hasErrors,
        isError: hasErrors,
        refetchAll: fetchAll,
    };
}
