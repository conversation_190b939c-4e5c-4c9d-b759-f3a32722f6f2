
document.querySelectorAll('[data-atk-cname="forms"]').forEach((group) => {
    const soId = group?.dataset?.autoId;
    const btnSubmit = document.querySelector(`[data-auto-id-form-submit="${soId}"]`);
    if (btnSubmit) {
        btnSubmit.style.transform = 'translateY(0px)';
        btnSubmit.style.transition = 'transform 0.1s ease-in-out';
        btnSubmit.addEventListener('mousedown', () => {
            btnSubmit.style.transform = 'translateY(4px)';
        });
        btnSubmit.addEventListener('mouseup', () => {
            btnSubmit.style.transform = 'translateY(0)';
        });
    }
});