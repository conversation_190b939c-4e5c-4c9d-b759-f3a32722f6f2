import { FC } from 'react';
import { SettingsSwitchTab } from '../SettingsSwitchTab';
import { alignVerticalOptions, alignHorizontalOptions } from '../../data/options';

interface SettingsAlignProps {
    blockId: string;
    path: string;
    isUpdateConfigs?: boolean;
}

export const SettingsAlign: FC<SettingsAlignProps> = ({ blockId, path, isUpdateConfigs }) => {
    return (
        <div
            css={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                '& .switch-tabs__container': {
                    width: '100%',
                    minWidth: '128px',
                },
            }}
        >
            <SettingsSwitchTab
                options={alignHorizontalOptions}
                blockId={blockId}
                path={`${path}.alignSelf`}
                direction="column"
                isUpdateConfigs={isUpdateConfigs}
                switchTabProps={{
                    fullWidth: true,
                }}
            />
            <SettingsSwitchTab
                options={alignVerticalOptions}
                blockId={blockId}
                path={`${path}.justifySelf`}
                direction="column"
                isUpdateConfigs={isUpdateConfigs}
                switchTabProps={{
                    fullWidth: true,
                }}
            />
        </div>
    );
};
