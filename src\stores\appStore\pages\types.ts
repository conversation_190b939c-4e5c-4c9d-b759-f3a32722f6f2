import { Auto_BlockData } from '@giaminhautoketing/auto-builder';

export type Status = 'all' | 'published' | 'draft' | 'saved' | 'default';

export interface PageJsonData {
    blocks: Record<string, Auto_BlockData<Record<string, unknown>>>;
    hierarchy: Record<string, string[]>;
}

export interface PageItem {
    thumbnail: string;
    shopId: string;
    status: Status;
    pageType: string;
    id: number;
    title: string;
    handle: string;
    body: string;
    isPublished: boolean;
    templateSuffix: string;
    createdAt: string;
    updatedAt: string;
    templateId: number;
    jsonData: PageJsonData;
}

export interface TemplateType {
    id: number;
    typeName: string;
    icon?: string;
}

export type RequestStatus = 'loading' | 'success' | 'error';
export type RequestName = string;

export interface RequestState {
    status: RequestStatus;
    error?: Error;
}

export interface PageState {
    pageList: PageItem[];
    templateType: TemplateType[];
    apiStatus: RequestStatus;
    requestsStatus: Record<RequestName, RequestState>;
    total: number;
    templatePreview: {
        name: string;
        url: string;
    };
    currentParams: Record<string, unknown>;
    page: PageItem | null;
    templateTypeId: number | string;
    pageId: number | null;
    pageCreationStatus: 'idle' | 'starting' | 'pending' | 'complete';
    hasEdits: boolean;
}

export interface PageActions {
    setPageList: (params?: Record<string, unknown>) => void;
    setApiStatus: (apiStatus: 'loading' | 'success' | 'error') => void;
    setTotal: (total: number) => void;
    updatePage: (
        id: string,
        updateData: Partial<PageItem>,
        currentParams: Record<string, unknown>,
    ) => Promise<PageItem>;
    updatePageStatus: (id: number, status: Status, currentParams: Record<string, unknown>) => Promise<PageItem>;
    updatePageJson: (id: string, updateData: string) => Promise<PageItem>;
    restorePage: (id: number, currentParams: Record<string, unknown>) => Promise<PageItem>;
    deletePage: (pageIds: string[], currentParams: Record<string, unknown>) => Promise<void>;
    unfavoriteTemplate: (templateIds: string[], currentParams: Record<string, unknown>) => Promise<void>;
    duplicatePage: (
        id: string,
        duplicateData: Partial<PageItem>,
        currentParams: Record<string, unknown>,
    ) => Promise<PageItem>;
    setTemplatePreview: (templatePreview: { name: string; url: string }) => void;
    setTemplateType: () => void;
    exportPage: (
        pageIds: string[],
        currentParams: Record<string, unknown>,
    ) => Promise<{ data: { downloadUrl: string } }>;
    importPage: (formData: FormData, currentParams: Record<string, unknown>) => Promise<void>;
    setCurrentParams: (currentParams: Record<string, unknown>) => void;
    getDetailPage: (id: string) => Promise<PageItem>;
    previewPage: (params: number) => Promise<void>;
    createPage: (createData: string) => Promise<PageItem>;
    setTemplateTypeId: (templateTypeId: number | string) => void;
    setPageCreationStatus: (status: 'idle' | 'starting' | 'pending' | 'complete') => void;
    updateRequestStatus: (requestName: RequestName, status: RequestStatus) => void;
    setHasEdits: (hasEdits: boolean) => void;
    setPageId: (pageId: number) => void;
}
