import { useState, useCallback, useEffect } from 'react';
import { Combobox } from '@/components';
import { options } from './configs';
import { useRTE } from '../../context';
import './styles.scss';

export function FontSize() {
    const { editor } = useRTE();
    const [inputValue, setInputValue] = useState('');
    const [filteredOptions, setFilteredOptions] = useState(options);

    const fontSize = editor?.getAttributes('textStyle')['fontSizeDesktop'] || '16px';

    const setFontSize = useCallback(
        (val: string) => {
            editor?.chain().setMarkFontSize(val, 'desktop').run();
        },
        [editor],
    );

    const handleSearch = useCallback((searchValue: string) => {
        const searchOptions = options.filter((option) =>
            option.label.toLowerCase().includes(searchValue.toLowerCase()),
        );
        setFilteredOptions(searchOptions);
    }, []);

    const getLabel = (val: string) => {
        return options.find((option) => option.value === val)?.label || val;
    };

    useEffect(() => {
        setInputValue(getLabel(fontSize.replace('px', '')));
    }, [fontSize]);

    return (
        <Combobox>
            <Combobox.Trigger
                value={inputValue}
                onChange={setInputValue}
                onSearch={handleSearch}
                showArrowIcon
                onlyNumber
                onFocus={() => {
                    editor?.chain().setSpotlightEnabled(true).spotlightSelection().run();
                }}
                onBlur={() => {
                    setInputValue(getLabel(fontSize.replace('px', '')));
                    editor?.chain().setSpotlightEnabled(false).clearSpotlight().focus().run();
                }}
                onKeyDown={(e, setIsOpen) => {
                    if (e.key === 'Enter') {
                        if (!inputValue) {
                            (e.target as HTMLInputElement).blur();
                            setIsOpen(false);
                        } else {
                            setFontSize(inputValue + 'px');
                            setInputValue(inputValue);
                            setIsOpen(false);
                        }
                    }
                    setFilteredOptions(options);
                }}
            />
            <Combobox.Content maxHeight={200} hideEmptyState>
                <Combobox.Options
                    options={filteredOptions}
                    selectedValue={fontSize.replace('px', '')}
                    onSelect={(val) => {
                        setFontSize(val + 'px');
                        setInputValue(getLabel(val.replace('px', '')));
                    }}
                />
            </Combobox.Content>
        </Combobox>
    );
}
