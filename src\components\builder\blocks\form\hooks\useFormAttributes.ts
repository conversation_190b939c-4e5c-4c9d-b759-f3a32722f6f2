/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
import { FormConfig } from '@/components/builder/blocks/form/types';
import { DATA_SET_AUTO_ID_INNER } from '@giaminhautoketing/auto-builder';
import {
    DATA_SET_FORM_CSS_VARIABLE,
    DATA_SET_FORM_HAS_CUSTOM,
    DATA_SET_FORM_CONFIGS,
    DATA_SET_FORM_POSITION,
    DATA_SET_FORM_CUSTOM_CLASS,
    DATA_SET_FORM_CUSTOM_CSS,
} from '@/components/builder/blocks/form/constants';
import {
    DATA_SET_ATK_DISPLAY_ON_DESKTOP,
    DATA_SET_ATK_DISPLAY_ON_MOBILE,
    DATA_SET_ATK_ANIMATION,
} from '@/components/builder/constants/constants';

const getCustomCssAttributes = (configs: FormConfig) => {
    if (!configs?.customCSS?.enable) return {};

    return {
        [DATA_SET_FORM_CUSTOM_CLASS]: configs.customCSS?.className || undefined,
        [DATA_SET_FORM_CUSTOM_CSS]: configs.customCSS?.style || undefined,
    };
};

const hasPosition = (position: any, autoId: string) => {
    if (!position) return false;
    return position.positionType !== 'default' ? autoId : undefined;
};

const hasAnimation = (animation: FormConfig['animation']) => {
    return animation?.type && animation.type !== 'none';
};

export const useFormAttributes = (configs: FormConfig, autoId: string, currentDevice: string, bpConfigs: any) => {
    return useMemo(() => {
        const displayAttr =
            currentDevice === 'desktop'
                ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                : { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile };
        return {
            [DATA_SET_AUTO_ID_INNER]: autoId,
            [DATA_SET_FORM_CSS_VARIABLE]: autoId,
            [DATA_SET_FORM_HAS_CUSTOM]: configs?.customCSS?.enable,
            [DATA_SET_FORM_CONFIGS]: JSON.stringify(configs.submit),
            [DATA_SET_ATK_ANIMATION]: hasAnimation(configs.animation),
            [DATA_SET_FORM_POSITION]: hasPosition(bpConfigs[currentDevice].position, autoId),
            ...getCustomCssAttributes(configs),
            ...displayAttr,
            className: 'animate__animated',
        };
    }, [configs, autoId, currentDevice, bpConfigs]);
};
