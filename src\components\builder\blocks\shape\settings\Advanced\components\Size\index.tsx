import { FC } from 'react';

import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Box, Icon, InlineStack } from '@shopify/polaris';
import { BlockStack } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import './style.scss';

interface SizeProps {
    blockId: string;
    isUpdateConfigs: boolean;
}

export const Size: FC<SizeProps> = ({ blockId, isUpdateConfigs }) => {
    return (
        <BaseCollapse label="Size" labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}>
            <Box paddingBlockStart="300" id="size">
                <BlockStack gap="400">
                    <InlineStack wrap={false} align="space-between" gap="200">
                        <SettingsInput
                            path="mt"
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            label="X"
                            direction="column"
                            inputProps={{
                                suffix: 'px',
                            }}
                        />
                        <SettingsInput
                            path="ml"
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            label="Y"
                            direction="column"
                            inputProps={{
                                suffix: 'px',
                            }}
                        />
                    </InlineStack>
                    <InlineStack wrap={false} align="space-between" gap="200">
                        <SettingsInput
                            path="width"
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            label="Width"
                            direction="column"
                            inputProps={{
                                suffix: 'px',
                            }}
                        />
                        <SettingsInput
                            path="height"
                            blockId={blockId}
                            isUpdateConfigs={isUpdateConfigs}
                            label="Height"
                            direction="column"
                            inputProps={{
                                suffix: 'px',
                            }}
                        />
                    </InlineStack>
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
