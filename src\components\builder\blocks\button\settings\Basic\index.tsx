import { DATA_SET_AUTO_ID_INNER, useBuilderStore } from '@giaminhautoketing/auto-builder';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { SettingElementID } from '@/components/builder/settings/SettingElementID';
import { Content } from '@/components/builder/blocks/button/settings/Basic/components/Contents';
import { Typography } from '@/components/builder/blocks/button/settings/Basic/components/Typography';
import { Styles } from '@/components/builder/blocks/button/settings/Basic/components/Styles';
import { Border } from '@/components/builder/blocks/button/settings/Basic/components/Border';
export const Basic = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;
    const selectedBlockTarget = document.querySelector(
        `[${DATA_SET_AUTO_ID_INNER}="${selectedBlockId}"]`,
    ) as HTMLElement;
    return (
        <BaseHasBorderLayout hasElementID>
            <SettingElementID value={selectedBlockId} />
            <Content id={selectedBlockId} isUpdateConfigs={true} selectedBlockTarget={selectedBlockTarget} />
            <Border id={selectedBlockId} />
            <Typography id={selectedBlockId} selectedBlockTarget={selectedBlockTarget} />
            <Styles id={selectedBlockId} />
        </BaseHasBorderLayout>
    );
};
