/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useEffect } from 'react';
import { ChevronDownIcon, ChevronUpIcon, MinusIcon, PlusIcon } from '@shopify/polaris-icons';
import {
    Auto_BlockViewer,
    BlockViewer,
    DATA_SET_VIEWER,
    DATA_SET_AUTO_ID_INNER,
    useBuilderStore,
} from '@giaminhautoketing/auto-builder';
import { useAppStore } from '@/stores/appStore';
import { getBlockBPProperty } from '@/utils/shared';
import { getLetterSpacing, getLineHeight } from '@/utils';
import { DATA_SET_ATK_DISPLAY_ON_MOBILE } from '@/components/builder/constants/constants';
import { DATA_SET_ATK_DISPLAY_ON_DESKTOP } from '@/components/builder/constants/constants';

export const AccordionViewer: FC<Auto_BlockViewer> = ({ autoId, cname, label, type, bpConfigs, configs }) => {
    const currentDevice = useBuilderStore((state) => state.currentDevice);
    const defaultAccordionId = 'accordion-1';

    const renderIcon = ({ icon, size, color }: { icon: any; size: string; color: string }) => {
        const SvgComponent = icon;
        return <SvgComponent width={size} height={size} fill={color} />;
    };

    const borderTopLeftVal = getBlockBPProperty(`border.radius.top-left.val`, autoId);
    const borderTopRightVal = getBlockBPProperty(`border.radius.top-right.val`, autoId);
    const borderBottomLeftVal = getBlockBPProperty(`border.radius.bottom-left.val`, autoId);
    const borderBottomRightVal = getBlockBPProperty(`border.radius.bottom-right.val`, autoId);

    const gap = configs.gap as any;
    const border = bpConfigs[currentDevice].border as any;
    const blockData = configs.blockData as any;
    const content = configs.content as any;
    const accordionHeader = content.accordionHeader as any;
    const accordion = useAppStore((state) => state.accordion);
    const setAccordion = useAppStore((state) => state.setAccordion);

    useEffect(() => {
        if (!blockData?.length) return;

        if (blockData[0]?.id) {
            setAccordion(autoId, blockData[0].id);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [autoId]);

    const accordionDataAttr = content.allowMultipleOpen
        ? content.defaultAccordion === 'all-items-opened'
            ? 'data-so-accordion-all-active'
            : content.defaultAccordion === 'all-items-closed'
            ? 'data-so-accordion-all-no'
            : 'data-so-accordion-all'
        : content.defaultAccordion === 'all-items-closed'
        ? 'data-so-accordion-no'
        : 'data-so-accordion';

    const toggleItem = (itemId: string) => {
        setAccordion(autoId, itemId);
    };

    const renderAccordionItem = (item: any) => {
        const isOpen = accordion[autoId]?.activeId === item.id;
        const IconComponent =
            accordionHeader.icon.typeIcon === 'arrow'
                ? isOpen
                    ? ChevronUpIcon
                    : ChevronDownIcon
                : isOpen
                ? MinusIcon
                : PlusIcon;

        return (
            <div
                className="accordion-viewer__item"
                key={item.id}
                style={{
                    borderLeft:
                        gap.val !== '0' ? `${border.left.val}${border.left.unit} ${border.type} ${border.color}` : '',
                    borderRight:
                        gap.val !== '0' ? `${border.right.val}${border.right.unit} ${border.type} ${border.color}` : '',
                    borderBottom:
                        gap.val !== '0'
                            ? `${border.bottom.val}${border.bottom.unit} ${border.type} ${border.color}`
                            : '',
                    overflow: 'hidden',
                    borderTopLeftRadius: `${borderTopLeftVal}px`,
                    borderTopRightRadius: `${borderTopRightVal}px`,
                    borderBottomLeftRadius: `${borderBottomLeftVal}px`,
                    borderBottomRightRadius: `${borderBottomRightVal}px`,
                }}
            >
                <div
                    className="accordion-viewer__item-header"
                    style={{
                        display: 'flex',
                        justifyContent: accordionHeader.icon.pos === 'left' ? 'flex-end' : 'space-between',
                        flexDirection: accordionHeader.icon.pos === 'left' ? 'row-reverse' : 'row',
                        alignItems: 'center',
                        cursor: 'pointer',
                        gap: '10px',
                        backgroundColor: accordionHeader.background.color,
                        borderTop:
                            gap.val !== '0'
                                ? `${border.top.val}${border.top.unit} ${
                                      border.type === 'default' ? 'solid' : border.type
                                  } ${border.color}`
                                : '',
                        padding: `${accordionHeader.verticalPadding.val}${accordionHeader.verticalPadding.unit} ${accordionHeader.horizontalPadding.val}${accordionHeader.horizontalPadding.unit}`,
                    }}
                    onClick={() => toggleItem(item.id)}
                >
                    <div
                        className="accordion-viewer__item-header-title"
                        style={{
                            color: accordionHeader.text.color,
                            fontSize: `${accordionHeader.text.fontSize.val}${accordionHeader.text.fontSize.unit}`,
                            fontWeight: accordionHeader.text.fontWeight,
                            fontFamily: accordionHeader.text.fontFamily,
                            lineHeight: getLineHeight(accordionHeader.text.lineHeight),
                            letterSpacing: getLetterSpacing(accordionHeader.text.letterSpacing),
                            textDecoration: accordionHeader.text.textDecoration,
                            textTransform: accordionHeader.text.textTransform,
                        }}
                    >
                        {item.title || 'Accordion Item'}
                    </div>
                    {renderIcon({
                        icon: IconComponent,
                        size: accordionHeader.icon.size.val,
                        color: accordionHeader.icon.color,
                    })}
                </div>
                <div
                    className="accordion-viewer__item-collapse"
                    style={{
                        transition: `height ${content.applyTransition.duration.val}s, min-height ${content.applyTransition.duration.val}s`,
                        overflow: 'hidden',
                        height: '0',
                        minHeight: isOpen ? '150px' : '0',
                        backgroundColor: content.accordionCollapse.background.color,
                        borderTop: isOpen
                            ? `${border.top.val}${border.top.unit} ${
                                  border.type === 'default' ? 'solid' : border.type
                              } ${border.color}`
                            : 'none',
                        borderBottom:
                            gap.val !== '0'
                                ? ''
                                : `${border.top.val}${border.top.unit} ${
                                      border.type === 'default' ? 'solid' : border.type
                                  } ${border.color}`,
                    }}
                >
                    <div className="accordion-viewer__item-collapse-content">{item.content}</div>
                </div>
            </div>
        );
    };

    return (
        <BlockViewer
            autoId={autoId}
            cname={cname}
            label={label}
            type={type}
            bpConfigs={bpConfigs}
            attrs={{ [DATA_SET_VIEWER]: 'true' }}
            configs={configs}
            css={{
                overflow: 'hidden',
                border: gap.val !== '0' ? 'none !important' : '',
                borderBottom: 'none !important',
                borderRadius: gap.val !== '0' ? '0 !important' : '',
            }}
        >
            <div
                className="accordion-viewer"
                {...{ [DATA_SET_AUTO_ID_INNER]: autoId }}
                {...{ [accordionDataAttr]: 'true' }}
                data-default-accordion-id={defaultAccordionId}
                {...(currentDevice === 'desktop'
                    ? { [DATA_SET_ATK_DISPLAY_ON_DESKTOP]: configs.displayOnDesktop }
                    : {})}
                {...(currentDevice === 'mobile' ? { [DATA_SET_ATK_DISPLAY_ON_MOBILE]: configs.displayOnMobile } : {})}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: `${gap.val}${gap.unit}`,
                    overflow: 'hidden',
                    border: gap.val !== '0' ? 'none !important' : '',
                    borderBottom: 'none',
                }}
            >
                {blockData?.map(renderAccordionItem)}
            </div>
        </BlockViewer>
    );
};
