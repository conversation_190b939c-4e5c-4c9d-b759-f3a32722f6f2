import { FC } from 'react';
import {
    BrowserRouter,
    // RouterProvider, RouterProviderProps
} from 'react-router-dom';
import { AppProvider as PolarisProvider } from '@shopify/polaris';
// import { router } from '@/routes';
import enTranslations from '@shopify/polaris/locales/en.json';
import '@shopify/polaris/build/esm/styles.css';
import Routes from './routes/routes';
export const App: FC = () => {
    return (
        <PolarisProvider i18n={enTranslations}>
            {/* <RouterProvider
                router={router}
                future={
                    {
                        v7_relativeSplatPath: false,
                        v7_startTransition: false,
                    } as RouterProviderProps['future']
                }
            /> */}
            <BrowserRouter>
                <Routes />
            </BrowserRouter>
        </PolarisProvider>
    );
};
