import { FC } from 'react';
import { Text } from '@shopify/polaris';
import { Auto_BlockType } from '@giaminhautoketing/auto-builder';
import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { htmlCodeToolbarOptions } from '../configs';
import { ReactComponent as CodeOutlineIcon } from '@/assets/svgs/code-outline.svg';

export const HtmlCodePreview: FC = () => {
    return (
        <>
            {htmlCodeToolbarOptions.map((option) => (
                <BlockToolbar
                    id={option.id}
                    key={option.id}
                    cname={option.cname}
                    label={option.label}
                    type={option.type as Auto_BlockType}
                    bpConfigs={option.bpConfigs}
                    overlay={option.overlay}
                    configs={option.configs}
                    style={{
                        width: '100%',
                        height: '10rem',
                        background: '#FAFBFB',
                        borderRadius: '8px',
                        border: '1px solid #e5e5e5',
                        cursor: 'move',
                        display: 'flex',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        alignItems: 'center',
                    }}
                >
                    <CodeOutlineIcon style={{ width: '2.5rem', height: '2.5rem' }} />
                    <Text as="span" variant="headingMd" fontWeight="medium">
                        HTML Code
                    </Text>
                </BlockToolbar>
            ))}
        </>
    );
};
