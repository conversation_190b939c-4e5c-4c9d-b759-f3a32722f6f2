:root {
    --switch-checked-background-color: #75a434;
    --switch-background-color: #d2d1d4;
    --switch-button-color: #fff;
    --switch-label-color: #202223;
}

.switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &__label {
        font-size: 12px;
        color: var(--p-color-text);
        font-weight: 450;
    }

    &__control {
        position: relative;
        display: inline-block;
        width: 38px;
        height: 20px;
    }

    &__checkbox--input {
        opacity: 0;
        width: 0;
        height: 0;
        &:checked + .switch__label_checkbox {
            background-color: var(--switch-checked-background-color);
            &::before {
                transform: translateX(18px);
            }
        }
    }

    &__label_checkbox {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--switch-background-color);
        transition: 0.2s;
        border-radius: 18px;

        &::before {
            position: absolute;
            content: '';
            height: 18px;
            width: 18px;
            border-radius: 50%;
            left: 1px;
            bottom: 1px;
            background-color: white;
            transition: 0.2s;
        }
    }
}
