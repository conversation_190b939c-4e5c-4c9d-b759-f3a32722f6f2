import { FC } from 'react';
import { InlineStack, Text } from '@shopify/polaris';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { getBlockTypeClassName, getTabsInspectorOptions } from '@/pages/BuilderPage/utils';
import './style.scss';

interface SettingTabsInspectorProps {
    activeTab: string;
    onChange: (tabId: string) => void;
    selectedBlock: Auto_BlockData;
}

export const SettingTabsInspector: FC<SettingTabsInspectorProps> = ({ activeTab, onChange, selectedBlock }) => {
    const updateTab = (tabId: string) => {
        onChange(tabId);
    };
    const tabsInspectorOptions = getTabsInspectorOptions(selectedBlock?.type || '');
    return (
        <div className={`setting-tabs-inspector ${getBlockTypeClassName(selectedBlock?.type || '')}`}>
            {tabsInspectorOptions.map((tab) => (
                <button
                    key={tab.id}
                    onClick={() => updateTab(tab.id)}
                    className={`setting-tabs-inspector__tab ${activeTab === tab.id ? 'active' : ''}`}
                >
                    <InlineStack align="center" blockAlign="center" gap="050">
                        <Text as="p" fontWeight={activeTab === tab.id ? 'medium' : 'regular'} tone="base">
                            {tab.content}
                        </Text>
                    </InlineStack>
                </button>
            ))}
        </div>
    );
};
