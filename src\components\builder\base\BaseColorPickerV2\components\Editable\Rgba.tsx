import { ChangeEvent, FC, FocusEvent } from 'react';
import { ColorResult, HsvaColor, RgbaColor, hsvaToRgba, rgbaToHsva, color as handleColor } from '@uiw/color-convert';
import { ChanelInput } from './ChanelInput';

interface RgbaProps {
    hsva?: HsvaColor;
    onChange?: (color: ColorResult) => void;
}

export const Rgba: FC<RgbaProps> = ({ hsva, onChange }) => {
    const rgba = (hsva ? hsvaToRgba(hsva) : {}) as RgbaColor;

    const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        if (value && value > 255) {
            e.target.value = '255';
        }
        if (value && value < 0) {
            e.target.value = '0';
        }
    };
    const handleChange = (value: string | number, type: 'r' | 'g' | 'b' | 'a', e: ChangeEvent<HTMLInputElement>) => {
        if (typeof value === 'number') {
            if (type === 'a') {
                if (value < 0) value = 0;
                if (value > 100) value = 100;
                if (onChange) {
                    onChange(handleColor(rgbaToHsva({ ...rgba, a: value / 100 })));
                }
            }
            if (value > 255) {
                value = 255;
                e.target.value = '255';
            }
            if (value < 0) {
                value = 0;
                e.target.value = '0';
            }
            if (type === 'r') {
                if (onChange) {
                    onChange(handleColor(rgbaToHsva({ ...rgba, r: value })));
                }
            }
            if (type === 'g') {
                if (onChange) {
                    onChange(handleColor(rgbaToHsva({ ...rgba, g: value })));
                }
            }
            if (type === 'b') {
                if (onChange) {
                    onChange(handleColor(rgbaToHsva({ ...rgba, b: value })));
                }
            }
        }
    };

    return (
        <div
            css={{
                display: 'flex',
                width: '100%',
                height: '32px',
                border: '1px solid #ebebeb',
                borderRadius: '8px',
                overflow: 'hidden',
            }}
        >
            <ChanelInput
                value={rgba.r || 0}
                onBlur={handleBlur}
                onChange={(e, val) => handleChange(val, 'r', e)}
                css={{ flex: 1, textAlign: 'center' }}
            />
            <ChanelInput
                value={rgba.g || 0}
                onBlur={handleBlur}
                onChange={(evn, val) => handleChange(val, 'g', evn)}
                css={{ flex: 1, textAlign: 'center' }}
            />
            <ChanelInput
                value={rgba.b || 0}
                onBlur={handleBlur}
                onChange={(evn, val) => handleChange(val, 'b', evn)}
                css={{ flex: 1, textAlign: 'center' }}
            />
            <ChanelInput
                value={(rgba.a ? Math.round(rgba.a * 100) : 0) + '%'}
                onChange={(evn, val) => handleChange(val, 'a', evn)}
                css={{ width: '48px', textAlign: 'center', borderRightStyle: 'none' }}
            />
        </div>
    );
};
