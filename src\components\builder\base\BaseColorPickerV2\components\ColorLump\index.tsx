import { FC } from 'react';
import { HsvaColor, color } from '@uiw/color-convert';

interface ColorLumpProps {
    hsva: HsvaColor;
}

export const ColorLump: FC<ColorLumpProps> = ({ hsva }) => {
    return (
        <div
            css={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                width: '40px',
                height: '40px',
                borderRadius: '6px',
                outline: '2px solid #eeeeee',
                backgroundImage:
                    'conic-gradient(rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 25%, transparent 0deg, transparent 50%, rgb(238, 238, 238) 0deg, rgb(238, 238, 238) 75%, transparent 0deg)',
                backgroundSize: '8px 8px',
                overflow: 'hidden',
                flexShrink: 0,
            }}
        >
            <div css={{ background: color({ ...hsva, a: 1 }).hexa }}></div>
            <div css={{ backgroundColor: color(hsva).hexa }}></div>
        </div>
    );
};
