import { FC } from 'react';
import { BlockStack, Box, TextProps } from '@shopify/polaris';
import { BaseItemLayout } from '../../base/BaseItemLayout';
import { BaseInput } from '../../base/BaseInput';
import { getBlockProperty } from '@/utils/shared';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { getBlockBPProperty } from '@/utils/shared';
import { useBlockStore } from '@giaminhautoketing/auto-builder';

type BaseItemLayoutProps = Parameters<typeof BaseItemLayout>[0];
type BaseInputProps = Parameters<typeof BaseInput>[0];

interface SettingsCustomCSSProps extends Pick<BaseItemLayoutProps, 'direction' | 'containerClassName'> {
    path: string;
    blockId: string;
    isUpdateConfigs?: boolean;
    label: string;
    inputProps?: Omit<BaseInputProps, 'value' | 'onChange'>;
    textProps?: Omit<Partial<TextProps>, 'children'>;
}

export const SettingsCustomCSS: FC<SettingsCustomCSSProps> = ({
    path,
    blockId,
    isUpdateConfigs,
    inputProps,
    textProps,
    ...otherProps
}) => {
    const defaultTextProps = { as: 'p', variant: 'bodyMd' };

    const updateBlockProperty = useBlockStore((state) => state.updateBlockProperty);
    const updateBlockConfigsProperty = useBlockStore((state) => state.updateBlockConfigsProperty);
    const currentDevice = useBuilderStore((state) => state.currentDevice);

    const classNameValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.className`, blockId)
        : getBlockBPProperty(`${path}.className`, blockId);

    const styleValue = isUpdateConfigs
        ? getBlockProperty(`configs.${path}.style`, blockId)
        : getBlockBPProperty(`${path}.style`, blockId);

    const handleClassNameChange = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.className`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.className`, value);
        }
    };

    const handleStyleChange = (value: string) => {
        if (isUpdateConfigs) {
            updateBlockConfigsProperty(blockId, `${path}.style`, value);
        } else {
            updateBlockProperty(blockId, currentDevice, `${path}.style`, value);
        }
    };
    return (
        <Box paddingBlockStart="300">
            <BlockStack gap="300">
                <BaseItemLayout
                    textProps={{ ...defaultTextProps, ...textProps, children: 'Class name' } as TextProps}
                    direction="column"
                    {...otherProps}
                >
                    <BaseInput
                        type="text"
                        value={classNameValue}
                        onChange={handleClassNameChange}
                        placeholder="Enter custom class name"
                        {...inputProps}
                    />
                </BaseItemLayout>
                <BaseItemLayout
                    textProps={{ ...defaultTextProps, ...textProps, children: 'Custom style' } as TextProps}
                    direction="column"
                    {...otherProps}
                >
                    <BaseInput
                        type="text"
                        value={styleValue}
                        onChange={handleStyleChange}
                        placeholder="Enter custom style"
                        multiline={4}
                        maxHeight={100}
                        {...inputProps}
                    />
                </BaseItemLayout>
            </BlockStack>
        </Box>
    );
};
