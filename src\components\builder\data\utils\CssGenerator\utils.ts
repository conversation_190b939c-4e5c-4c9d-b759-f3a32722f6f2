/* eslint-disable @typescript-eslint/no-explicit-any */
import { generateTextShadow, generateBoxShadow } from '@/utils';
import { FormField } from '@/components/builder/blocks/form/types';
import {
    UnitValue,
    Spacing,
    Border,
    Background,
    Typography,
    BorderRadius,
    FormSubmitStyle,
    GeneralForm,
} from '@/components/builder/data/utils/CssGenerator/types';
import {
    createTypographyTransformer,
    generateCssProperty,
    generateCssWithVariable,
    generateSpacingValue,
    generateUnitValue,
} from '@/components/builder/data/utils/CssGenerator/helper';
import { SettingsShadowData } from '@/components/builder/settings/SettingsShadow/types';

// CSS Generation functions
export const generateSpacing = (spacing: Spacing, property: 'padding' | 'margin', cssVariable: string): string => {
    if (!spacing) return '';
    const directions = ['top', 'right', 'bottom', 'left'];

    return directions
        .map((dir) =>
            generateCssWithVariable(
                `${property}-${dir}`,
                [`${cssVariable}-${property}`, `${cssVariable}-${property}-${dir}`],
                generateUnitValue(spacing[dir as keyof Spacing]),
            ),
        )
        .join('\n');
};

export const generateAlignment = (value: { alignSelf: string; justifySelf: string }): string => {
    const alignmentMap = {
        'left top': 'start',
        'left middle': 'center start',
        'left bottom': 'end start',
        'center top': 'start center',
        'center middle': 'center',
        'center bottom': 'end center',
        'right top': 'start end',
        'right middle': 'center end',
        'right bottom': 'end end',
    };

    const alignment = `${value.alignSelf} ${value.justifySelf}`;
    return `place-self: ${alignmentMap[alignment as keyof typeof alignmentMap] || 'start'};`;
};

export const generateBorderWidth = (
    border: Pick<Border, 'top' | 'right' | 'bottom' | 'left'> | undefined | null,
): string => {
    if (!border) return '0px 0px 0px 0px';
    const values = ['top', 'right', 'bottom', 'left'].map((dir) =>
        generateUnitValue(border[dir as keyof typeof border]),
    );
    return values.join(' ');
};

export const generateBorderRadius = (radius: BorderRadius): string => {
    if (!radius) return '0px 0px 0px 0px';
    const corners = ['top-left', 'top-right', 'bottom-right', 'bottom-left'];
    return corners.map((corner) => generateUnitValue(radius[corner as keyof BorderRadius])).join(' ');
};

export const generateBorder = (border: Border): string => {
    if (!border) return '';
    return `
        ${generateCssProperty('border-width', generateBorderWidth(border))}
        ${generateCssProperty('border-radius', generateBorderRadius(border.radius))}
        ${generateCssProperty('border-color', border.color || 'transparent')}
        ${generateCssProperty('border-style', border.type === 'default' ? 'solid' : border.type || 'none')}
    `;
};

export const generateBackground = (background: Background): string => {
    if (!background) return '';
    if (background.type === 'color') {
        return generateCssProperty('background', background.color || 'transparent');
    }
    if (background.type === 'image' && background.image) {
        const { url, repeat, position, attachment, fill } = background.image;
        return `
            ${generateCssProperty('background', `url(${url})`)}
            ${generateCssProperty('background-repeat', repeat ? (repeat === 'no' ? 'no-repeat' : 'repeat') : 'no-repeat')}
            ${generateCssProperty('background-position', position || 'center')}
            ${generateCssProperty('background-attachment', attachment || 'scroll')}
            ${generateCssProperty('background-size', fill || 'auto')}
        `;
    }
    return '';
};

export const generateTypography = (styles: Typography, letterSpacingVar?: string, lineHeightVar?: string): string => {
    if (!styles) return '';

    const DEFAULTS = {
        FONT: {
            WEIGHT: 'normal',
            FAMILY: 'inherit',
            COLOR: 'inherit',
        },
        ALIGN: 'center',
    } as const;

    const ALIGN_TO_FLEX = {
        left: 'flex-start',
        justify: 'flex-start',
        right: 'flex-end',
        center: 'center',
    } as const;

    const css = (condition: boolean, prop: string, value: string) =>
        condition ? generateCssProperty(prop, value) : '';

    const cssVariable = (condition: boolean, prop: string, varName: string, value: string) =>
        condition ? generateCssWithVariable(prop, varName, value) : '';

    const basic = `
        --button-content-align: ${ALIGN_TO_FLEX[styles.textAlign as keyof typeof ALIGN_TO_FLEX] || DEFAULTS.ALIGN};
        ${generateCssProperty('font-size', generateUnitValue(styles.fontSize))}
        ${generateCssProperty('font-weight', styles.fontWeight || DEFAULTS.FONT.WEIGHT)}
        ${generateCssProperty('font-family', styles.fontFamily || DEFAULTS.FONT.FAMILY)}
        ${generateCssProperty('color', styles.color || DEFAULTS.FONT.COLOR)}
    `;

    const text = `
        ${css(styles.textTransform !== 'default', 'text-transform', styles.textTransform!)}
        ${css(styles.textDecoration !== 'default', 'text-decoration', styles.textDecoration!)}
        ${css(!!styles.textAlign, 'text-align', styles.textAlign!)}
        ${css(styles.fontStyle !== 'default', 'font-style', styles.fontStyle!)}
        ${css(!!styles.textDirection, 'direction', styles.textDirection!)}
    `;

    const spacing = `
        ${cssVariable(
            !!styles.letterSpacing,
            'letter-spacing',
            letterSpacingVar ?? '',
            generateSpacingValue(styles.letterSpacing ?? { val: '0', unit: 'px' }),
        )}
        ${cssVariable(
            !!styles.lineHeight,
            'line-height',
            lineHeightVar ?? '',
            generateSpacingValue(styles.lineHeight ?? { val: '0', unit: '' }),
        )}
    `;

    const shadow = `
        ${css(!!styles.textShadow, 'text-shadow', generateTextShadow(styles.textShadow! as SettingsShadowData) || '')}
        ${css(!!styles.boxShadow, 'box-shadow', generateBoxShadow(styles.boxShadow! as SettingsShadowData) || '')}
    `;

    return `
        ${basic}
        ${text}
        ${spacing}
        ${shadow}
    `;
};

// Form specific functions
export const generateSubmitButtonStyles = (data: FormSubmitStyle): string => {
    const getLetterSpacing = (spacing: { type: string; value: UnitValue }) => {
        const spacingMap = {
            auto: { val: 'normal', unit: '' },
            narrow: { val: '-0.05', unit: 'em' },
            wide: { val: '0.2', unit: 'em' },
            custom: spacing.value,
            default: undefined,
        };
        return spacingMap[spacing.type as keyof typeof spacingMap] || undefined;
    };

    const getLineHeight = (spacing: { type: string; value: UnitValue }) => {
        const heightMap = {
            '1': { val: 'normal', unit: '' },
            '1.5': { val: '1.5', unit: '' },
            '2': { val: '2', unit: '' },
            custom: spacing.value,
            default: undefined,
        };
        return heightMap[spacing.type as keyof typeof heightMap] || undefined;
    };

    const typography: Typography = {
        fontSize: data.fontSize,
        fontWeight: data.fontWeight,
        fontFamily: data.fontFamily,
        color: data.color,
        textTransform: data.textTransform,
        textDecoration: data.textDecoration,
        textAlign: data.textAlign,
        fontStyle: data.fontStyle,
        letterSpacing: getLetterSpacing(data.buttonLetterSpacing),
        lineHeight: getLineHeight(data.buttonLineSpacing),
        textShadow: data.textShadow,
        boxShadow: data.boxShadow,
    };

    return `
        ${generateCssWithVariable('width', '--form-button-width', generateUnitValue(data.buttonWidth))}
        ${generateCssWithVariable('height', '--form-button-height', generateUnitValue(data.buttonHeight))}
        ${generateTypography(typography, '--form-button-letter-spacing', '--form-button-line-height')}
        ${generateBackground(data.background)}
        ${generateBorder(data.buttonBorder)}
        ${generateSpacing(data.buttonSpacing?.padding, 'padding', '--form-button-spacing')}
        ${generateSpacing(data.buttonSpacing?.margin, 'margin', '--form-button-spacing')}
    `;
};

export const generateButtonArrangement = (arrangement: { buttonAlign: string; spaceTitleField: UnitValue }): string => {
    return `
        display: flex;
        align-items: center;
        justify-content: ${arrangement.buttonAlign || 'center'};
        ${generateCssWithVariable(
            'padding-top',
            '--form-space-title-field',
            generateUnitValue(arrangement.spaceTitleField),
        )}
    `;
};

export const generateGeneralFormVariable = (value: GeneralForm): string => {
    return `
        --form-display: flex;
        --form-direction: column;
        ${generateCssWithVariable(
            '--form-general-spaceTitleField',
            '--form-space-title-field',
            `${value.spaceTitleField.val}${value.spaceTitleField.unit}`,
        )}
        ${generateCssWithVariable(
            '--form-general-horizontal',
            '--form-horizontal',
            `${value.horizontal.val}${value.horizontal.unit}`,
        )}
        ${generateCssWithVariable(
            '--form-general-vertical',
            '--form-vertical',
            `${value.vertical.val}${value.vertical.unit}`,
        )}
        --form-general-align: ${value.align};
    `;
};

export const generateSizeFormFieldVariable = (value: Record<string, any>): string => {
    return Object.entries(value)
        .map(([key, val]) => {
            return [
                generateCssWithVariable(
                    `--form-field-${key}-width`,
                    `--form-${key}-width`,
                    generateUnitValue(val.fieldWidth),
                ),
                generateCssWithVariable(
                    `--form-field-${key}-height`,
                    `--form-${key}-height`,
                    generateUnitValue(val.fieldHeight),
                ),
                key.includes('checkbox')
                    ? generateCssWithVariable(
                          `--form-field-${key}-checkbox-size`,
                          `--form-${key}-checkbox-size`,
                          generateUnitValue(val.checkboxSize),
                      )
                    : '',
                key.includes('radio')
                    ? generateCssWithVariable(
                          `--form-field-${key}-radio-size`,
                          `--form-${key}-radio-size`,
                          generateUnitValue(val.radioSize),
                      )
                    : '',
            ].join('\n');
        })
        .join('');
};

export const generateFormFieldVariable = (forms: FormField[]): string => {
    if (!forms) return '';
    return forms
        .map((field: any) => {
            const lines: string[] = [];
            if (field.type === 'checkbox' || field.type === 'radio') {
                lines.push(`--form-field-${field.id}-direction: ${field.direction};`);
            }
            lines.push(`--form-field-${field.id}-separate-line-width: ${field.separateLine ? '100%' : '0%'};`);
            lines.push(
                `--form-field-${field.id}-separate-line-display: ${field.separateLine ? 'inline-block' : 'none'};`,
            );
            return lines.join('\n');
        })
        .join('\n');
};

export const generateFormAlignment = (value: { alignSelf: string; justifySelf: string }): string => {
    const alignmentMap = {
        'left top': 'start',
        'left middle': 'center start',
        'left bottom': 'end start',
        'center top': 'start center',
        'center middle': 'center',
        'center bottom': 'end center',
        'right top': 'start end',
        'right middle': 'center end',
        'right bottom': 'end end',
    };

    const alignment = `${value.alignSelf} ${value.justifySelf}`;
    return `place-self: ${alignmentMap[alignment as keyof typeof alignmentMap] || 'start'};`;
};

// Typography transformers
export const transformLabelToTypography = createTypographyTransformer('label');
export const transformInputToTypography = createTypographyTransformer('input');
export const transformPlaceholderToTypography = createTypographyTransformer('placeholder');

// Button content styles
export const generateContentStyles = (styles: { direction: string; reverse: boolean; spacing: UnitValue }): string => {
    const { direction, reverse, spacing } = styles;
    return `
        display: flex;
        align-items: center;
        justify-content: var(--button-content-align);
        flex-direction: ${direction}${reverse ? '-reverse' : ''};
        ${generateCssWithVariable('column-gap', '--atk-button-gap', generateUnitValue(spacing))}
    `;
};

export const generateButtonSpace = (styles: any, cssVariable: string): string => {
    return `padding: ${['top', 'right', 'bottom', 'left']
        .map(
            (dir) =>
                `var(${cssVariable}-padding, var(${cssVariable}-padding-${dir}, ${generateUnitValue(
                    styles.padding[dir],
                )}))`,
        )
        .join(' ')};`;
};
