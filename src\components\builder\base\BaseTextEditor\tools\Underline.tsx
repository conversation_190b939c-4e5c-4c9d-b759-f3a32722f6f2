import { TextUnderlineIcon } from '@shopify/polaris-icons';
import { useRTE } from '../context';
import { BaseTool } from './BaseTool';

export const Underline = () => {
    const { editor } = useRTE();
    const isActive = editor?.isActive('underline');
    const toggleUnderline = () => {
        editor?.chain().focus().toggleUnderline().run();
    };
    return <BaseTool onClick={toggleUnderline} icon={TextUnderlineIcon} tooltip="Underline" isActive={isActive} />;
};
