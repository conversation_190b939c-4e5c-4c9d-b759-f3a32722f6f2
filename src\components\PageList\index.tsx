import { useCallback, useEffect, useState, type FC } from 'react';
import type { IndexFiltersProps, ResourceListProps } from '@shopify/polaris';
import {
    BlockStack,
    Box,
    Card,
    ChoiceList,
    IndexFilters,
    ResourceItem,
    Pagination,
    ResourceList,
    Text,
    useSetIndexFiltersMode,
} from '@shopify/polaris';
import { Modal, TitleBar } from '@shopify/app-bridge-react';
import { useDebounce } from '@/hooks';
import { useAppStore } from '@/stores/appStore/useAppStore';
import {
    getSortParams,
    resourceName,
    sortOptions,
    pageStatusChoices,
    disambiguateLabel,
    isEmpty,
    getStatusFromChoices,
    handleApiCall,
    showToast,
} from './configs';
import { PageListParams } from './types';
import { PageItem } from './PageItem';
import { Skeleton } from './Skeleton';
import imgDataEmptyUrl from '@/assets/svgs/data-empty.svg?url';
import imgSearchEmptyUrl from '@/assets/svgs/search-empty.svg?url';
import imgErrorEmptyUrl from '@/assets/svgs/500.svg?url';
import './style.scss';
import { type PageItem as PageItemType } from '@/stores/appStore/pages/types';

export const PageList: FC = () => {
    const isHomePage = window.location.pathname === '/';
    const isAuthenticated = useAppStore((state) => state.isAuthenticated);
    const setCurrentParams = useAppStore((state) => state.setCurrentParams);
    const currentParams = useAppStore((state) => state.currentParams);
    const setPageList = useAppStore((state) => state.setPageList);
    const pageList = useAppStore((state) => state.pageList);
    const requestsStatus = useAppStore((state) => state.requestsStatus);
    const totalPages = useAppStore((state) => state.total);
    const templateType = useAppStore((state) => state.templateType);
    const deletePage = useAppStore((state) => state.deletePage);
    const unfavoriteTemplate = useAppStore((state) => state.unfavoriteTemplate);
    const exportPage = useAppStore((state) => state.exportPage);
    const pageCreationStatus = useAppStore((state) => state.pageCreationStatus);
    const [tabSelected, setTabSelected] = useState(0);
    const [queryValue, setQueryValue] = useState('');
    const { mode, setMode } = useSetIndexFiltersMode();
    const [page, setPage] = useState(1);
    const [sortSelected, setSortSelected] = useState<string[]>(['updatedAt desc']);
    const [statusChoices, setStatusChoices] = useState<string[]>([]);
    const [pageType, setPageType] = useState<string[]>([]);
    const [items, setItems] = useState<PageItemType[]>([]);

    const [selectedItems, setSelectedItems] = useState<ResourceListProps['selectedItems']>([]);
    const [activePopoverId, setActivePopoverId] = useState<number | null>(null);
    const searchValue = useDebounce(queryValue, 500, () => setPage(1));
    const LIMIT_PAGE = 5;

    const [modalState, setModalState] = useState({
        open: false,
        kind: 'delete',
    });

    const handleDelete = useCallback(async () => {
        if (!selectedItems?.length) {
            showToast('Please select at least one page', true);
            return;
        }

        const apiCall =
            currentParams.status === 3
                ? () => unfavoriteTemplate(selectedItems as string[], currentParams)
                : () => deletePage(selectedItems as string[], currentParams);

        await handleApiCall(
            apiCall,
            'Page deleted',
            'Delete failed',
            undefined,
            setModalState,
            setSelectedItems as React.Dispatch<React.SetStateAction<string[]>>,
        );
    }, [selectedItems, currentParams, deletePage, unfavoriteTemplate]);

    const handleExport = useCallback(async () => {
        if (!selectedItems?.length) {
            showToast('Please select at least one page', true);
            return;
        }

        await handleApiCall(
            () => exportPage(selectedItems as string[], currentParams),
            'Page exported',
            'Export failed',
            (response) => {
                setModalState({ open: false, kind: 'export' });
                setSelectedItems([]);
                setTimeout(() => {
                    window.open(response.data.downloadUrl, '_blank');
                }, 1500);
            },
            setModalState,
            setSelectedItems as React.Dispatch<React.SetStateAction<string[]>>,
        );
    }, [selectedItems, currentParams, exportPage]);

    const modalContent: Record<string, { title: string; content: string; onAction: () => void; okText: string }> = {
        delete: {
            title: 'Delete pages',
            content: 'Are you sure you want to delete selected pages?',
            onAction: handleDelete,
            okText: 'Delete',
        },
        export: {
            title: 'Export pages',
            content: 'Do you want to export selected pages?',
            onAction: handleExport,
            okText: 'Yes, export',
        },
    };

    const promotedBulkActions = [
        {
            content: 'Delete pages',
            onAction: () => {
                setModalState({ open: true, kind: 'delete' });
            },
        },
        {
            content: 'Export selected pages',
            onAction: () => {
                setModalState({ open: true, kind: 'export' });
            },
        },
    ];

    const tabs = ['All', 'Published', 'Draft', 'Favourite template'].map((item, index) => ({
        content: item,
        index,
        onAction: () => {},
        id: `${item}-${index}`,
        isLocked: true,
        actions: [],
    }));

    const tabsTemplate = [
        {
            content: 'All',
            index: 0,
            onAction: () => {},
            id: 'template-all-0',
            isLocked: true,
            actions: [],
        },
        ...templateType.map((item) => ({
            content: item.typeName,
            index: item.id,
            onAction: () => {},
            id: `template-${item.typeName}-${item.id}`,
            isLocked: true,
            actions: [],
        })),
    ];

    useEffect(() => {
        setItems(pageList);
    }, [pageList]);

    const onQueryChange = useCallback((value: string) => {
        setQueryValue(value);
    }, []);

    const onSort = useCallback((value: string[]) => {
        setSortSelected(value);
    }, []);

    const onChangeTab = useCallback(
        (index: number) => {
            if (index === tabSelected) return;
            setPage(1);
            setTabSelected(index);
            if (!isHomePage) {
                setPageType([index.toString()]);
            }
        },
        [isHomePage, tabSelected],
    );

    const handleStatusChange = useCallback((value: string[]) => {
        setPage(1);
        setStatusChoices(value);
    }, []);

    const handlePageTypeChange = useCallback((value: string[]) => {
        setPage(1);
        setPageType(value);
    }, []);

    const handleStatusRemove = useCallback(() => {
        setStatusChoices([]);
    }, []);

    const handlePageTypeRemove = useCallback(() => {
        setPageType([]);
    }, []);

    const onClearAll = useCallback(() => {
        // setPageType([]);
        setTabSelected(0);
        setQueryValue('');
        handleStatusRemove();
        handlePageTypeRemove();
    }, [handleStatusRemove, handlePageTypeRemove]);

    const onQueryClear = useCallback(() => {
        setQueryValue('');
        onClearAll();
    }, [onClearAll]);

    const handleSelectionChange = useCallback(
        (newSelectedItems: string[]) => {
            const selectableIds = items
                .filter((item) => {
                    return item.status !== 'default';
                })
                .map((item) => item.id.toString());

            if (newSelectedItems.length === items.length || newSelectedItems.length === selectableIds.length) {
                if (selectableIds.every((id) => selectedItems?.includes(id))) {
                    setSelectedItems([]);
                } else {
                    setSelectedItems(selectableIds);
                }
            } else {
                setSelectedItems(newSelectedItems);
            }
        },
        [items, selectedItems],
    );

    const filters = [
        {
            key: 'status',
            label: 'Status',
            filter: (
                <ChoiceList
                    title="Status"
                    titleHidden
                    choices={pageStatusChoices}
                    selected={statusChoices}
                    onChange={handleStatusChange}
                    allowMultiple={false}
                />
            ),
            shortcut: true,
        },
        {
            key: 'pageType',
            label: 'Page type',
            filter: (
                <ChoiceList
                    title="Page type"
                    titleHidden
                    choices={templateType.map((item) => ({
                        label: item.typeName,
                        value: item.id.toString(),
                    }))}
                    selected={pageType}
                    onChange={handlePageTypeChange}
                    allowMultiple={false}
                />
            ),
            shortcut: true,
        },
    ];

    const appliedFilters: IndexFiltersProps['appliedFilters'] = [];
    if (statusChoices && !isEmpty(statusChoices)) {
        const key = 'status';
        appliedFilters.push({
            key,
            label: disambiguateLabel(key, statusChoices),
            onRemove: handleStatusRemove,
        });
    }

    if (pageType && !isEmpty(pageType)) {
        const key = 'pageType';
        appliedFilters.push({
            key,
            label: disambiguateLabel(key, pageType, templateType),
            onRemove: handlePageTypeRemove,
        });
    }

    useEffect(() => {
        if (isAuthenticated && pageCreationStatus === 'pending') {
            const params: PageListParams = {
                currentPage: page,
                perPage: LIMIT_PAGE,
                ...(isHomePage
                    ? { status: tabSelected }
                    : statusChoices.length > 0
                    ? { status: getStatusFromChoices(statusChoices[0]) }
                    : {}),
                ...(sortSelected.length > 0 && getSortParams(sortSelected[0])),
                ...(searchValue && { keyword: searchValue }),
                ...(pageType.length > 0 && { pageType: pageType.join(',') }),
            };
            setCurrentParams(params);
            setPageList(params);
        }
    }, [
        isAuthenticated,
        tabSelected,
        searchValue,
        page,
        LIMIT_PAGE,
        sortSelected,
        statusChoices,
        isHomePage,
        pageType,
        setPageList,
        setCurrentParams,
        pageCreationStatus,
    ]);

    useEffect(() => {
        setSelectedItems([]);
    }, [page, tabSelected, sortSelected, searchValue]);

    const handleNext = useCallback(() => {
        setPage(page + 1);
    }, [page]);

    const handlePrevious = useCallback(() => {
        setPage(page - 1);
    }, [page]);

    return (
        <div className="page-list">
            <Card padding="0">
                {isHomePage && (
                    <>
                        <Box paddingInline="400" paddingBlockStart="500" paddingBlockEnd="200">
                            <Text as="h3" variant="headingMd">
                                Page list
                            </Text>
                        </Box>
                        <Box paddingInline="400" paddingBlockEnd="200">
                            <Text as="p" variant="bodyMd" tone="subdued">
                                The Page List is where all your created pages are displayed. From here, you can manage,
                                edit, and organize them with ease.
                            </Text>
                        </Box>
                    </>
                )}
                <Box paddingInlineStart="100" paddingInlineEnd="200" paddingBlockStart={!isHomePage ? '500' : '0'}>
                    <IndexFilters
                        tabs={isHomePage ? tabs : tabsTemplate}
                        sortOptions={sortOptions}
                        sortSelected={sortSelected}
                        onSort={onSort}
                        selected={tabSelected}
                        filteringAccessibilityTooltip={isHomePage ? 'Search all pages' : ''}
                        onSelect={onChangeTab}
                        queryValue={queryValue}
                        queryPlaceholder="Searching all pages"
                        onQueryChange={onQueryChange}
                        onQueryClear={onQueryClear}
                        onClearAll={onClearAll}
                        loading={false}
                        mode={mode}
                        setMode={setMode}
                        filters={!isHomePage ? filters : []}
                        appliedFilters={!isHomePage ? appliedFilters : []}
                        cancelAction={{
                            onAction: onQueryClear,
                            disabled: false,
                            loading: false,
                        }}
                        canCreateNewView={false}
                    />
                </Box>
                <Skeleton active={requestsStatus.setPageList?.status === 'loading'} count={4} />
                {requestsStatus.setPageList?.status === 'success' && (
                    <div className="page-list__resource">
                        <ResourceList
                            filterControl
                            selectable
                            flushFilters
                            headerContent="Select all"
                            resourceName={resourceName}
                            items={items}
                            renderItem={(data, id) => {
                                return (
                                    <PageItem
                                        key={id}
                                        {...data}
                                        isHomePage={isHomePage}
                                        setSelectedItems={setSelectedItems}
                                        selectMode={selectedItems && selectedItems.length > 0}
                                        __type__={ResourceItem}
                                        popoverActive={activePopoverId === data.id}
                                        setPopoverActive={(active) => setActivePopoverId(active ? data.id : null)}
                                    />
                                );
                            }}
                            idForItem={(item) =>
                                `${currentParams.status === 3 ? 'favorite-' : ''}${item.id.toString()}`
                            }
                            selectedItems={selectedItems}
                            onSelectionChange={handleSelectionChange}
                            promotedBulkActions={promotedBulkActions}
                            emptyState={
                                searchValue ? null : (
                                    <div className="page-list__resource__empty--data">
                                        <BlockStack inlineAlign="center">
                                            <img src={imgDataEmptyUrl} alt="" className="page-list__empty-image" />
                                            <BlockStack gap="150">
                                                <Text as="h3" alignment="center" variant="headingMd">
                                                    No page yet!
                                                </Text>
                                                <Text as="p" alignment="center" variant="bodySm">
                                                    Lorem ipsum dolor sit amet consectetur. Eget sit bibendum in donec{' '}
                                                </Text>
                                            </BlockStack>
                                        </BlockStack>
                                    </div>
                                )
                            }
                            emptySearchState={
                                <div className="page-list__resource__empty--search">
                                    <BlockStack inlineAlign="center" gap="600">
                                        <img src={imgSearchEmptyUrl} alt="" className="page-list__empty-image" />
                                        <BlockStack gap="150">
                                            <Text as="p" variant="headingMd" alignment="center">
                                                No results found
                                            </Text>
                                            <Text as="p" variant="bodySm" alignment="center">
                                                Lorem ipsum dolor sit amet consectetur scelerisque.
                                            </Text>
                                        </BlockStack>
                                    </BlockStack>
                                </div>
                            }
                        />
                        {items.length > 0 && (
                            <div className="page-list__resource__pagination">
                                <Pagination
                                    hasNext={page * LIMIT_PAGE < totalPages}
                                    hasPrevious={page > 1}
                                    onNext={handleNext}
                                    onPrevious={handlePrevious}
                                />
                            </div>
                        )}
                    </div>
                )}
                {requestsStatus.setPageList?.status === 'error' && (
                    <div className="page-list__error">
                        <BlockStack inlineAlign="center" gap="500">
                            <img src={imgErrorEmptyUrl} alt="" className="page-list__error-image" />
                            <BlockStack gap="150">
                                <Text as="h3" alignment="center" variant="headingMd">
                                    Something went wrong
                                </Text>
                                <Text as="p" alignment="center" variant="bodySm">
                                    An error occurred while loading the page list. Please try switching to a different
                                    tab or reload the page.
                                </Text>
                            </BlockStack>
                        </BlockStack>
                    </div>
                )}
            </Card>
            <Modal open={modalState.open} onHide={() => setModalState({ open: false, kind: 'delete' })}>
                <Box padding="400" paddingBlockEnd="800">
                    <p>{modalContent[modalState.kind].content}</p>
                </Box>
                <TitleBar title={modalContent[modalState.kind].title}>
                    <button onClick={() => setModalState({ open: false, kind: 'delete' })}>Cancel</button>
                    <button
                        variant="primary"
                        tone={modalState.kind === 'delete' ? 'critical' : 'default'}
                        onClick={() => modalContent[modalState.kind].onAction()}
                        loading={requestsStatus.setPageList?.status === 'loading' ? '' : undefined}
                    >
                        {modalContent[modalState.kind].okText}
                    </button>
                </TitleBar>
            </Modal>
        </div>
    );
};
