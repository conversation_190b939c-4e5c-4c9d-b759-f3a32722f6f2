import { FC } from 'react';
import { BlockStack, Box, Icon } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';

import { buttonDirectionOptions, buttonTypeOptions } from '@/components/builder/data/options';
import { SettingsSelect } from '@/components/builder/settings/SettingsSelect';
import { getBlockBPProperty, getBlockProperty } from '@/utils/shared';
import { SettingsInput } from '@/components/builder/settings/SettingsInput';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';
import { SettingsToggle } from '@/components/builder/settings/SettingsToggle';
import { SettingsSliderInput } from '@/components/builder/settings/SettingsSliderInput';
import { SettingsIcon } from '@/components/builder/settings/SettingsIcon';
import { SettingsColorPicker, SettingsSpace } from '@/components/builder/settings';
import './styles.scss';
interface ContentProps {
    id: string;
    isUpdateConfigs: boolean;
    selectedBlockTarget: HTMLElement;
}

export const Content: FC<ContentProps> = ({ id, isUpdateConfigs, selectedBlockTarget }) => {
    const buttonType = isUpdateConfigs
        ? getBlockProperty(`configs.content.type`, id)
        : getBlockBPProperty(`content.type`, id);
    return (
        <BaseCollapse
            label="Content"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BlockStack gap="400">
                    <SettingsSelect
                        options={buttonTypeOptions}
                        path="content.type"
                        blockId={id}
                        isUpdateConfigs={true}
                        label="Type"
                        direction="column"
                    />

                    {(buttonType === 'text-only' || buttonType === 'text-and-icon') && (
                        <SettingsInput
                            path="content.text"
                            blockId={id}
                            isUpdateConfigs={true}
                            label="Text input"
                            direction="column"
                            inputProps={{
                                type: 'text',
                                placeholder: 'Enter text',
                            }}
                        />
                    )}

                    {(buttonType === 'text-and-icon' || buttonType === 'icon-only') && (
                        <>
                            <SettingsIcon
                                path="content.icon"
                                blockId={id}
                                isUpdateConfigs
                                label="Icon"
                                direction="column"
                            />
                            <SettingsColorPicker
                                path="content.iconColor"
                                blockId={id}
                                isUpdateConfigs
                                label="Icon color"
                            />
                            <SettingsSliderInput
                                path="content.iconSize"
                                blockId={id}
                                isUpdateConfigs
                                selectedBlockTarget={selectedBlockTarget}
                                cssVariable={`--atk-button-icon-size`}
                                title="Icon size"
                                max={64}
                                min={8}
                                inputProps={{
                                    suffix: 'px',
                                    min: 8,
                                    step: 1,
                                }}
                                direction="column"
                            />
                            {buttonType === 'text-and-icon' && (
                                <>
                                    <SettingsSwitchTab
                                        options={buttonDirectionOptions}
                                        path="content.direction"
                                        blockId={id}
                                        isUpdateConfigs
                                        label="Direction"
                                    />
                                    <SettingsToggle
                                        path="content.reverse"
                                        blockId={id}
                                        isUpdateConfigs
                                        label="Reverse"
                                        toggleProps={{
                                            id: 'reverse',
                                        }}
                                    />
                                    <SettingsSliderInput
                                        path="content.spacing"
                                        blockId={id}
                                        isUpdateConfigs
                                        selectedBlockTarget={selectedBlockTarget}
                                        cssVariable={`--atk-button-gap`}
                                        title="Spacing"
                                        direction="column"
                                        max={50}
                                        min={0}
                                        step={0.5}
                                        inputProps={{
                                            suffix: 'px',
                                            step: 0.5,
                                            min: 0,
                                        }}
                                    />
                                </>
                            )}
                        </>
                    )}

                    <SettingsSpace
                        path="buttonsSpace"
                        selectedBlockTarget={selectedBlockTarget}
                        cssVariable="--atk-button-spacing"
                        blockId={id}
                        isUpdateConfig={false}
                        label="Padding"
                        isMargin={false}
                    />
                </BlockStack>
            </Box>
        </BaseCollapse>
    );
};
