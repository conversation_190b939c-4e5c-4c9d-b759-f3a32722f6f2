import { FC } from 'react';
import { FormField as FormFieldType } from '@/components/builder/blocks/form/types';
import { BaseInputFieldSettings } from '@/components/builder/blocks/form/settings/Contents/FormFieldSettings/common/BaseInputFieldSettings';

interface InputSettingsProps {
    formField: FormFieldType;
    selectedBlockTarget: HTMLElement;
}

export const InputSettings: FC<InputSettingsProps> = ({ formField, selectedBlockTarget }) => {
    return <BaseInputFieldSettings formField={formField} selectedBlockTarget={selectedBlockTarget} />;
};
