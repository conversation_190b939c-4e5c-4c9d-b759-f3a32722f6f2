import { FC } from 'react';
import { Text } from '@shopify/polaris';
import { Auto_BlockType } from '@giaminhautoketing/auto-builder';
import { BlockToolbar } from '@/components/builder/BlockToolbar';
import { accordionToolbarOptions } from '../configs';

export const AccordionPreview: FC = () => {
    return (
        <>
            {accordionToolbarOptions.map((option) => {
                return (
                    <div
                        key={option.id}
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '0.5rem',
                            alignItems: 'center',
                            flex: 1,
                        }}
                    >
                        <BlockToolbar
                            id={option.id}
                            cname={option.cname}
                            label={option.label}
                            type={option.type as Auto_BlockType}
                            configs={option.configs}
                            bpConfigs={option.bpConfigs}
                            overlay={option.overlay}
                            style={{
                                width: '100%',
                                height: '120px',
                                background: '#FAFBFB',
                                borderRadius: '8px',
                                border: '1px solid #e5e5e5',
                                cursor: 'move',
                                display: 'flex',
                                justifyContent: 'center',
                                flexDirection: 'column',
                                alignItems: 'center',
                            }}
                        ></BlockToolbar>
                        <Text variant="bodySm" as="span">
                            {option.label}
                        </Text>
                    </div>
                );
            })}
        </>
    );
};
