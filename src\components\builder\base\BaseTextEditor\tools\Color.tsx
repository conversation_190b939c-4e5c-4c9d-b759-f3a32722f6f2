import { Tooltip } from '@shopify/polaris';
import { BaseColorPickerV2 } from '@/components/builder/base';
import { useRTE } from '../context';

export function Color() {
    const { editor } = useRTE();

    const color = editor?.getAttributes('textStyle').color || '#000000';

    return (
        <BaseColorPickerV2
            color={color}
            onChange={(value) => {
                editor?.chain().setSpotlightEnabled(true).spotlightSelection().run();
                editor?.chain().setColor(value).run();
            }}
            popoverOptions={{
                offsetOptions: {
                    mainAxis: 102,
                    crossAxis: -5,
                },
            }}
            triggerRender={
                <div>
                    <Tooltip content="Color">
                        <button
                            className="Polaris-Button rte-tool"
                            style={{ paddingTop: '2px' }}
                            data-polaris-tooltip-activator
                        >
                            <svg width="20px" height="20px" viewBox="0 0 20 20" focusable="false" aria-hidden="true">
                                <path
                                    fillRule="evenodd"
                                    d="M10 3a.75.75 0 0 1 .686.447l4.2 9.5a.75.75 0 1 1-1.372.606l-1.35-3.053h-4.328l-1.35 3.053a.75.75 0 0 1-1.372-.606l4.2-9.5a.75.75 0 0 1 .686-.447Zm-1.5 6h3l-1.5-3.395-1.5 3.395Z"
                                ></path>
                                <path
                                    d="M3.075 16.25a.75.75 0 0 1 .75-.75h12.35a.75.75 0 0 1 0 1.5h-12.35a.75.75 0 0 1-.75-.75Z"
                                    fill={color}
                                ></path>
                            </svg>
                        </button>
                    </Tooltip>
                </div>
            }
        />
    );
}
