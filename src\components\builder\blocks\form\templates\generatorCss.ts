/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    createTypographyTransformer,
    generateBackground,
    generateBaseCss,
    generateBorder,
    generateBoxShadowV2,
    generateSpacingCSS,
    generateTypography,
    generateUnitValue,
    getResponsiveValue,
} from '@/stores/appStore/cssSystem';
import type {
    Generator,
    Background,
    Border,
    Typography,
    Spacing,
    UnitValue,
    SettingsShadow,
} from '@/stores/appStore/cssSystem';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { formFieldGenerators } from '@/components/builder/blocks/form/templates/generatorCssFormField';

export const formCssGenerators: Generator[] = [
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            return {
                ...generateBaseCss(blockData, breakpoint),
            };
        },
    },
    {
        selector: (blockId) => `[data-auto-id="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const { displayOnDesktop, displayOnMobile } = blockData.configs;
            const display = breakpoint === 'desktop' ? displayOnDesktop : displayOnMobile;
            return {
                display: display ? 'block' : 'none',
            };
        },
        applyTo: 'publish',
    },
    /* === Form Inner === */
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const background = getResponsiveValue<Background>(blockData, 'backgroundForm', breakpoint);
            const border = getResponsiveValue<Border>(blockData, 'formBorder', breakpoint);
            const boxShadow = getResponsiveValue<SettingsShadow>(blockData, 'boxShadow', breakpoint);
            return {
                ...generateBackground(background),
                ...generateBorder(border),
                ...generateBoxShadowV2(boxShadow),
                pointerEvents: 'none',
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const spacing = getResponsiveValue<Spacing>(blockData, 'generalForm.space.padding', breakpoint);
            return {
                ...generateSpacingCSS(spacing, 'padding', 'form-general-space'),
            };
        },
        applyTo: 'editor',
    },
    {
        selector: (blockId) => `[data-auto-id-inner="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const spacing = getResponsiveValue<Spacing>(blockData, 'generalForm.space.padding', breakpoint);
            return {
                ...generateSpacingCSS(spacing, 'padding'),
            };
        },
        applyTo: 'publish',
    },
    /* === Form Title === */
    {
        selector: (blockId) => `[data-atk-form-title="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const padding = getResponsiveValue<Spacing>(blockData, 'titleForm.titleSpacing.padding', breakpoint);
            const margin = getResponsiveValue<Spacing>(blockData, 'titleForm.titleSpacing.margin', breakpoint);
            const typography = getResponsiveValue<Typography>(blockData, 'titleForm', breakpoint);
            return {
                ...generateSpacingCSS(padding, 'padding', 'form-title-spacing'),
                ...generateSpacingCSS(margin, 'margin', 'form-title-spacing'),
                ...generateTypography(typography, 'form-title-line-height', 'form-title-letter-spacing'),
            };
        },
        applyTo: 'editor',
    },
    {
        selector: (blockId) => `[data-atk-form-title="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const padding = getResponsiveValue<Spacing>(blockData, 'titleForm.titleSpacing.padding', breakpoint);
            const margin = getResponsiveValue<Spacing>(blockData, 'titleForm.titleSpacing.margin', breakpoint);
            const typography = getResponsiveValue<Typography>(blockData, 'titleForm', breakpoint);
            return {
                ...generateSpacingCSS(padding, 'padding'),
                ...generateSpacingCSS(margin, 'margin'),
                ...generateTypography(typography),
            };
        },
        applyTo: 'publish',
    },
    /* === Form Field Wrapper === */
    {
        selector: (blockId) => `[data-atk-form-field-wrap="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const vertical = getResponsiveValue<UnitValue>(blockData, 'generalForm.vertical', breakpoint);
            const horizontal = getResponsiveValue<UnitValue>(blockData, 'generalForm.horizontal', breakpoint);
            const align = getResponsiveValue<string>(blockData, 'generalForm.align', breakpoint);
            return {
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: align,
                margin: `calc(var(--form-vertical, ${generateUnitValue(
                    vertical,
                )})/2) calc(var(--form-horizontal, ${generateUnitValue(horizontal)})/2*(-1))`,
            };
        },
        applyTo: 'editor',
    },
    {
        selector: (blockId) => `[data-atk-form-field-wrap="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const vertical = getResponsiveValue<UnitValue>(blockData, 'generalForm.vertical', breakpoint);
            const horizontal = getResponsiveValue<UnitValue>(blockData, 'generalForm.horizontal', breakpoint);
            const align = getResponsiveValue<string>(blockData, 'generalForm.align', breakpoint);
            return {
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: align,
                margin: `calc(${generateUnitValue(vertical)}/2) calc(${generateUnitValue(horizontal)}/2*(-1))`,
                boxSizing: 'border-box',
            };
        },
        applyTo: 'publish',
    },

    /* === Form Field === */
    {
        selector: (blockId) => `[data-atk-form-field="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const rowGap = getResponsiveValue<UnitValue>(blockData, 'generalForm.spaceTitleField', breakpoint);
            return {
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                alignSelf: 'end',
                rowGap: generateUnitValue(rowGap),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-atk-form-field="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const vertical = getResponsiveValue<UnitValue>(blockData, 'generalForm.vertical', breakpoint);
            const horizontal = getResponsiveValue<UnitValue>(blockData, 'generalForm.horizontal', breakpoint);
            return {
                padding: `calc(var(--form-vertical, ${generateUnitValue(
                    vertical,
                )})/2) calc(var(--form-horizontal, ${generateUnitValue(horizontal)})/2)`,
            };
        },
        applyTo: 'editor',
    },
    {
        selector: (blockId) => `[data-atk-form-field="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const vertical = getResponsiveValue<UnitValue>(blockData, 'generalForm.vertical', breakpoint);
            const horizontal = getResponsiveValue<UnitValue>(blockData, 'generalForm.horizontal', breakpoint);
            return {
                padding: `calc(${generateUnitValue(vertical)}/2) calc(${generateUnitValue(horizontal)}/2)`,
            };
        },
        applyTo: 'publish',
    },
    {
        selector: (blockId) => `[data-atk-form-input="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const inputStyles = getResponsiveValue<Record<string, Typography>>(blockData, 'fieldsInput', breakpoint);
            const inputTypography = createTypographyTransformer('input');
            const inputTypographyData = inputTypography(inputStyles);
            return {
                ...generateTypography(inputTypographyData),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) =>
            `[data-atk-form-field-label="${blockId}"], [data-atk-form-checkbox-label="${blockId}"], [data-atk-form-radio-label="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const labelStyles = getResponsiveValue<Record<string, Typography>>(blockData, 'fieldsLabel', breakpoint);
            const labelTypography = createTypographyTransformer('label');
            const labelTypographyData = labelTypography(labelStyles);
            return {
                ...generateTypography(labelTypographyData),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-atk-form-field="${blockId}"] input::placeholder`,
        generator: (blockData, breakpoint) => {
            const placeholderStyles = getResponsiveValue<Record<string, Typography>>(
                blockData,
                'fieldsPlaceholder',
                breakpoint,
            );
            const placeholderTypography = createTypographyTransformer('placeholder');
            const placeholderTypographyData = placeholderTypography(placeholderStyles);
            return {
                ...generateTypography(placeholderTypographyData),
            };
        },
        applyTo: 'all',
    },

    /* === Form Button  === */
    {
        selector: (blockId) => `[data-atk-form-submit-wrap="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const padding = getResponsiveValue<UnitValue>(blockData, 'arrangement.spaceTitleField', breakpoint);
            const align = getResponsiveValue<string>(blockData, 'arrangement.buttonAlign', breakpoint);

            return {
                display: 'flex',
                justifyContent: align,
                alignItems: 'center',
                paddingTop: generateUnitValue(padding),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-atk-form-submit="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const background = getResponsiveValue<Background>(blockData, 'buttonSubmit.background', breakpoint);
            const border = getResponsiveValue<Border>(blockData, 'buttonSubmit.buttonBorder', breakpoint);
            const boxShadow = getResponsiveValue<SettingsShadow>(blockData, 'buttonSubmit.boxShadow', breakpoint);
            return {
                ...generateBackground(background),
                ...generateBorder(border),
                ...generateBoxShadowV2(boxShadow),
            };
        },
        applyTo: 'all',
    },
    {
        selector: (blockId) => `[data-atk-form-submit="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const padding = getResponsiveValue<Spacing>(blockData, 'buttonSubmit.buttonSpacing.padding', breakpoint);
            const margin = getResponsiveValue<Spacing>(blockData, 'buttonSubmit.buttonSpacing.margin', breakpoint);
            const buttonSubmit = getResponsiveValue<any>(blockData, 'buttonSubmit', breakpoint);
            const getLetterSpacing = (spacing: { type: string; value: UnitValue }) => {
                const spacingMap = {
                    auto: { val: 'normal', unit: '' },
                    narrow: { val: '-0.05', unit: 'em' },
                    wide: { val: '0.2', unit: 'em' },
                    custom: spacing.value,
                    default: undefined,
                };
                return spacingMap[spacing.type as keyof typeof spacingMap] || undefined;
            };
            const getLineHeight = (spacing: { type: string; value: UnitValue }) => {
                const heightMap = {
                    '1': { val: 'normal', unit: '' },
                    '1.5': { val: '1.5', unit: '' },
                    '2': { val: '2', unit: '' },
                    custom: spacing.value,
                    default: undefined,
                };
                return heightMap[spacing.type as keyof typeof heightMap] || undefined;
            };
            const typography: Typography = {
                fontSize: buttonSubmit.fontSize,
                fontWeight: buttonSubmit.fontWeight,
                fontFamily: buttonSubmit.fontFamily,
                color: buttonSubmit.color,
                textTransform: buttonSubmit.textTransform,
                textDecoration: buttonSubmit.textDecoration,
                textAlign: buttonSubmit.textAlign,
                fontStyle: buttonSubmit.fontStyle,
                letterSpacing: getLetterSpacing(buttonSubmit.buttonLetterSpacing),
                lineHeight: getLineHeight(buttonSubmit.buttonLineSpacing),
                textShadow: buttonSubmit.textShadow,
            };
            const width = getResponsiveValue<UnitValue>(blockData, 'buttonSubmit.buttonWidth', breakpoint);
            const height = getResponsiveValue<UnitValue>(blockData, 'buttonSubmit.buttonHeight', breakpoint);
            return {
                ...generateSpacingCSS(padding, 'padding', 'form-button-spacing'),
                ...generateSpacingCSS(margin, 'margin', 'form-button-spacing'),
                ...generateTypography(typography, 'form-button-line-height', 'form-button-letter-spacing'),
                width: `var(--form-button-width, ${generateUnitValue(width)})`,
                height: `var(--form-button-height, ${generateUnitValue(height)})`,
            };
        },
        applyTo: 'editor',
    },
    {
        selector: (blockId) => `[data-atk-form-submit="${blockId}"]`,
        generator: (blockData, breakpoint) => {
            const padding = getResponsiveValue<Spacing>(blockData, 'buttonSubmit.buttonSpacing.padding', breakpoint);
            const margin = getResponsiveValue<Spacing>(blockData, 'buttonSubmit.buttonSpacing.margin', breakpoint);
            const buttonSubmit = getResponsiveValue<any>(blockData, 'buttonSubmit', breakpoint);
            const getLetterSpacing = (spacing: { type: string; value: UnitValue }) => {
                const spacingMap = {
                    auto: { val: 'normal', unit: '' },
                    narrow: { val: '-0.05', unit: 'em' },
                    wide: { val: '0.2', unit: 'em' },
                    custom: spacing.value,
                    default: undefined,
                };
                return spacingMap[spacing.type as keyof typeof spacingMap] || undefined;
            };
            const getLineHeight = (spacing: { type: string; value: UnitValue }) => {
                const heightMap = {
                    '1': { val: 'normal', unit: '' },
                    '1.5': { val: '1.5', unit: '' },
                    '2': { val: '2', unit: '' },
                    custom: spacing.value,
                    default: undefined,
                };
                return heightMap[spacing.type as keyof typeof heightMap] || undefined;
            };
            const typography: Typography = {
                fontSize: buttonSubmit.fontSize,
                fontWeight: buttonSubmit.fontWeight,
                fontFamily: buttonSubmit.fontFamily,
                color: buttonSubmit.color,
                textTransform: buttonSubmit.textTransform,
                textDecoration: buttonSubmit.textDecoration,
                textAlign: buttonSubmit.textAlign,
                fontStyle: buttonSubmit.fontStyle,
                letterSpacing: getLetterSpacing(buttonSubmit.buttonLetterSpacing),
                lineHeight: getLineHeight(buttonSubmit.buttonLineSpacing),
                textShadow: buttonSubmit.textShadow,
            };
            const width = getResponsiveValue<UnitValue>(blockData, 'buttonSubmit.buttonWidth', breakpoint);
            const height = getResponsiveValue<UnitValue>(blockData, 'buttonSubmit.buttonHeight', breakpoint);
            return {
                ...generateSpacingCSS(padding, 'padding'),
                ...generateSpacingCSS(margin, 'margin'),
                ...generateTypography(typography),
                width: generateUnitValue(width),
                height: generateUnitValue(height),
            };
        },
        applyTo: 'publish',
    },
];

export const getFormCssGenerators = (blockData: Auto_BlockData): Generator[] => {
    return [...formCssGenerators, ...formFieldGenerators(blockData)];
};
