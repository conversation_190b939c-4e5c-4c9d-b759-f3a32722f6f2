import { FC } from 'react';
import { Box } from '@shopify/polaris';
import { BaseCollapse } from '@/components/builder/base/BaseCollapse';
import { Icon } from '@shopify/polaris';
import { BaseItemLayout } from '@/components/builder/base/BaseItemLayout';
import { BlockStack } from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon } from '@shopify/polaris-icons';
import { SettingsBackgroundColor } from '@/components/builder/settings/SettingsBackgroundColor';
interface StylesProps {
    id: string;
}

export const Styles: FC<StylesProps> = ({ id }) => {
    return (
        <BaseCollapse
            label="Styles"
            labelContent={(open) => <Icon source={open ? ChevronDownIcon : ChevronRightIcon} />}
        >
            <Box paddingBlockStart="300">
                <BaseItemLayout textProps={{ as: 'p', variant: 'bodyMd', children: '' }} direction="column">
                    <BlockStack gap="400">
                        <SettingsBackgroundColor
                            blockId={id}
                            path="backgroundBreadcrumb"
                            label="Background"
                            isUpdateConfigs={false}
                        />
                    </BlockStack>
                </BaseItemLayout>
            </Box>
        </BaseCollapse>
    );
};
