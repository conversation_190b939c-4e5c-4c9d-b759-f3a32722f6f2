import { FC } from 'react';
import { Card, BlockStack, Text, Box, InlineStack, Badge, Button, Link } from '@shopify/polaris';

import { PlanStatus } from '../../types';
interface AccountStatusCardProps {
    shopifyDomain: string;
    accountStatus: PlanStatus;
    accountPlanName: string;
}

const handleChangePlan = () => {
    console.log('change plan');
};

export const AccountStatusCard: FC<AccountStatusCardProps> = ({ shopifyDomain, accountStatus, accountPlanName }) => {
    return (
        <Card padding="0">
            <Box paddingBlock="500" paddingInline="400">
                <BlockStack gap="400">
                    <Text as="h2" variant="headingSm">
                        Account status
                    </Text>
                    <BlockStack gap="500">
                        <BlockStack gap="100">
                            <Text as="p" variant="bodyMd">
                                Shopify Domain
                            </Text>
                            <Box
                                paddingBlock="150"
                                paddingInline="300"
                                borderRadius="200"
                                borderWidth="0165"
                                borderColor="input-border"
                            >
                                <InlineStack>
                                    <Link url={`https://${shopifyDomain}`} target="_blank">
                                        <Text as="p" variant="bodyMd">
                                            {shopifyDomain}
                                        </Text>
                                    </Link>
                                </InlineStack>
                            </Box>
                        </BlockStack>
                        <Box>
                            <BlockStack gap="100">
                                <Text as="p" variant="bodyMd">
                                    Shopify account status
                                </Text>
                                <Box padding="300" borderRadius="200" borderWidth="0165" borderColor="input-border">
                                    <InlineStack gap="300" align="space-between">
                                        <InlineStack gap="300">
                                            <Text as="p" variant="bodyMd">
                                                {accountPlanName}
                                            </Text>
                                            <Badge tone={accountStatus === 'pro' ? 'success' : 'enabled'}>
                                                {accountStatus}
                                            </Badge>
                                        </InlineStack>
                                        <Button variant="plain" onClick={handleChangePlan}>
                                            Change plan
                                        </Button>
                                    </InlineStack>
                                </Box>
                            </BlockStack>
                        </Box>
                    </BlockStack>
                </BlockStack>
            </Box>
        </Card>
    );
};
