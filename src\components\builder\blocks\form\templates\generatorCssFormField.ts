import { Spacing, UnitValue, Background, Border, generateBorder } from '@/stores/appStore/cssSystem';
import {
    getResponsiveValue,
    generateUnitValue,
    generateSpacingCSS,
    generateBackground,
} from '@/stores/appStore/cssSystem';
import { Breakpoint, Generator } from '@/stores/appStore/cssSystem/core/types';
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import { FormField } from '@/components/builder/blocks/form/types';

export const formFieldGenerators = (blockData: Auto_BlockData): Generator[] => {
    const fields = (blockData.configs?.form || []) as FormField[];

    return fields
        .filter((f) => f.id)
        .flatMap((field, index) => [
            ...generateWrapperGenerator(field),
            ...generateSeparatorGenerator(field, index),
            ...(field.type !== 'checkbox' && field.type !== 'radio' && field.type !== 'free-text'
                ? generateInputGenerator(field)
                : []),
            ...(field.type === 'checkbox' ? generateCheckboxGenerator(field, index) : []),
            ...(field.type === 'radio' ? generateRadioGenerator(field, index) : []),
        ]);
};

export function generateSeparatorGenerator(field: FormField, index: number): Generator[] {
    return [
        {
            selector: (blockId) => `[data-atk-form-separator-line-${index}="${blockId}"]`,
            generator: () => {
                return {
                    display: field.separateLine ? 'inline-block' : 'none',
                    width: field.separateLine ? '100%' : '0%',
                };
            },
        },
    ];
}

export function generateWrapperGenerator(field: FormField): Generator[] {
    return [
        {
            selector: (blockId) => `[data-atk-form-field="${blockId}"]:has([data-atk-form-field-id="${field.id}"])`,
            generator: (blockData, breakpoint: Breakpoint) => {
                const fieldSizes = getResponsiveValue<Record<string, { fieldWidth: UnitValue }>>(
                    blockData,
                    'fieldSizes',
                    breakpoint,
                );
                const fieldSize = fieldSizes?.[field.key];
                return {
                    maxWidth: `var(--form-${field.key}-width , ${generateUnitValue(fieldSize?.fieldWidth)})`,
                };
            },
            applyTo: 'editor',
        },
        {
            selector: (blockId) => `[data-atk-form-field="${blockId}"]:has([data-atk-form-field-id="${field.id}"])`,
            generator: (blockData, breakpoint: Breakpoint) => {
                const fieldSizes = getResponsiveValue<Record<string, { fieldWidth: UnitValue }>>(
                    blockData,
                    'fieldSizes',
                    breakpoint,
                );
                const fieldSize = fieldSizes?.[field.key];
                return {
                    maxWidth: generateUnitValue(fieldSize?.fieldWidth),
                };
            },
            applyTo: 'publish',
        },
    ];
}

export function generateInputGenerator(field: FormField): Generator[] {
    return [
        {
            selector: (blockId) => `[data-atk-form-field="${blockId}"] [data-atk-form-field-id="${field.id}"]`,
            generator: (blockData, breakpoint: Breakpoint) => {
                //prettier-ignore
                const fieldSizes = getResponsiveValue<Record<string, { fieldHeight: UnitValue }>>(blockData, 'fieldSizes', breakpoint);
                const fieldSize = fieldSizes?.[field.key];
                const padding = getResponsiveValue<Spacing>(blockData, 'fieldsSpacing.padding', breakpoint);
                const background = getResponsiveValue<Background>(blockData, 'fieldsStyles.background', breakpoint);
                const border = getResponsiveValue<Border>(blockData, 'fieldsStyles.border', breakpoint);
                return {
                    minHeight: `var(--form-${field.key}-height, ${generateUnitValue(fieldSize?.fieldHeight)})`,
                    ...generateSpacingCSS(padding, 'padding', 'form-fields-spacing'),
                    ...generateBackground(background),
                    ...generateBorder(border),
                };
            },
            applyTo: 'editor',
        },
        {
            selector: (blockId) => `[data-atk-form-field="${blockId}"] [data-atk-form-field-id="${field.id}"]`,
            generator: (blockData, breakpoint: Breakpoint) => {
                const fieldSizes = getResponsiveValue<Record<string, { fieldHeight: UnitValue }>>(
                    blockData,
                    'fieldSizes',
                    breakpoint,
                );
                const fieldSize = fieldSizes?.[field.key];
                const padding = getResponsiveValue<Spacing>(blockData, 'fieldsSpacing.padding', breakpoint);
                const background = getResponsiveValue<Background>(blockData, 'fieldsStyles.background', breakpoint);
                const border = getResponsiveValue<Border>(blockData, 'fieldsStyles.border', breakpoint);

                return {
                    minHeight: generateUnitValue(fieldSize?.fieldHeight),
                    ...generateSpacingCSS(padding, 'padding'),
                    ...generateBackground(background),
                    ...generateBorder(border),
                };
            },
            applyTo: 'publish',
        },
    ];
}

export function generateCheckboxGenerator(field: FormField, index: number): Generator[] {
    const checkboxMultiElement: Generator = {
        selector: (blockId) => `[data-atk-form-checkbox-multi-element-${index}="${blockId}"]`,
        generator: (blockData: Auto_BlockData) => {
            const formFields = (blockData.configs?.form || []) as FormField[];
            //prettier-ignore
            const currentField = formFields.find((f) => f.key === field.key) as { direction?: 'row' | 'column' }
            const direction = currentField?.direction;
            return {
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'start',
                columnGap: '12px',
                rowGap: '6px',
                flexDirection: direction,
            };
        },
        applyTo: 'all',
    };
    const checkboxElement: Generator = {
        selector: (blockId) => `[data-atk-form-checkbox-element="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                flexDirection: 'column',
                rowGap: '6px',
            };
        },
    };
    const checkboxElementWrap: Generator = {
        selector: (blockId) => `[data-atk-form-checkbox-element-wrap="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                alignItems: 'center',
                rowGap: '4px',
            };
        },
    };
    const checkboxElementInner: Generator = {
        selector: (blockId) => `[data-atk-form-checkbox-element-inner="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            };
        },
    };
    // Checkbox input: editor dùng var, publish không dùng var
    const checkboxInputEditor: Generator = {
        selector: (blockId) => `[data-atk-form-field="${blockId}"] input[type="checkbox"]`,
        generator: (blockData, breakpoint: Breakpoint) => {
            const fieldSizes = getResponsiveValue<Record<string, { checkboxSize: UnitValue }>>(
                blockData,
                'fieldSizes',
                breakpoint,
            )[field.key];
            return field.type === 'checkbox'
                ? {
                      width: `var(--form-${field.key}-width , ${generateUnitValue(fieldSizes?.checkboxSize)})`,
                      height: `var(--form-${field.key}-height , ${generateUnitValue(fieldSizes?.checkboxSize)})`,
                  }
                : {};
        },
        applyTo: 'editor',
    };
    const checkboxInputPublish: Generator = {
        selector: (blockId) => `[data-atk-form-field="${blockId}"] input[type="checkbox"]`,
        generator: (blockData, breakpoint: Breakpoint) => {
            const fieldSizes = getResponsiveValue<Record<string, { checkboxSize: UnitValue }>>(
                blockData,
                'fieldSizes',
                breakpoint,
            )[field.key];
            return field.type === 'checkbox'
                ? {
                      width: generateUnitValue(fieldSizes?.checkboxSize),
                      height: generateUnitValue(fieldSizes?.checkboxSize),
                  }
                : {};
        },
        applyTo: 'publish',
    };
    return [
        checkboxMultiElement,
        checkboxElement,
        checkboxInputEditor,
        checkboxInputPublish,
        checkboxElementWrap,
        checkboxElementInner,
    ];
}

export function generateRadioGenerator(field: FormField, index: number): Generator[] {
    const radioMultiElement: Generator = {
        selector: (blockId) => `[data-atk-form-radio-multi-element-${index}="${blockId}"]`,
        generator: (blockData: Auto_BlockData) => {
            const formFields = (blockData.configs?.form || []) as FormField[];
            //prettier-ignore
            const currentField = formFields.find((f) => f.key === field.key) as { direction?: 'row' | 'column' }
            const direction = currentField?.direction;
            return {
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'start',
                columnGap: '12px',
                rowGap: '6px',
                flexDirection: direction,
            };
        },
    };
    const radioElement: Generator = {
        selector: (blockId) => `[data-atk-form-radio-element        ="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                flexDirection: 'column',
                rowGap: '6px',
            };
        },
    };
    const radioElementWrap: Generator = {
        selector: (blockId) => `[data-atk-form-radio-element-wrap="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
            };
        },
    };
    const radioElementInner: Generator = {
        selector: (blockId) => `[data-atk-form-radio-element-inner="${blockId}"]`,
        generator: () => {
            return {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            };
        },
    };
    const radioInputEditor: Generator = {
        selector: (blockId) => `[data-atk-form-field="${blockId}"] input[type="radio"]`,
        generator: (blockData, breakpoint: Breakpoint) => {
            const fieldSizes = getResponsiveValue<Record<string, { radioSize: UnitValue }>>(
                blockData,
                'fieldSizes',
                breakpoint,
            )[field.key];
            return {
                width: `var(--form-${field.key}-width , ${generateUnitValue(fieldSizes?.radioSize)})`,
                height: `var(--form-${field.key}-height , ${generateUnitValue(fieldSizes?.radioSize)})`,
                margin: '0',
            };
        },
        applyTo: 'editor',
    };
    const radioInputPublish: Generator = {
        selector: (blockId) => `[data-atk-form-field="${blockId}"] input[type="radio"]`,
        generator: (blockData, breakpoint: Breakpoint) => {
            //prettier-ignore
            const fieldSizes = getResponsiveValue<Record<string, { radioSize: UnitValue }>>(blockData, 'fieldSizes', breakpoint)[field.key];
            return {
                width: generateUnitValue(fieldSizes?.radioSize),
                height: generateUnitValue(fieldSizes?.radioSize),
                margin: '0 !important',
            };
        },
        applyTo: 'publish',
    };
    return [radioMultiElement, radioElement, radioInputEditor, radioInputPublish, radioElementWrap, radioElementInner];
}
