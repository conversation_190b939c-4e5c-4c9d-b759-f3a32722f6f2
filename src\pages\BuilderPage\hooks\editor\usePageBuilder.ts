import { useEffect, useState, useCallback } from 'react';
import { useAppStore } from '@/stores/appStore';
import {
    useBlockStore,
    useBuilderStore,
    applyCSSVariablesToElementToolbar,
    Auto_BlockData,
} from '@giaminhautoketing/auto-builder';
import { exampleToolbarOptions } from '@/pages/BuilderPage/exampleConfigs';
import { generateTemplates } from '@/pages/BuilderPage/utils/index';
import { PageJson } from '@/pages/BuilderPage/types/page';
import { blockStructure } from '@/components/builder/data/structure';
import { loadScripts } from '@/utils/jsBlockGenerator';
import { CssSystem } from '@/stores/appStore/cssSystem/core/cssSystem';

export const usePageBuilder = () => {
    const blocks = useBlockStore((s) => s.blocks);
    const hierarchy = useBlockStore((s) => s.hierarchy);
    const currentDevice = useBuilderStore((s) => s.currentDevice);
    const page = useAppStore((s) => s.page);
    const isAuthenticated = useAppStore((s) => s.isAuthenticated);
    const templateTypeId = useAppStore((s) => s.templateTypeId);
    const pageCreationStatus = useAppStore((s) => s.pageCreationStatus);
    const currentParams = useAppStore((s) => s.currentParams);
    const pageId = useAppStore((s) => s.pageId);
    const setPageId = useAppStore((s) => s.setPageId);

    const [mainContentWidth, setMainContentWidth] = useState('1140px');

    const { setBlocks, setHierarchy } = useBlockStore.getState();

    const {
        getDetailPage,
        updatePageJson,
        createPage,
        getCombinedCss,
        generateCssFromBlocks,
        setTemplateType,
        setHasEdits,
        setPageCreationStatus,
        updateRequestStatus,
        updatePageStatus,
    } = useAppStore.getState();

    // === Helpers ===

    const syncPageJsonToStore = useCallback(() => {
        if (!page || pageCreationStatus === 'starting') return;
        const jsonBlocks = page.jsonData?.blocks ?? {};
        const jsonHierarchy = page.jsonData?.hierarchy ?? {};
        if (!Object.keys(blocks || {}).length) setBlocks(jsonBlocks);
        if (!Object.keys(hierarchy || {}).length) setHierarchy(jsonHierarchy);
    }, [page, blocks, hierarchy, pageCreationStatus, setBlocks, setHierarchy]);

    const initializeNewPage = useCallback(() => {
        if (pageCreationStatus !== 'starting') return;

        if (!Object.keys(blocks || {}).length) setBlocks(blockStructure.blocks);
        if (!Object.keys(hierarchy || {}).length) setHierarchy(blockStructure.hierarchy);
    }, [pageCreationStatus, blocks, hierarchy, setBlocks, setHierarchy]);

    const checkForEdits = useCallback(() => {
        if (!page?.jsonData || !blocks || !hierarchy) return;

        const changed =
            JSON.stringify(page.jsonData.blocks) !== JSON.stringify(blocks) ||
            JSON.stringify(page.jsonData.hierarchy) !== JSON.stringify(hierarchy);
        setHasEdits(changed);
    }, [page, blocks, hierarchy, setHasEdits]);

    const generatePageData = useCallback(
        async (blocks: Record<string, Auto_BlockData>, hierarchy: Record<string, string[]>) => {
            await generateCssFromBlocks(blocks);
            const cssContent = getCombinedCss();
            const cssSystem = CssSystem.getInstance().cssPublishString;
            const jsContent = await loadScripts(Object.values(blocks).map((b) => b.type));
            const templates = generateTemplates();
            const render = createPageRenderer(blocks, hierarchy, templates);
            const liquidData = await render('page', true);
            const htmlData = await render('page', false);
            const updatedJsonData = JSON.stringify({ blocks, hierarchy });

            return { updatedJsonData, cssContent, jsContent, liquidData, htmlData, cssSystem };
        },
        [getCombinedCss, generateCssFromBlocks],
    );

    // === Handlers ===

    const handleSave = useCallback(
        async (page: PageJson) => {
            try {
                shopify.toast.show('Saving...');
                updateRequestStatus('updatePageJson', 'loading');
                const data = await generatePageData(blocks, hierarchy);
                await updatePageJson(
                    page.id.toString(),
                    JSON.stringify({
                        jsonData: data.updatedJsonData,
                        liquidData: data.liquidData,
                        htmlData: data.htmlData,
                        cssData: data.cssSystem,
                        jsData: data.jsContent,
                    }),
                );
                setHasEdits(false);
                shopify.toast.show('Page saved');
                updateRequestStatus('updatePageJson', 'success');
            } catch (err) {
                console.error(err);
                shopify.toast.show('Error saving page', { isError: true });
            }
        },
        [blocks, hierarchy, generatePageData, setHasEdits, updatePageJson, updateRequestStatus],
    );

    const handleCreate = useCallback(
        async (title: string) => {
            try {
                shopify.toast.show('Creating...');
                const data = await generatePageData(blocks, hierarchy);
                const res = await createPage(
                    JSON.stringify({
                        templateTypeId,
                        isPublished: 0,
                        title,
                        jsonData: data.updatedJsonData,
                        liquidData: data.liquidData,
                        htmlData: data.htmlData,
                        cssData: data.cssSystem,
                        jsData: data.jsContent,
                    }),
                );
                setPageId(Number(res.id));
                shopify.toast.show('Page created successfully');
                shopify.modal.hide('builder-modal-create');
                setPageCreationStatus('pending');
            } catch (err) {
                console.error(err);
                shopify.toast.show('Error creating page', { isError: true });
            }
        },
        [blocks, hierarchy, generatePageData, templateTypeId, createPage, setPageCreationStatus, setPageId],
    );

    const handlePublish = useCallback(async () => {
        try {
            shopify.toast.show('Publishing...');
            await updatePageStatus(Number(pageId), 'published', currentParams);
            updateRequestStatus('publishPage', 'success');
            shopify.toast.show('Page published successfully');
        } catch {
            shopify.toast.show('Error publishing page', { isError: true });
        }
    }, [pageId, currentParams, updatePageStatus, updateRequestStatus]);

    const handleDiscardChanges = useCallback(() => {
        if (!page?.jsonData) return;
        setBlocks(page.jsonData.blocks || {});
        setHierarchy(page.jsonData.hierarchy || {});
        setHasEdits(false);
    }, [page, setBlocks, setHierarchy, setHasEdits]);

    // === Effects ===

    useEffect(() => {
        setMainContentWidth(currentDevice === 'desktop' ? '1140px' : '480px');
    }, [currentDevice]);

    useEffect(() => {
        if (isAuthenticated) {
            const templateId = localStorage.getItem('templateId');
            if (templateId) {
                setTemplateType();

                getDetailPage(templateId);
            }
        }
    }, [isAuthenticated, setTemplateType, getDetailPage]);

    useEffect(() => {
        syncPageJsonToStore();
    }, [syncPageJsonToStore]);

    useEffect(() => {
        initializeNewPage();
    }, [pageCreationStatus, initializeNewPage]);

    useEffect(() => {
        checkForEdits();
    }, [checkForEdits]);

    useEffect(() => {
        applyCSSVariablesToElementToolbar(exampleToolbarOptions);
    }, []);

    // Modal communication
    useEffect(() => {
        const modalChannel = new BroadcastChannel('builder-modal');
        modalChannel.addEventListener('message', (e: MessageEvent) => {
            if (e.data === 'Save' && page) handleSave(page as PageJson);
            else if (e.data?.action === 'Create') handleCreate(e.data.title);
            else if (e.data === 'Publish') handlePublish();
            else if (e.data === 'Discard') handleDiscardChanges();
        });
        return () => {
            modalChannel.close();
        };
    }, [handleSave, handleCreate, handlePublish, handleDiscardChanges, page]);

    return {
        blocks,
        hierarchy,
        apiStatus: useAppStore((s) => s.apiStatus),
        mainContentWidth,
        setBlocks,
        setHierarchy,
        handleSave,
        handleCreate,
    };
};

const createPageRenderer = (
    blocks: Record<string, Auto_BlockData>,
    hierarchy: Record<string, string[]>,
    templates: ReturnType<typeof generateTemplates>,
) => {
    const renderPage = async (id: string, isLiquid: boolean): Promise<string> => {
        const block = blocks[id];
        if (!block) return '';
        const templateFn = block.type && templates[block.type as keyof typeof templates];
        if (!templateFn) return '';

        const children = (await Promise.all((hierarchy[id] || []).map((cid) => renderPage(cid, isLiquid)))).join('');
        return templateFn(id, block, isLiquid, children);
    };
    return renderPage;
};
