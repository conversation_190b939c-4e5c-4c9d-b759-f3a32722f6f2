/* eslint-disable @typescript-eslint/no-explicit-any */
import { Auto_BlockData } from '@giaminhautoketing/auto-builder';
import {
    generateBackground,
    generateBorder,
} from '@/components/builder/data/utils/CssGenerator/utils';
import { getBlockClassName } from '@/utils/cssBlockGenerator';
import {
    isValidCssValue,
    isValidUnitValue,
    formatCss,
} from '@/utils/cssValidator';

export const generateCss = (blockId: string, block: Auto_BlockData, device: 'desktop' | 'mobile'): string => {
    if (!block.bpConfigs?.[device]) return '';
    const bpConfig = block.bpConfigs[device];
    const config = block.configs;
    
    const className = getBlockClassName('shape');

    const outer = `.${className}-wrapper[data-auto-id="${blockId}"]`;
    const inner = `${outer} .atk-shape[data-auto-id-inner="${blockId}"]`;
    
    let outerCss = '';

    // Grid & layout
    if (isValidCssValue(bpConfig.gridColStart)) {
        outerCss += `grid-column-start: ${bpConfig.gridColStart};\n    `;
    }
    if (isValidCssValue(bpConfig.gridColEnd)) {
        outerCss += `grid-column-end: ${bpConfig.gridColEnd};\n    `;
    }
    if (isValidCssValue(bpConfig.gridRowStart)) {
        outerCss += `grid-row-start: ${bpConfig.gridRowStart};\n    `;
    }
    if (isValidCssValue(bpConfig.gridRowEnd)) {
        outerCss += `grid-row-end: ${bpConfig.gridRowEnd};\n    `;
    }
    if (isValidUnitValue(bpConfig.mt)) {
        outerCss += `margin-top: ${bpConfig.mt.val}${bpConfig.mt.unit || 'px'};\n    `;
    }
    if (isValidUnitValue(bpConfig.ml)) {
        outerCss += `margin-left: ${bpConfig.ml.val}${bpConfig.ml.unit || 'px'};\n    `;
    }
    if (isValidCssValue(bpConfig.justifySelf)) {
        outerCss += `justify-self: ${bpConfig.justifySelf};\n    `;
    }
    if (bpConfig?.alignSelf && bpConfig.alignSelf !== 'none') {
        outerCss += `align-self: ${bpConfig.alignSelf};\n    `;
    }
        if (isValidUnitValue(bpConfig.width)) {
            outerCss += `width: ${bpConfig.width.val}${bpConfig.width.unit || 'px'};\n    `;
    }
    if (isValidUnitValue(bpConfig.height)) {
        outerCss += `height: ${bpConfig.height.val}${bpConfig.height.unit || 'px'};\n    `;
    }
    outerCss += 'display: block;\n    ';

    let css = `${outer} {\n    ${outerCss}}`;
    // Media queries
    if (device === 'desktop' && config.displayOnDesktop === false) {
        css += `\n@media (min-width: 769px) {\n    ${outer} {\n        display: none;\n    }\n}`;
    }
    if (device === 'mobile' && config.displayOnMobile === false) {
        css += `\n@media (max-width: 768px) {\n    ${outer} {\n        display: none;\n    }\n}`;
    }

    let innerCss = '';
    // Background & border cho shape
    if (bpConfig.background) {
        innerCss += `${generateBackground(bpConfig.background)}\n    `;
        
    }
    if (bpConfig.border) {
        innerCss += `${generateBorder(bpConfig.border)}\n    `;
    }
    // Nếu là svg mask thì thêm style mask
    const content = config.content as any;
    if (content?.type === 'svg' && content?.svg) {
        innerCss += `mask-image: url('data:image/svg+xml,${encodeURIComponent(content.svg)}');\n    `;
        innerCss += `-webkit-mask-image: url('data:image/svg+xml,${encodeURIComponent(content.svg)}');\n    `;
        innerCss += `mask-size: 100% 100%;\n    `;
        innerCss += `-webkit-mask-size: 100% 100%;\n    `;
        }
    css += `\n\n${inner} {\n    ${innerCss}}`;
    
    return formatCss(css);
};