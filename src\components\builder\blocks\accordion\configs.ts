import { genRandomBlockId } from '@giaminhautoketing/auto-builder';

export const accordionToolbarOptions = [
    {
        id: genRandomBlockId(),
        cname: 'accordion',
        label: 'Accordion',
        type: 'accordion',
        configs: {
            content: {
                accordionHeader: {
                    background: {
                        type: 'color',
                        color: '#FFFFFF',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                    text: {
                        fontFamily: 'Inter',
                        fontSize: { val: '16', unit: 'px' },
                        fontWeight: '400',
                        color: '#000000',
                        letterSpacing: { type: 'custom', payload: { val: '0', unit: 'px' } },
                        lineHeight: { type: 'custom', payload: { val: '24', unit: 'px' } },
                        fontStyle: 'normal',
                        textTransform: 'default',
                        textDecoration: 'default',
                    },
                    icon: {
                        typeIcon: 'arrow',
                        size: { val: '24', unit: 'px' },
                        pos: 'right',
                        color: '#333333',
                    },
                    horizontalPadding: { val: '10', unit: 'px' },
                    verticalPadding: { val: '10', unit: 'px' },
                },
                defaultAccordion: 'first-items-opened',
                allowMultipleOpen: true,
                applyTransition: {
                    effect: true,
                    duration: { val: '0.3', unit: 's' },
                },
                accordionCollapse: {
                    background: {
                        type: 'color',
                        color: '#FFFFFF',
                        image: {
                            url: '',
                            repeat: 'no',
                            position: 'center center',
                            attachment: 'fixed',
                            fill: 'cover',
                        },
                    },
                },
            },
            displayOnDesktop: true,
            displayOnMobile: true,
            animation: {
                type: 'none',
                duration: { val: '0', unit: 's' },
                loop: '1',
                delay: { val: '0', unit: 's' },
            },
            syncOnDesktop: false,
            syncOnMobile: false,
            customCSS: {
                classNames: '',
                style: '',
            },
            gap: { val: '0', unit: 'px' },
            events: {},
            blockData: [
                {
                    id: genRandomBlockId(),
                    title: 'Accordion Item 1',
                    content: 'Content 1',
                },
                {
                    id: genRandomBlockId(),
                    title: 'Accordion Item 2',
                    content: 'Content 2',
                },
                {
                    id: genRandomBlockId(),
                    title: 'Accordion Item 3',
                    content: 'Content 3',
                },
                {
                    id: genRandomBlockId(),
                    title: 'Accordion Item 3',
                    content: 'Content 3',
                },
            ],
        },
        bpConfigs: {
            desktop: {
                width: { val: '350', unit: 'px' },
                border: {
                    radius: {
                        'top-left': { val: '0', unit: 'px' },
                        'top-right': { val: '0', unit: 'px' },
                        'bottom-right': { val: '0', unit: 'px' },
                        'bottom-left': { val: '0', unit: 'px' },
                    },
                    color: '#000000',
                    top: { val: '1', unit: 'px' },
                    right: { val: '1', unit: 'px' },
                    bottom: { val: '1', unit: 'px' },
                    left: { val: '1', unit: 'px' },
                    type: 'solid',
                },
                background: {
                    type: 'color',
                    color: '#FFFFFF',
                    image: {
                        url: '',
                        repeat: 'no',
                        position: 'center center',
                        attachment: 'fixed',
                        fill: 'cover',
                    },
                },
            },
            mobile: {
                width: { val: '300', unit: 'px' },
            },
        },
        overlay: {
            desktop: {
                width: '350',
                height: '300',
            },
            mobile: {
                width: '300',
                height: '200',
            },
        },
    },
];
