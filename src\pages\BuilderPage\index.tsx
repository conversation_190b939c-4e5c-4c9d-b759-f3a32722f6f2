import { FC, memo } from 'react';
import { AppProvider } from '@shopify/polaris';
import { Header } from './components/Header';
import { MainContent } from './components/MainContent';
import { MediaManager } from './components/MediaManager';
import './_styles.scss';

const BuilderPage: FC = memo(() => {
    return (
        <AppProvider i18n={{}}>
            <div style={{ position: 'relative' }} className="builder-container">
                <MediaManager />
                <Header />
                <MainContent />
            </div>
        </AppProvider>
    );
});

export default BuilderPage;
