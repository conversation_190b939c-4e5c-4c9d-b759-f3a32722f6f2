import { FC } from 'react';
import { FormOutlined } from './components/FormOutlined';
import { FormUnderlined } from './components/FormUnderlined';
import { FormSettingsConfigs } from '@/components/builder/blocks/form/configs';
import { css } from '@emotion/react';
export const FormPreview: FC = () => {
    return (
        <div
            css={css`
                display: flex;
                flex-direction: column;
                gap: 10px;
            `}
        >
            {FormSettingsConfigs.map((option) => {
                const formLabel = option.label;
                return (
                    <div key={option.id}>
                        {formLabel === 'Form Outlined' && <FormOutlined option={option} />}
                        {formLabel === 'Form Underlined' && <FormUnderlined option={option} />}
                    </div>
                );
            })}
        </div>
    );
};
