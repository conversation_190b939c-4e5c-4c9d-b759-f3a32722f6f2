import { forwardRef, HTMLAttributes } from 'react';
import { mergeProps } from '@zag-js/react';
import { useColorPickerContext } from './use-color-picker-context.ts';

export type ColorPickerAreaProps = HTMLAttributes<HTMLDivElement>;

export const ColorPickerArea = forwardRef<HTMLDivElement, ColorPickerAreaProps>((props, ref) => {
    const colorPicker = useColorPickerContext();
    const mergedProps = mergeProps(colorPicker.getAreaProps(), props);

    return (
        <div {...mergedProps} ref={ref}>
            <div {...colorPicker.getAreaBackgroundProps()} />
            <div {...colorPicker.getAreaThumbProps()} />
        </div>
    );
});

ColorPickerArea.displayName = 'ColorPickerArea';
