import { FC } from 'react';
import { BlockStack, Box, Button, Card, ChoiceList, Filters, InlineStack, Page, Popover } from '@shopify/polaris';
import { ChevronDownIcon } from '@shopify/polaris-icons';
import { css } from '@emotion/react';
import { Pagination, SortButton } from '@/components';
import { useAppStore } from '@/stores';
import { useTemplates } from '@/hooks';
import { UseCaseFilter } from './UseCaseFilter';
import { TemplateList, TemplateCard } from './TemplateList';

export const TemplatesPage: FC = () => {
    const {
        isLoading,
        limit,
        page,
        pageType,
        industry,
        queryValue,
        sortSelected,
        templates,
        totalTemplates,
        industryChoices,
        pageTypeChoices,
        sortChoices,
        selectedIndustryName,
        isIndustryPopoverOpen,
        setPage,
        setQueryValue,
        setIndustry,
        setSortSelected,
        setPageType,
        clearIndustrySelection,
        setIsIndustryPopoverOpen,
    } = useTemplates();

    const setTemplatePreview = useAppStore((state) => state.setTemplatePreview);

    return (
        <Page title="Templates">
            <div css={{ paddingBottom: '64px' }}>
                <Card padding="0">
                    <Box padding="400" paddingBlockStart="200" paddingBlockEnd="800">
                        <div
                            css={css`
                                display: flex;
                                flex-direction: column;
                                gap: 22px;
                                .Polaris-Filters__Container {
                                    margin-inline: -8px;
                                    border-bottom: none;
                                    .Polaris-Button--iconWithText {
                                        padding-inline-start: 12px;
                                        padding-inline-end: 8px;
                                        flex-direction: row-reverse;
                                    }
                                    .Polaris-InlineStack {
                                        gap: 8px;
                                    }
                                    .Polaris-TextField__Backdrop {
                                        border: none;
                                        box-shadow: 0px 1px 0px 0px #e3e3e3 inset, 1px 0px 0px 0px #e3e3e3 inset,
                                            -1px 0px 0px 0px #e3e3e3 inset, 0px -1px 0px 0px #b5b5b5 inset;
                                    }
                                }
                                .Polaris-TextField__Input {
                                    font-size: var(--p-font-size-325);
                                    line-height: var(--p-font-line-height-500);
                                }
                            `}
                        >
                            <Filters
                                queryPlaceholder="Search template"
                                filters={[]}
                                queryValue={queryValue}
                                onQueryChange={setQueryValue}
                                onQueryClear={() => setQueryValue('')}
                                onClearAll={() => {}}
                            >
                                <Popover
                                    active={isIndustryPopoverOpen}
                                    activator={
                                        <Button
                                            icon={ChevronDownIcon}
                                            onClick={() => setIsIndustryPopoverOpen(!isIndustryPopoverOpen)}
                                        >
                                            {selectedIndustryName}
                                        </Button>
                                    }
                                    onClose={() => setIsIndustryPopoverOpen(false)}
                                    preferredAlignment="right"
                                >
                                    <div
                                        css={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '4px',
                                            padding: '8px 16px',
                                        }}
                                    >
                                        <ChoiceList
                                            choices={industryChoices}
                                            title="Industry"
                                            titleHidden
                                            selected={industry}
                                            onChange={(value) => {
                                                setIndustry(value);
                                                setIsIndustryPopoverOpen(false);
                                                setPage(1);
                                            }}
                                        />
                                        <div>
                                            <Button
                                                variant="plain"
                                                disabled={industry.length === 0}
                                                onClick={clearIndustrySelection}
                                            >
                                                Clear
                                            </Button>
                                        </div>
                                    </div>
                                </Popover>
                                <SortButton choices={sortChoices} selected={sortSelected} onChange={setSortSelected} />
                            </Filters>
                            <UseCaseFilter
                                options={pageTypeChoices}
                                value={pageType[0]}
                                onChange={(val) => {
                                    setPageType([val]);
                                    setPage(1);
                                }}
                            />
                        </div>
                    </Box>
                    <Box paddingInline="400" paddingBlockEnd="1000">
                        <BlockStack gap="800">
                            <TemplateList
                                data={templates}
                                isLoading={isLoading}
                                emptyTitle="No template found"
                                emptyDescription="Lorem ipsum dolor sit amet consectetur scelerisque."
                                renderItem={(data, index) => (
                                    <TemplateCard
                                        key={index}
                                        data={data}
                                        onPreview={() => {
                                            setTemplatePreview({
                                                name: data.title,
                                                url: 'https://seal-commerce-asia.myshopify.com/pages/socks-monthly-subscription',
                                            });
                                            shopify.modal.show('modal-preview-template');
                                        }}
                                    />
                                )}
                            />
                            {!isLoading && templates && templates.length > 0 && (
                                <InlineStack align="center">
                                    <Pagination
                                        value={page}
                                        onChange={setPage}
                                        total={Math.ceil((totalTemplates || 1) / limit)}
                                        size="large"
                                    />
                                </InlineStack>
                            )}
                        </BlockStack>
                    </Box>
                </Card>
            </div>
        </Page>
    );
};
