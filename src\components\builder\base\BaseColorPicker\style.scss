[data-scope='color-picker'][data-part='trigger'] {
    display: flex;
    border: none;
    background-color: transparent;
    cursor: pointer;
}

[data-scope='color-picker'][data-part='transparency-grid'] {
    --size: 16px !important;
    border-radius: 8px;
}

[data-scope='color-picker'][data-part='positioner'] {
    --z-index: 999 !important;
}

[data-scope='color-picker'][data-part='content']:not([data-content-background='true']) {
    isolation: isolate;
    padding-inline: 1rem;
    padding-block-end: 1rem;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 4px 6px -2px rgba(26, 26, 26, 0.2), 0 1px 0 0 rgba(204, 204, 204, 0.5), 0 -1px 0 0 rgba(0, 0, 0, 0.17),
        -1px 0 0 0 rgba(0, 0, 0, 0.13), 1px 0 0 0 rgba(0, 0, 0, 0.13);
}

[data-scope='color-picker'][data-part='content'][data-content-background='true'] {
    isolation: unset;
    padding-inline: unset;
    padding-block-end: unset;
    border-radius: unset;
    background-color: unset;
    box-shadow: unset;
}

[data-scope='color-picker'][data-part='area'] {
    width: 235px;
    height: 165px;
    border-radius: 8px;
    border-width: 1px;
    cursor: crosshair;
}

[data-scope='color-picker'][data-part='area-background'] {
    height: inherit;
    border-radius: inherit;
}

[data-scope='color-picker'][data-part='area-thumb'],
[data-scope='color-picker'][data-part='channel-slider-thumb'] {
    width: 16px;
    height: 16px;
    border: 3px solid white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    outline: none;
}
[data-scope='color-picker'][data-part='channel-slider-thumb'] {
    cursor: pointer;
}

[data-scope='color-picker'][data-part='channel-slider'] {
    height: 1rem;
}

[data-scope='color-picker'][data-part='channel-slider-track'] {
    height: 100%;
    border-radius: 8px;
}

[data-scope='color-picker'][data-part='channel-input'] {
    width: 37px;
    height: 32px;
    font-family: var(--p-font-family-sans);
    font-size: 12px;
    font-weight: 450;
    text-align: center;
    outline: none;
    border: none;
    padding-block: 6px;
    border-right: 1px solid #ebebeb;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
        appearance: none;
    }
}
[data-scope='color-picker'][data-part='channel-input'][data-channel='hex'] {
    flex-grow: 1;
    padding-inline: 12px;
    text-align: start;
}
[data-scope='color-picker'][data-part='channel-input'][data-channel='alpha'] {
    width: 48px;
    border-right: none;
}
