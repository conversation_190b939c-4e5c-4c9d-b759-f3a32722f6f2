import {
    Badge,
    BlockStack,
    Box,
    Button,
    Collapsible,
    Icon,
    IconSource,
    InlineStack,
    Scrollable,
    Text,
    TextField,
} from '@shopify/polaris';
import { ChevronDownIcon, ChevronRightIcon, SearchIcon } from '@shopify/polaris-icons';
import { FC, useState } from 'react';
import { ConfigTabs } from '@/pages/BuilderPage/configs/tabs';
import { useSideBar } from '@/pages/BuilderPage/hooks/useSideBar';
import { type CategoryProps, type TabItemProps } from '@/pages/BuilderPage/types/tabs';

interface ElementsProps {
    onItemClick?: (item: TabItemProps) => void;
    selectedItem?: TabItemProps;
}

export const Elements: FC<ElementsProps> = ({ onItemClick, selectedItem }) => {
    const { drawerContentState, updateDrawerContentState } = useSideBar();
    const { activeTab, searchTerm, isSearchOpen } = drawerContentState.elements || {
        activeTab: ConfigTabs[0].id,
        searchTerm: '',
        isSearchOpen: false,
    };

    const [openCategories, setOpenCategories] = useState<Set<string>>(
        new Set(ConfigTabs.flatMap((tab) => tab.categories.map((cat) => cat.id))),
    );

    const currentTab = ConfigTabs.find((tab) => tab.id === activeTab);

    const filteredCategories =
        currentTab?.categories
            .map((category) => ({
                ...category,
                items: searchTerm
                    ? category.items.filter((item) =>
                          (typeof item.title === 'string' ? item.title : '')
                              .toLowerCase()
                              .includes(searchTerm.toLowerCase()),
                      )
                    : category.items,
            }))
            .filter((category) => category.items.length > 0) || [];

    const toggleCategory = (id: string) => {
        setOpenCategories((prev) => {
            const next = new Set(prev);
            if (next.has(id)) next.delete(id);
            else next.add(id);
            return next;
        });
    };

    const updateTab = (tabId: string) => {
        updateDrawerContentState('elements', { activeTab: tabId });
    };

    const toggleSearch = () => {
        updateDrawerContentState('elements', {
            isSearchOpen: !isSearchOpen,
            searchTerm: '',
        });
    };

    const updateSearch = (value: string) => {
        updateDrawerContentState('elements', { searchTerm: value });
    };

    const renderItem = (item: TabItemProps) => (
        <Box paddingInline="400" paddingBlock="200">
            <InlineStack align="start" blockAlign="start" gap="200">
                {typeof item.icon === 'string' ? (
                    <img src={item.icon} alt={typeof item.title === 'string' ? item.title : ''} />
                ) : (
                    <InlineStack>
                        <Icon source={item.icon as IconSource} />
                    </InlineStack>
                )}
                <Text as="p">{item.title}</Text>
            </InlineStack>
        </Box>
    );

    const renderCategory = (category: CategoryProps) => (
        <div key={category.id}>
            <div className="elements-scrollable__category" onClick={() => toggleCategory(category.id)}>
                <Box>
                    <InlineStack>
                        <InlineStack>
                            <Icon source={openCategories.has(category.id) ? ChevronDownIcon : ChevronRightIcon} />
                        </InlineStack>
                        <Text as="p" variant="bodyMd" fontWeight="medium">
                            {category.name}
                        </Text>
                    </InlineStack>
                </Box>
            </div>
            <Collapsible open={openCategories.has(category.id)} id={category.id}>
                <div className="elements-scrollable__items">
                    {category.items.map((item) => (
                        <div
                            key={item.id}
                            className={`elements-scrollable__items__item ${
                                selectedItem?.id === item.id ? 'active' : ''
                            }`}
                            onClick={() => onItemClick?.(item)}
                        >
                            {renderItem(item)}
                        </div>
                    ))}
                </div>
            </Collapsible>
        </div>
    );

    return (
        <>
            <Box
                paddingInline="200"
                paddingBlock="300"
                borderColor="border"
                borderStyle="solid"
                borderBlockEndWidth="025"
                minHeight="52px"
            >
                {!isSearchOpen ? (
                    <div className="element-tab__container">
                        {ConfigTabs.map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => updateTab(tab.id)}
                                className={`element-tab ${activeTab === tab.id ? 'active' : ''}`}
                            >
                                <InlineStack align="center" blockAlign="center" gap="050">
                                    <Text as="p" fontWeight={activeTab === tab.id ? 'medium' : 'regular'} tone="base">
                                        {tab.name}
                                    </Text>
                                    <Badge size="small">
                                        {tab.categories
                                            .reduce((total, category) => total + category.items.length, 0)
                                            .toString()}
                                    </Badge>
                                </InlineStack>
                            </button>
                        ))}
                        <Button icon={SearchIcon} variant="tertiary" onClick={toggleSearch} />
                    </div>
                ) : (
                    <div className="search-container show" style={{ height: '28px' }}>
                        <TextField
                            label=""
                            value={searchTerm}
                            onChange={updateSearch}
                            autoComplete="off"
                            placeholder={currentTab?.searchPlaceholder}
                            connectedRight={
                                <Button variant="plain" onClick={toggleSearch}>
                                    Cancel
                                </Button>
                            }
                        />
                    </div>
                )}
            </Box>
            <div className="elements-scrollable">
                <Scrollable style={{ position: 'absolute', inset: 0, height: 'calc(100% - 52px)' }}>
                    <Box paddingInline="200" paddingBlock="300">
                        <BlockStack gap="200">
                            {filteredCategories.length === 0 ? (
                                <BlockStack align="center" inlineAlign="center" gap="200">
                                    <Icon source={SearchIcon} />
                                    <Text as="p" variant="headingMd" tone="subdued">
                                        No elements found
                                    </Text>
                                    <Text as="p" variant="bodyMd" tone="subdued">
                                        Try changing the search term
                                    </Text>
                                </BlockStack>
                            ) : (
                                filteredCategories.map(renderCategory)
                            )}
                        </BlockStack>
                    </Box>
                </Scrollable>
            </div>
        </>
    );
};
