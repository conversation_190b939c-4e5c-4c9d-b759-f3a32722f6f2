import { FC } from 'react';
import { useBuilderStore } from '@giaminhautoketing/auto-builder';
import { InlineStack } from '@shopify/polaris';
import { BaseHasBorderLayout } from '@/components/builder/base/BaseHasBorderLayout';
import { alignVerticalOptions } from '@/components/builder/data/options';
import { BaseCollapse } from '@/components/builder/base';
import { SettingsSwitchTab } from '@/components/builder/settings/SettingsSwitchTab';
import { alignHorizontalOptions } from '@/components/builder/data/options';
import { DisplayOn } from './components/DisplayOn';
import { Size } from './components/Size';

const DEFAULT_PATH_DISPLAY_ON = 'displayOn';

export const Advanced: FC = () => {
    const selectedBlockId = useBuilderStore((state) => state.selectedBlockId) as string;

    return (
        <>
            <BaseHasBorderLayout>
                <BaseCollapse label="Align" containerClassName="form-align-settings">
                    <InlineStack blockAlign="center" align="space-between">
                        <SettingsSwitchTab
                            options={alignHorizontalOptions}
                            blockId={selectedBlockId}
                            path="alignment.alignSelf"
                            direction="column"
                            isUpdateConfigs={false}
                            switchTabProps={{
                                fullWidth: true,
                            }}
                        />
                        <SettingsSwitchTab
                            options={alignVerticalOptions}
                            blockId={selectedBlockId}
                            path="alignment.justifySelf"
                            direction="column"
                            isUpdateConfigs={false}
                            switchTabProps={{
                                fullWidth: true,
                            }}
                        />
                    </InlineStack>
                </BaseCollapse>
                <Size blockId={selectedBlockId || ''} isUpdateConfigs={false} />
                <DisplayOn
                    blockId={selectedBlockId || ''}
                    path={DEFAULT_PATH_DISPLAY_ON}
                    label="Display on"
                    isUpdateConfigs
                />
            </BaseHasBorderLayout>
        </>
    );
};
